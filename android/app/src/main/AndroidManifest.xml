
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">
  <application
      android:allowBackup="false"
      android:icon="@mipmap/ic_launcher_round"
      android:label="@string/app_name"
      android:largeHeap="true"
      android:networkSecurityConfig="@xml/network_security_config"
      android:theme="@style/AppTheme">
    <meta-data
        android:name="android.content.APP_RESTRICTIONS"
        android:resource="@xml/app_restrictions" />
    <activity
        tools:replace="android:exported"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:label="@string/app_name"
        android:launchMode="singleTask"
        android:theme="@style/AppTheme"
        android:name=".MainActivity"
        android:exported="true"
        android:resizeableActivity="true"
        android:supportsPictureInPicture="true"
        android:windowSoftInputMode="adjustResize">
      <meta-data android:name="firebase_crashlytics_collection_enabled" android:value="true" />
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.BROWSABLE" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:host="meet.groupvi.systems" android:scheme="https" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.BROWSABLE" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:scheme="meet.groupvi.systems" />
      </intent-filter>
    </activity>
  </application>
</manifest>
