/**
 * Mixins that mimic the way Atlaskit fills the screen with modals at low screen widths.
 */
@mixin full-size-modal-positioner() {
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    max-width: 100%;
    width: 100%;
}

@mixin full-size-modal-dialog() {
    height: 100%;
    max-height: 100%;
    border-radius: 0;
}

/**
 * Move the @atlaskit/flag container up a little bit so it does not cover the
 * toolbar with the first notification.
 */
.atlaskit-portal > #notifications-container {
    bottom: calc(#{$newToolbarSizeWithPadding}) !important;
}

.modal-dialog-form {
    /**
     * Override @atlaskit/dropdown-menu styling when in a modal because the
     * dropdown backgrounds clash with the modal backgrounds.
     */
    .dropdown-menu div[style*="transform"] {
        outline: 1px solid #455166;
    }
}

/**
 * Override @atlaskit/modal-dialog header styling
 */
.atlaskit-portal [role="dialog"] header {
    .jitsi-icon {
        cursor: pointer;
    }

    .jitsi-icon svg {
        fill: #B8C7E0;
    }
}

/**
 * Make header close button more easily tappable on mobile.
 */
.mobile-browser .atlaskit-portal [role="dialog"] header .jitsi-icon {
    display: grid;
    place-items: center;
    height: 48px;
    width: 48px;
    background: #2a3a4b;
    border-radius: 3px;
}

/**
 * Override @atlaskit/theme styling for the top toolbar so it displays over
 * the video thumbnail while obscuring as little as possible.
 */
.videocontainer__toptoolbar > div > div {
    background: none !important;
}


/**
 * Keep overflow menu within screen vertical bounds and make it scrollable.
 */
 .toolbox-button-wth-dialog > div:nth-child(2) {
    max-height: calc(100vh - #{$newToolbarSizeWithPadding} - 46px);
    margin-bottom: 4px;
    overflow-y: auto;
}

.audio-preview > div:nth-child(2),
.video-preview > div:nth-child(2) {
    margin-bottom: 4px;
    outline: none;
    padding: 0;
}

/**
 * The following selectors keep the chat modal full-size anywhere between 100px
 * and 580px for desktop or 680px for mobile.
 */
@media (min-width: 100px) and (max-width: 320px) {
    .smiley-input {
        display: none;
    }
    .shift-right .focus-lock > div > div {
        @include full-size-modal-positioner();
    }

    .shift-right .focus-lock [role="dialog"] {
        @include full-size-modal-dialog();
    }
}

@media (min-width: 480px) and (max-width: 580px) {
    .shift-right .focus-lock > div > div {
        @include full-size-modal-positioner();
    }

    .shift-right .focus-lock [role="dialog"] {
        @include full-size-modal-dialog();
    }
}

@media (min-width: 580px) and (max-width: 680px) {
    .mobile-browser {
        &.shift-right .focus-lock > div > div {
            @include full-size-modal-positioner();
        }

        &.shift-right .focus-lock [role="dialog"] {
            @include full-size-modal-dialog();
        }
    }
}

div.Tooltip {
    color: #fff;
    font-size: 12px;
    line-height: 14px;
    padding: 8px;
}

.css-1ek22hd {
    box-shadow: none !important;
}


.css-1oc7v0j {
    width: 900px !important;
    height: auto !important;

    @media (min-width: 300px) and (max-width: 680px) {
        height: 100% !important;
    }
}

/**
 * Move the @atlaskit/flag container up a little bit so it does not cover the
 * toolbar with the first notification.
 */
 .jIMojv{
    bottom: calc(#{$newToolbarSizeWithPadding}) !important;
}

/**
 * Disable the slide-in animation for @atlaskit/flag due to the animation
 * repeating for each queued flag once it becomes the top flag.
 */
 .mIBKA:first-child {
    animation: cbfRuT 0s !important;
    -webkit-animation: cbfRuT 0s !important;
}

.modal-dialog-form {
    /**
     * Update the @atlaskit/dropdown-menu trigger wrapper to make sure it looks
     * click-able.
     */
    .cjJUnw {
        cursor: pointer;
    }

    /**
     * Override @atlaskit/dropdown-menu styling when in a modal because the
     * dropdown backgrounds clash with the modal backgrounds.
     */
    .cksvax[data-role=droplistContent] {
        border: 1px solid #455166;
    }
}

/**
 * Override @atlaskit/theme styling for the top toolbar so it displays over
 * the video thumbnail while obscuring as little as possible.
 */
.videocontainer .tOoji {
    background: none;
}

/**
 * Override @atlaskit/InlineDialog styling for the overflowmenu so it displays
 * with the correct height.
 */
.toolbox-button-wth-dialog .eYJELv {
    max-height: initial;
}

.YymJg{
    width: 900px !important;
    height: auto !important;
}

.css-1aqfjd7 {
    background-color: #25323E !important;

}

.css-1l2f42w-ButtonBase:active, .css-1l2f42w-ButtonBase:hover, .css-ea4b39-ButtonBase:hover, .jzqbjL.jzqbjL, .css-1xv1yut-ButtonBase, .iwHKVI, .css-1kp36sk-ButtonBase, .css-1kp36sk-ButtonBase:active {
    background-color: #0D1424 !important;
}

.itZGfB {
    // color: #B8C7E0 !important;
}

.css-13hbh4u{
    .css-zc8nma{
        color: rgb(24 24 24) !important;
    }
}


.css-ea4b39-ButtonBase, .css-v3jnws-ButtonBase,.css-lhdkzp, .css-9w9q1l {
    background: #333666!important;
    height: 3.285714em !important;
    width: 93px !important;
    padding: 8px 10px !important;
    border-radius: 3px !important;
    .css-19r5em7{
        color: #fff !important;
    }
    color: #fff !important;
}

.css-1l2f42w-ButtonBase, .css-1l2f42w-ButtonBase,.css-ws8iq8 {
    background: #880000 !important;
    height: 3.285714em !important;
    width: 93px !important;
    padding: 8px 10px !important;
    border-radius: 3px !important;


    .css-19r5em7{
        color: #fff !important;
    }
    color: #EBEBEB !important;
}

.wQDaF{
   background: none  !important;
}

// .css-1jesbqk{
//     width: 100% !important;
// }


.bVdRQG{
    background-color: $menuBG !important;
    max-height: 440px !important;
    overflow-y: scroll;
}

.dtLwjJ{
    background-color: $menuBG !important;
    max-height: 550px !important;
}

.file-upload-label12 {
    cursor: pointer;
}

.css-krjp51 {
        width: 27%!important;
        height: 248px;
        top: 191px !important;

        @media (max-width: 767px) {
            width: 100%!important;
            height: 100%;
            top: 0 !important;
        }
}

.css-1aqfjd7 {
    background-color: #f0f0f0 !important;

    @media (min-width: 300px) and (max-width: 680px) {
        height: 91% !important;
    }
}

.prejoin-preview-dropdown-container {
    .bVdRQG {
        border-radius: 30px;
        color: #fff;
    }
}

.css-vjdnif{
    color: #000 !important;
}

.itZGfB{
color: #000 !important;
    }

.jsYMHu{
    height: 37px !important;
    width: 98% !important;
    background-color: #ffff !important;
    border-color: #f0f0f0 !important;
    border-radius: 7px !important;
}

.GVpqe {
    background: #ffff !important;
    border: 0;
    color: #000 !important;
}

.modal-dialog-donor {
    .dvocAg {
        background-color: #fff !important;
    }
}


.ejwajD, .cLnhlo{
    background: none !important;
}

.dEUJGG{
    background: none !important;
    border-color: none !important;
    border: none !important;
}

.css-1aqfjd7{
    color : rgb(60 60 60) !important;
}

.embed-meeting-dialog-header3{
    color: rgb(92 92 92) !important;
}

.css-lhdkzp {
    color : rgb(255 255 255) !important;
    width: auto !important;
    min-width: 100px;
}

.css-116m23a{
   background-color:  rgb(13, 20, 36) !important;
}

.dvocAg{
    border: none !important;
    background: none !important;
}

.exHNhk {
    border: none !important;
    background: none !important;
}


ins.adsbygoogle[data-ad-status="unfilled"] {
    background-image: url('../images/adremovalbanner.jpg');
    width: 80vw !important;
    background-repeat: no-repeat;
}

.hr-line {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #ccc;
    margin: 1em 0;
    padding: 0;
}

.css-1dhnqu0-label {
    color: rgb(0 0 0);
}

.css-z25nd1 {
    button {
        height: auto;
        align-items: "anchor-center";
        background-color: #333666;
        &:hover{
            background: #880000 !important;
        }
     span {
        color: #fff !important;
     }
    }
}