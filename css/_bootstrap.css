/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/

#header {
    height: 80px;
    transition: all 0.5s;
    z-index: 997;
    transition: all 0.5s;
    background: #333666;
}

#header.header-transparent {
    background: transparent;
}

#header.header-scrolled {
    background: #333666;
    height: 60px;
}

#header .logo h1 {
    font-size: 28px;
    margin: 0;
    padding: 0;
    line-height: 1;
    font-weight: 700;
}

#header .logo h1 a,
#header .logo h1 a:hover {
    color: #fff;
    text-decoration: none;
}

#header .logo div {
    padding: 0;
    margin: 0;
    max-height: 65px;
}

@media (max-width: 992px) {
    #header {
        height: 64px;
    }

    #header .logo h1 {
        font-size: 28px;
    }
}

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.d-flex {
    display: -ms-flexbox !important;
    display: flex !important;
}

.align-items-center {
    -ms-flex-align: center !important;
    align-items: center !important;
}



.mr-auto,
.mx-auto {
    margin-right: auto !important;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}


/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/


/* Desktop Navigation */

.nav-menu,
.nav-menu * {
    margin: 0;
    padding: 0;
    list-style: none;
}

.nav-menu>ul>li {
    position: relative;
    white-space: nowrap;
    float: left;
}

.nav-menu a {
    display: block;
    position: relative;
    /* color: rgba(255, 255, 255, 0.7) !important; */
    padding: 10px 0 10px 25px;
    transition: 0.3s;
    font-size: 15px;
    font-weight: 500;
    font-family: "Poppins", sans-serif;
}

.nav-menu>ul>li>a:before {
    content: "";
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 3px;
    left: 25px;
    background-color: #036531;
    visibility: hidden;
    width: 0px;
    transition: all 0.3s ease-in-out 0s;
}

.nav-menu a:hover:before,
.nav-menu li:hover>a:before,
.nav-menu .active>a:before {
    visibility: visible;
    width: 25px;
}

.nav-menu a:hover,
.nav-menu .active>a,
.nav-menu li:hover>a {
    color: #fff;
    text-decoration: none;
}

.nav-menu .drop-down ul {
    display: block;
    position: absolute;
    left: 25px;
    top: calc(100% - 30px);
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    padding: 10px 0;
    background: #fff;
    box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
    transition: ease all 0.3s;
}

.nav-menu .drop-down:hover>ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
    border-radius: 5px;
}

.nav-menu .drop-down li {
    min-width: 180px;
    position: relative;
}

.nav-menu .drop-down ul a {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    text-transform: none;
    color: #5E82B4;
}

.nav-menu .drop-down ul a:hover,
.nav-menu .drop-down ul .active>a,
.nav-menu .drop-down ul li:hover>a {
    color: #036531;
}

.nav-menu .drop-down>a:after {
    content: "\ea99";
    font-family: IcoFont;
    padding-left: 5px;
}

.nav-menu .drop-down .drop-down ul {
    top: 0;
    left: calc(100% - 30px);
}

.nav-menu .drop-down .drop-down:hover>ul {
    opacity: 1;
    top: 0;
    left: 100%;
}

.nav-menu .drop-down .drop-down>a {
    padding-right: 35px;
}

.nav-menu .drop-down .drop-down>a:after {
    content: "\eaa0";
    font-family: IcoFont;
    position: absolute;
    right: 15px;
}

@media (max-width: 1366px) {
    .nav-menu .drop-down .drop-down ul {
        left: -90%;
    }

    .nav-menu .drop-down .drop-down:hover>ul {
        left: -100%;
    }

    .nav-menu .drop-down .drop-down>a:after {
        content: "\ea9d";
    }
}

.d-none {
    display: none !important;
}


/* Mobile Navigation */

.mobile-nav-toggle {
    position: fixed;
    right: 15px;
    top: 15px;
    z-index: 9998;
    border: 0;
    background: none;
    font-size: 24px;
    transition: all 0.4s;
    outline: none !important;
    line-height: 1;
    cursor: pointer;
    text-align: right;
}

.mobile-nav-toggle i {
    color: #fff;
}

.mobile-nav {
    position: fixed;
    top: 55px;
    right: 15px;
    bottom: 15px;
    left: 15px;
    z-index: 9999;
    overflow-y: auto;
    background: #fff;
    transition: ease-in-out 0.2s;
    opacity: 0;
    visibility: hidden;
    border-radius: 10px;
    padding: 10px 0;
}

.mobile-nav * {
    margin: 0;
    padding: 0;
    list-style: none;
}

.mobile-nav a {
    display: block;
    position: relative;
    color: #5E82B4;
    padding: 10px 20px;
    font-weight: 500;
    outline: none;
}

.mobile-nav a:hover,
.mobile-nav .active>a,
.mobile-nav li:hover>a {
    color: #036531;
    text-decoration: none;
}

.mobile-nav .drop-down>a:after {
    content: "\ea99";
    font-family: IcoFont;
    padding-left: 10px;
    position: absolute;
    right: 15px;
}

.mobile-nav .active.drop-down>a:after {
    content: "\eaa1";
}

.mobile-nav .drop-down>a {
    padding-right: 35px;
}

.mobile-nav .drop-down ul {
    display: none;
    overflow: hidden;
}

.mobile-nav .drop-down li {
    padding-left: 20px;
}

.mobile-nav-overly {
    width: 100%;
    height: 100%;
    z-index: 9997;
    top: 0;
    left: 0;
    position: fixed;
    overflow: hidden;
    display: none;
    transition: ease-in-out 0.2s;
    background-color: rgba(0, 0, 0, 0.7);
}

nav.mobile-nav.d-lg-none {
    height: 40% !important;
}

.mobile-nav-active {
    overflow: hidden;
}

.mobile-nav-active .mobile-nav {
    opacity: 1;
    visibility: visible;
}

.mobile-nav-active .mobile-nav-toggle i {
    color: #fff;
}

#hero {
    width: 100%;
    /* background: url("../images/conference.jpeg"); */
    position: relative;
    padding: 120px 0 0 0;
}

#hero:before {
    content: "";
    background: rgba(70, 83, 127, 0.8);
    background: rgba(95, 130, 181, 0.8);
    background: #333666cc;
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
}

/* #hero h1 {
    margin: 0 0 20px 0;
    font-size: 33px;
    font-weight: 700;
    line-height: 50px;
    color: rgba(255, 255, 255, 0.8);
} */

#hero h1 span {
    color: #fff;
    border-bottom: 4px solid #036531;
}

#hero h2 {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
    font-size: 24px;
}

#hero .btn-get-started {
    font-family: "Montserrat", sans-serif;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 1px;
    display: inline-block;
    padding: 10px 30px;
    border-radius: 50px;
    transition: 0.5s;
    color: #fff;
    background: #036531;
}

#hero .btn-get-started:hover {
    background: #17b57d;
}

#hero .animated {
    animation: up-down 2s ease-in-out infinite alternate-reverse both;
}

@media (min-width: 1024px) {
    #hero {
        background-attachment: fixed;
        background-size: 100%;
    }
}

@media (max-width: 991px) {
    #hero {
        padding-top: 80px;
    }

    #hero .animated {
        -webkit-animation: none;
        animation: none;
    }

    #hero .hero-img {
        text-align: center;
    }

    #hero .hero-img img {
        max-width: 50%;
    }

    #hero h1 {
        font-size: 24px;
        line-height: 30px;
        margin-bottom: 0px;
    }

    .pt-5,
    .py-5 {
        padding: 1rem !important
    }

    .form-div {
        width: 98% !important
    }

    #hero h2 {
        font-size: 18px;
        line-height: 24px;
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .list-group-1 {
        display: none;
    }
    #hero .hero-img img {
        width: 80%;
    }
    .prejoin nav a {
        color: #000000 !important;
    }
}

@-webkit-keyframes up-down {
    0% {
        transform: translateY(10px);
    }

    100% {
        transform: translateY(-10px);
    }
}

@keyframes up-down {
    0% {
        transform: translateY(10px);
    }

    100% {
        transform: translateY(-10px);
    }
}

.hero-waves {
    display: block;
    margin-top: 60px;
    width: 100%;
    height: 60px;
    z-index: 5;
    position: relative;
}

.wave1 use {
    -webkit-animation: move-forever1 10s linear infinite;
    animation: move-forever1 10s linear infinite;
    -webkit-animation-delay: -2s;
    animation-delay: -2s;
}

.wave2 use {
    -webkit-animation: move-forever2 8s linear infinite;
    animation: move-forever2 8s linear infinite;
    -webkit-animation-delay: -2s;
    animation-delay: -2s;
}

.wave3 use {
    -webkit-animation: move-forever3 6s linear infinite;
    animation: move-forever3 6s linear infinite;
    -webkit-animation-delay: -2s;
    animation-delay: -2s;
}

@-webkit-keyframes move-forever1 {
    0% {
        transform: translate(85px, 0%);
    }

    100% {
        transform: translate(-90px, 0%);
    }
}

@keyframes move-forever1 {
    0% {
        transform: translate(85px, 0%);
    }

    100% {
        transform: translate(-90px, 0%);
    }
}

@-webkit-keyframes move-forever2 {
    0% {
        transform: translate(-90px, 0%);
    }

    100% {
        transform: translate(85px, 0%);
    }
}

@keyframes move-forever2 {
    0% {
        transform: translate(-90px, 0%);
    }

    100% {
        transform: translate(85px, 0%);
    }
}

@-webkit-keyframes move-forever3 {
    0% {
        transform: translate(-90px, 0%);
    }

    100% {
        transform: translate(85px, 0%);
    }
}

@keyframes move-forever3 {
    0% {
        transform: translate(-90px, 0%);
    }

    100% {
        transform: translate(85px, 0%);
    }
}

.text-center {
    text-align: center !important;
}

.pt-5,
.py-5 {
    padding-top: 3rem !important;
}

.pt-lg-0,
.py-lg-0 {
    padding-top: 0 !important;
}

.order-1 {
    -ms-flex-order: 1;
    order: 1;
}

.order-lg-1 {
    -ms-flex-order: 1;
    order: 1;
}

.form-div {
    width: 90%
}

.hero-heading {
    color: #fff;
    font-size: 20px;
    text-align: center;
    margin: 0;
    padding: 0;
    width: 100%;
    margin-bottom: 10px;
}

.inpt {
    height: 50px;
    font-weight: 600;
    font-size: 1.1rem;
}

.form-control {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.btnn {
    width: 100%;
}

.btn-primary {
    color: #fff;
    background-color: #ffffff;
    border-color: #ffffff;
}

.btn-primary:hover {
    background-color: #880000;
    border-color: #880000;
}

.btn-lg,
.btn-group-lg>.btn {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 0.3rem;
}

.btn-lg+.dropdown-toggle-split,
.btn-group-lg>.btn+.dropdown-toggle-split {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.btn-block+.btn-block {
    margin-top: 0.5rem;
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
    width: 100%;
}

.fade {
    transition: opacity 0.15s linear;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.btn:not(:disabled):not(.disabled) {
    cursor: pointer;
}

.btn-group-lg>.btn,
.btn-lg {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem;
}

.list-group-1 a:hover {
    background-color: #333666;
    color: #fff;
}

.list-group-1 a:hover small.text-muted {
    color: #fff !important;
}

.list-group-1 a:hover h5.col-c {
    color: #fff !important;
}

.list-group-1-item.active {
    background-color: rgb(95, 130, 181);
}

.list-group-1 {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    border-radius: 0.25rem;
}

.list-group-1-item-action  {
 /*   width: 100%; */
    color: #495057 !important;
    text-align: inherit;
}

.list-group-1-item-action:hover,
.list-group-1-item-action:focus {
    z-index: 1;
    color: #495057 !important;
    text-decoration: none;
    background-color: #f8f9fa;
}

.list-group-1-item-action:active {
    color: #212529;
    background-color: #e9ecef;
}

.list-group-1-item {
    position: relative;
    display: block;
    padding: 0.75rem 1.25rem;
    cursor: pointer;
    background-color: rgba(247, 247, 247, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.color-black{
    color: #1b1e21 !important;
}



.list-group-1-item:first-child {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
}

.list-group-1-item:last-child {
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
}

.list-group-1-item.disabled,
.list-group-1-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
}

.list-group-1-item.active {
    z-index: 2;
    color: #fff !important;
}

.list-group-1-item.active  {
    z-index: 2;
    color: #fff !important;
}

.list-group-1-item.active h5,.list-group-1-item.active  small {
    z-index: 2;
    color: #fff !important;
}

.list-group-1-item+.list-group-1-item {
    border-top-width: 0;
}

.list-group-1-item+.list-group-1-item.active {
    margin-top: -1px;
    border-top-width: 1px;
}

.list-group-1-horizontal {
    -ms-flex-direction: row;
    flex-direction: row;
}

.list-group-1-horizontal>.list-group-1-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
}

.list-group-1-horizontal>.list-group-1-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
}

.list-group-1-horizontal>.list-group-1-item.active {
    margin-top: 0;
}

.list-group-1-horizontal>.list-group-1-item+.list-group-1-item {
    border-top-width: 1px;
    border-left-width: 0;
}

.list-group-1-horizontal>.list-group-1-item+.list-group-1-item.active {
    margin-left: -1px;
    border-left-width: 1px;
}

@media (min-width: 576px) {
    .list-group-1-horizontal-sm {
        -ms-flex-direction: row;
        flex-direction: row;
    }

    .list-group-1-horizontal-sm>.list-group-1-item:first-child {
        border-bottom-left-radius: 0.25rem;
        border-top-right-radius: 0;
    }

    .list-group-1-horizontal-sm>.list-group-1-item:last-child {
        border-top-right-radius: 0.25rem;
        border-bottom-left-radius: 0;
    }

    .list-group-1-horizontal-sm>.list-group-1-item.active {
        margin-top: 0;
    }

    .list-group-1-horizontal-sm>.list-group-1-item+.list-group-1-item {
        border-top-width: 1px;
        border-left-width: 0;
    }

    .list-group-1-horizontal-sm>.list-group-1-item+.list-group-1-item.active {
        margin-left: -1px;
        border-left-width: 1px;
    }
}

@media (min-width: 768px) {
    .list-group-1-horizontal-md {
        -ms-flex-direction: row;
        flex-direction: row;
    }

    .list-group-1-horizontal-md>.list-group-1-item:first-child {
        border-bottom-left-radius: 0.25rem;
        border-top-right-radius: 0;
    }

    .list-group-1-horizontal-md>.list-group-1-item:last-child {
        border-top-right-radius: 0.25rem;
        border-bottom-left-radius: 0;
    }

    .list-group-1-horizontal-md>.list-group-1-item.active {
        margin-top: 0;
    }

    .list-group-1-horizontal-md>.list-group-1-item+.list-group-1-item {
        border-top-width: 1px;
        border-left-width: 0;
    }

    .list-group-1-horizontal-md>.list-group-1-item+.list-group-1-item.active {
        margin-left: -1px;
        border-left-width: 1px;
    }
}

@media (min-width: 992px) {
    .list-group-1-horizontal-lg {
        -ms-flex-direction: row;
        flex-direction: row;
    }

    .list-group-1-horizontal-lg>.list-group-1-item:first-child {
        border-bottom-left-radius: 0.25rem;
        border-top-right-radius: 0;
    }

    .list-group-1-horizontal-lg>.list-group-1-item:last-child {
        border-top-right-radius: 0.25rem;
        border-bottom-left-radius: 0;
    }

    .list-group-1-horizontal-lg>.list-group-1-item.active {
        margin-top: 0;
    }

    .list-group-1-horizontal-lg>.list-group-1-item+.list-group-1-item {
        border-top-width: 1px;
        border-left-width: 0;
    }

    .list-group-1-horizontal-lg>.list-group-1-item+.list-group-1-item.active {
        margin-left: -1px;
        border-left-width: 1px;
    }
}

@media (min-width: 1200px) {
    .list-group-1-horizontal-xl {
        -ms-flex-direction: row;
        flex-direction: row;
    }

    .list-group-1-horizontal-xl>.list-group-1-item:first-child {
        border-bottom-left-radius: 0.25rem;
        border-top-right-radius: 0;
    }

    .list-group-1-horizontal-xl>.list-group-1-item:last-child {
        border-top-right-radius: 0.25rem;
        border-bottom-left-radius: 0;
    }

    .list-group-1-horizontal-xl>.list-group-1-item.active {
        margin-top: 0;
    }

    .list-group-1-horizontal-xl>.list-group-1-item+.list-group-1-item {
        border-top-width: 1px;
        border-left-width: 0;
    }

    .list-group-1-horizontal-xl>.list-group-1-item+.list-group-1-item.active {
        margin-left: -1px;
        border-left-width: 1px;
    }
}

.list-group-1-flush {
    border-radius: 0;
}

.list-group-1-flush>.list-group-1-item {
    border-width: 0 0 1px;
}

.list-group-1-flush>.list-group-1-item:last-child {
    border-bottom-width: 0;
}

.list-group-1-item-primary {
    color: #004085;
    background-color: #b8daff;
}

.list-group-1-item-primary.list-group-1-item-action:hover,
.list-group-1-item-primary.list-group-1-item-action:focus {
    color: #004085;
    background-color: #9fcdff;
}

.list-group-1-item-primary.list-group-1-item-action.active {
    color: #fff !important;
    background-color: #004085;
    border-color: #004085;
}

.list-group-1-item-primary.list-group-1-item-action.active h5{
    color: #fff !important;

}
.list-group-1-item-primary.list-group-1-item-action  h5{
    color: #383d41 !important;

}
.list-group-1-item-secondary {
    color: #383d41;
    background-color: #d6d8db;
}

.list-group-1-item-secondary.list-group-1-item-action:hover,
.list-group-1-item-secondary.list-group-1-item-action:focus {
    color: #383d41;
    background-color: #c8cbcf;
}

.list-group-1-item-secondary.list-group-1-item-action.active {
    color: #fff;
    background-color: #383d41;
    border-color: #383d41;
}

.list-group-1-item-success {
    color: #155724;
    background-color: #c3e6cb;
}

.list-group-1-item-success.list-group-1-item-action:hover,
.list-group-1-item-success.list-group-1-item-action:focus {
    color: #155724;
    background-color: #b1dfbb;
}

.list-group-1-item-success.list-group-1-item-action.active {
    color: #fff;
    background-color: #155724;
    border-color: #155724;
}

.list-group-1-item-info {
    color: #0c5460;
    background-color: #bee5eb;
}

.list-group-1-item-info.list-group-1-item-action:hover,
.list-group-1-item-info.list-group-1-item-action:focus {
    color: #0c5460;
    background-color: #abdde5;
}

.list-group-1-item-info.list-group-1-item-action.active {
    color: #fff;
    background-color: #0c5460;
    border-color: #0c5460;
}

.list-group-1-item-warning {
    color: #856404;
    background-color: #ffeeba;
}

.list-group-1-item-warning.list-group-1-item-action:hover,
.list-group-1-item-warning.list-group-1-item-action:focus {
    color: #856404;
    background-color: #ffe8a1;
}

.list-group-1-item-warning.list-group-1-item-action.active {
    color: #fff;
    background-color: #856404;
    border-color: #856404;
}

.list-group-1-item-danger {
    color: #721c24;
    background-color: #f5c6cb;
}

.list-group-1-item-danger.list-group-1-item-action:hover,
.list-group-1-item-danger.list-group-1-item-action:focus {
    color: #721c24;
    background-color: #f1b0b7;
}

.list-group-1-item-danger.list-group-1-item-action.active {
    color: #fff;
    background-color: #721c24;
    border-color: #721c24;
}

.list-group-1-item-light {
    color: #818182;
    background-color: #fdfdfe;
}

.list-group-1-item-light.list-group-1-item-action:hover,
.list-group-1-item-light.list-group-1-item-action:focus {
    color: #818182;
    background-color: #ececf6;
}

.list-group-1-item-light.list-group-1-item-action.active {
    color: #fff;
    background-color: #818182;
    border-color: #818182;
}

.list-group-1-item-dark {
    color: #1b1e21;
    background-color: #c6c8ca;
}

.list-group-1-item-dark.list-group-1-item-action:hover,
.list-group-1-item-dark.list-group-1-item-action:focus {
    color: #1b1e21;
    background-color: #b9bbbe;
}

.list-group-1-item-dark.list-group-1-item-action.active {
    color: #fff;
    background-color: #1b1e21;
    border-color: #1b1e21;
}

.flex-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
}

.align-items-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
}

.w-100 {
    width: 100% !important;
}


.justify-content-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
}


.mb-1,
.my-1 {
    margin-bottom: 0.25rem !important;
}

.order-2 {
    -ms-flex-order: 2;
    order: 2;
}

.order-lg-2 {
    -ms-flex-order: 2;
    order: 2;
}

section {
    padding: 30px 0;
    overflow: hidden;
  }

.hero-h{
    color:#fff;
}


.front-page{
    /* background-image: url("../images/conference.jpeg"); */
}

.features {
    color: #333 !important;
    }

.img-fluid1 {
    max-width: 100%;
    width: 128px !important;
}

.prejoin nav a {
    color: #333666;
}

.prejoin .mobile-nav a {
    color: rgb(94, 130, 180) !important;
}

.prejoin .drop-down .drop-down1 a {
    color: #5E82B4 !important;
}
.marginInc{
    margin-top: 70px;
}

.marginInc8{
    margin-top: 9%;
}

@media (max-width: 700px) {
    .marginInc{
        margin-top: 15%;
    }
}


.color-white {
    color: #fff;
}

