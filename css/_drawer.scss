.drawer-portal {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24px;
    z-index: $drawerZ;
}

.drawer-menu {
    padding: 0 16px;
    max-height: 50vh;
    background: #233b4f;
    border-radius: 8px;
    overflow-y: auto;

    &.expanded {
        max-height: 80vh;
    }

    .drawer-toggle {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 44px;
        cursor: pointer;

        svg {
            fill: none;
        }
    }

    .popupmenu {
        margin: auto;
        width: 100%;
    }

    .popupmenu__item {
        height: 48px;
    }

    &#{&} .overflow-menu {
        margin: auto;
        font-size: 1.2em;
        list-style-type: none;
        padding: 0;

        .overflow-menu-item {
            box-sizing: border-box;
            height: 48px;
            padding: 12px 16px;
            align-items: center;
            color: $overflowMenuItemColor;
            cursor: pointer;
            display: flex;
            font-size: 16px;
            flex-direction: row;
            // justify-content: space-between;

            div {
                display: flex;
                flex-direction: row;
                align-items: center;
            }

            &.unclickable {
                cursor: default;
            }
            @media (hover: hover) and (pointer: fine) {
                &.unclickable:hover {
                    background: inherit;
                }
            }
            &.disabled {
                cursor: initial;
                color: #333666;
            }
        }

        .profile-text {
            max-width: 100%;
            text-overflow: ellipsis;
            // overflow: hidden;
            // white-space: nowrap;
        }
    }
}
