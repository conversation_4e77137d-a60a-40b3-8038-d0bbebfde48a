/**
 * Shared style for full screen local track based dialogs/modals.
 */
 .premeeting-screen,
  .preview-overlay {
     position: absolute;
     left: 0;
     right: 0;
     top: 0;
     bottom: 0;
  }

  .premeeting-screen-lobby,
  .preview-overlay {
     position: absolute;
     left: 0;
     right: 0;
     top: 0;
     bottom: 0;
  }

 .premeeting-screen {
    align-items: stretch;
    background: #333666;
    display: flex;
    flex-direction: column;
    font-size: 13px;
    height: 100%;
    max-height: 1100px;
    z-index: $toolbarZ + 1;

    &-lobby {
        height: 100%;
        align-items: stretch;
        background: #fff;
        display: flex;
        flex-direction: column;
        font-size: 1.3em;
        // height: max-content;
        z-index: $toolbarZ + 1;
    }
    .preview-log-conference{
        z-index: 352;
    position: relative;
    #preview {
        z-index: 353;
        position: relative;
    }
    }

    .invalidMeeting{
        z-index: 355;
        position: relative;
    }
    &-avatar {
        background-color: #A4B8D1;
        margin-bottom: 24px;

        text {
            fill: black;
            font-size: 26px;
            font-weight: 400;
        }
    }

    footer#footer{
        z-index: 359;
        position: relative;

        &.headerFooter {
            position: relative;
            width: 100%;
            bottom: 0;
        }
    }

    .action-btn {
        border-radius: 3px;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-size: 15px;
        line-height: 24px;
        margin-top: 16px;
        padding: 7px 16px;
        position: relative;
        text-align: center;
        width: 85%;
        @media (max-width: 767px) {
            width: 85%;
        }
        &.meeth {
            width: 85%;
            padding: 12px;
            border-radius: 4px;

            background-color: #036531;
            color: #fff;
            border-color: #036531;
            &:hover{
                background-color: #036531;
                color: #fff;
                border-color: #036531;
            }
        }
        &.primary {
            background: #0376DA;
            border: 1px solid #0376DA;
        }

        &.secondary {
            background: transparent;
            border: 1px solid #5E6D7A;
        }

        &.text {
            width: auto;
            font-size: 13px;
            margin: 0;
            padding: 0;
        }

        &.disabled {
            background: #5E6D7A;
            border: 1px solid #5E6D7A;
            color: #AFB6BC;
            cursor: initial;

            .icon {
                & > svg {
                    fill: #AFB6BC;
                }
            }
        }

        .options {
            border-radius: 30px;
            align-items: center;
            display: flex;
            height: 100%;
            justify-content: center;
            position: absolute;
            right: 0;
            top: 0;
            width: 36px;

            &:hover {
                // background-color: #0262B6;
            }

            svg {
                pointer-events: none;
            }
        }
    }

    .meeting-link-desktop{
        z-index: 352;
        position: relative;
    }




    .action-btn1 {

        .overflow-menu-item{
            color: #fff;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            display: inline-block;
            font-size: 15px;
            line-height: 24px;
            background: #036531;
            border-color: #036531;
            margin-top: 16px;
            margin-bottom: 6px;
            padding: 7px 0px;
            /* position: relative; */
            text-align: center;
            /* width: 286px; */
            width: 100%;
        }
        .overflow-menu-item.disabled{
            background: #383838;
        }
        // .overflow-menu-item:hover{
        //     background: none  !important;
        // }

    }
    .preview-overlay {
        z-index: $toolbarZ + 1;
    }

    .content {
        align-items: center;
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: flex-end;
        padding-bottom: 24px;
        z-index: $toolbarZ + 10;
        position: relative;

        .title {
            color: #fff;
            font-size: 24px;
            line-height: 32px;
            margin-bottom: 16px;
        }



        // input.field {
        //     background-color: white;
        //     border: none;
        //     outline: none;
        //     border-radius: 3px;
        //     font-size: 15px;
        //     line-height: 24px;
        //     color: #1C2025;
        //     padding: 8px 0;
        //     text-align: center;
        //     width: 320px;

        //     &.error {
        //         box-shadow: 0px 0px 4px 3px rgba(225, 45, 45, 0.4);
        //     }

        //     &.focused {
        //         box-shadow: 0px 0px 4px 3px #0376DA;
        //     }
        // }
    }

    .media-btn-container {
        display: flex;
        justify-content: center;
        margin: 24px 0 16px 0;
        width: 100%;

        &> div {
            margin: 0 12px;
        }
    }
}

#preview {
    height: 310px;
    // position: absolute;
    width: 100%;
    position: relative;



    &.no-video {
        background: radial-gradient(50% 50% at 50% 50%, #5B6F80 0%, #365067 100%), #FFFFFF;
        text-align: center;
    }

    .avatar {
        background: #A4B8D1;
        margin: 0 auto;
    }

    video {
        border-radius: 4px;
        height: 100%;
        object-fit: cover;
        // position: absolute;
        width: 100%;
    }

    .toolbox-content-items1 {
        background: #333666;
        // box-shadow: 0px 2px 8px 4px rgba(51, 45, 45, 0.25), 0px 0px 0px 1px rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin: 0 auto;
        padding: 6px;
        text-align: center;

        position: relative;
        left: auto;
        top: -21%;
        width: 130px;

        @media (max-width: 800px) {
            left: auto;
            right: auto;
            width: 130px;

        }

        @media (max-width: 1250px) {
            left: auto;
            right: auto;
            width: 130px;

        }

        @media (max-width: 1100px) {
            left: auto;
            right: auto;
            width: 130px;

        }

        @media (max-width: 720px) {
            left: 0;
            right: 0;
            width: 126px;
        }

        @media (max-width: 400px) {
            left: 0;
            right: 0;
            width: 126px;
        }

        // position: relative;
        // bottom: 66px;

        >div {
            margin-left: 8px;

            &:first-child {
                margin: 0 auto;
            }
        }

        @media (max-width: 500px){
            background: #6c97a8;
            box-shadow: 0px 2px 8px 4px rgba(0, 0, 0, 0.25), 0px 0px 0px 1px rgba(0, 0, 0, 0.15);
            border-radius: 6px;
            margin: 0 auto;
            padding: 6px;
            text-align: center;

            >div {
                margin-left: 8px;

                &:first-child {
                    margin-left: 0;
                    // background-color: rgb(0 0 0 / 50%);
                    border-radius: 4px;
                }
            }
        }
    }
}

@mixin flex-centered() {
    align-items: center;
    display: flex;
    justify-content: center;
}

@mixin icon-container($bg, $fill) {
    .toggle-button-icon-container {
        background: $bg;

        svg {
            fill: $fill
        }
    }
}

.addColumn {
    @media (max-width: 730px) {
        height: 100% !important;
        max-height: 730px;
    }
}

.toggle-button {
    border-radius: 3px;
    cursor: pointer;
    color: #fff;
    font-size: 13px;
    height: 40px;
    margin: 0 auto;
    background: rgba(49,71,91,.88);
    transition: background 0.16s ease-out;
    width: 320px;

    @include flex-centered();

    svg {
        fill: transparent;
    }

    &:hover {

        @include icon-container(#A4B8D1, #1C2025);
    }

    &-container {
        position: relative;

        @include flex-centered();
    }

    &-icon-container {
        border-radius: 50%;
        left: -22px;
        padding: 2px;
        position: absolute;
    }

    &--toggled {
        @include icon-container(white, #1C2025);
    }
}

.meeting-link-desktop {
    .copy-meeting {
        align-items: center;
        cursor: pointer;
        color: #fff;
        display: flex;
        flex-direction: row;
        font-size: 15px;
        font-weight: 300;
        justify-content: center;
        line-height: 24px;
        margin-bottom: 16px;

        .url {
            background: rgba(28, 32, 37, 0.5);
            border-radius: 4px;
            display: flex;
            padding: 8px 10px;
            transition: background 0.16s ease-out;

            &:hover {
                background: #1C2025;
            }

            &.done {
                background: #31B76A;
            }

            .jitsi-icon {
                margin-left: 10px;
            }
        }

        .copy-meeting-text {
            width: 266px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &:hover {
            align-self: stretch;
        }

        textarea {
            border-width: 0;
            height: 0;
            opacity: 0;
            padding: 0;
            width: 0;
        }
    }
}

.user-preview{
    // background-color: #4e6372;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    // padding: 10px;
    border-radius: 4px;
    min-height: 310px;
    height: 100%;
    text-align: center;
    width: 100%;
    .premeeting-screen-avatar{
        margin: 0 auto;
        padding: 6px;
        position: relative;
        top: 102px;
    }
    .toolbox-content-items1 {
        // background: rgb(49 71 91 / 88%);
        // box-shadow: 0px 2px 8px 4px rgba(51, 45, 45, 0.25), 0px 0px 0px 1px rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin: 0 auto;
        padding: 6px;
        text-align: center;
        position: relative;
        top: 152px;
        // position: relative;
        // bottom: 66px;

        >div {
            margin-left: 8px;

            &:first-child {
                margin: 0 auto;
            }
        }

        @media (max-width: 500px){
            background: $newToolbarBackgroundColor;
            box-shadow: 0px 2px 8px 4px rgba(0, 0, 0, 0.25), 0px 0px 0px 1px rgba(0, 0, 0, 0.15);
            border-radius: 6px;
            margin: 0 auto;
            padding: 6px;
            text-align: center;

            >div {
                margin-left: 8px;

                &:first-child {
                    margin-left: 0;
                }
            }
        }
    }
}

.prejoin-input-field {
    background-color: #f1f1f1;
    padding: 10px;
    border: none;
    height: 50px;
    margin-bottom: 15px;
    text-align: center;
    border-radius: 30px;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    display: inline-block;
    width: 85%;

    &:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
    }
}

.meeting-link-mobile {
    .copy-meeting {
        align-items: center;
        cursor: pointer;
        color: #fff;
        display: flex;
        flex-direction: row;
        font-size: 15px;
        font-weight: 300;
        justify-content: center;
        line-height: 24px;
        margin-bottom: 16px;

        .url {
            background: rgba(28, 32, 37, 0.5);
            border-radius: 4px;
            display: flex;
            padding: 8px 10px;
            transition: background 0.16s ease-out;

            &:hover {
                background: #1C2025;
            }

            &.done {
                background: #31B76A;
            }

            .jitsi-icon {
                margin-left: 10px;
            }
        }

        .copy-meeting-text {
            width: 266px;
            @media (max-width: 450px) {
                width: 219px;
            }
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &:hover {
            align-self: stretch;
        }

        textarea {
            border-width: 0;
            height: 0;
            opacity: 0;
            padding: 0;
            width: 0;
        }
    }
}

.bg-white-radius-20 {
    padding-top: 6px;
    background-color: #fff;
    border-radius: 4px;
}
