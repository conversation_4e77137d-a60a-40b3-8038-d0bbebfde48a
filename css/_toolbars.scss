/**
 * Round badge.
 */
 .badge-round {
    background-color: $toolbarBadgeBackground;
    border-radius: 50%;
    box-sizing: border-box;
    color: $toolbarBadgeColor;
    // Do not inherit the font-family from the toolbar button, because it's an
    // icon style.
    font-family: $baseFontFamily;
    font-size: 9px;
    font-weight: 700;
    line-height: 13px;
    min-width: 13px;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    vertical-align: middle;
}

/**
 * TODO: when the old filmstrip has been removed, remove the "new-" prefix.
 */
.new-toolbox {
    display: none;
    bottom: calc((#{$newToolbarSize} * 2) * -1);
    left: 0;
    position: absolute;
    right: 0;
    transition: bottom .3s ease-in;
    width: 100%;


    &.visible {
        bottom: 0;
    }

    &.no-buttons {
        display: none;
    }

    @media (min-width: 581px) {
        &.shift-right {
            margin-left: $sidebarWidth;
            width: calc(100% - #{$sidebarWidth});
        }
        &.shift-left {
            margin-right: 315px;
            width: calc(100% - 315px);
        }
    }
}

.toolbox-content-mobile {
    max-width: fit-content;
    width: 100% !important;
}

.toolbox-content {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    margin-left: auto;
    margin-bottom: 16px;
    margin-right: auto;
    box-shadow: 0 2px 8px 4px rgb(0 0 0 / 25%), 0 0 0 1px rgb(0 0 0 / 15%);
    padding: 6px;
    border-radius: 6px;
    background-color: #333666;
    position: relative;
    width: 70%;
    z-index: $toolbarZ;

    .button-group-center,
    .button-group-left,
    .button-group-right {
        display: flex;
        width: 33%;
    }

    .button-group-center {
        justify-content: center;
    }

    .button-group-right {
        justify-content: flex-end;
    }

    .toolbox-button-wth-dialog {
        display: inline-block;

        &> div {
            padding: 0;
        }
    }

    @media (max-width: 500px) {
            align-items: center;
            box-sizing: border-box;
            display: flex;
            margin-bottom: 22px !important;
            position: relative;
            z-index: $toolbarZ;
            box-shadow: 0 2px 8px 4px rgb(0 0 0 / 25%), 0 0 0 1px rgb(0 0 0 / 15%);
            padding: 0px;
            border-radius: 1px;
            background-color: rgba(49,71,91,.88);

            .button-group-center,
            .button-group-left,
            .button-group-right {
                display: flex;
                width: 33%;
            }

            .button-group-center {
                justify-content: center;
            }

            .button-group-right {
                justify-content: flex-end;
            }

            .toolbox-button-wth-dialog {
                display: inline-block;
            }

    }
}

.toolbox-button {
    color: $toolbarButtonColor;
    cursor: pointer;
    display: inline-block;
    line-height: $newToolbarSize;
    text-align: center;
}

.toolbar-button-with-badge {
    display: inline-block;
    position: relative;

    .badge-round {
        bottom: -5px;
        font-size: 12px;
        line-height: 20px;
        min-width: 20px;
        pointer-events: none;
        position: absolute;
        right: -5px;
    }
}


.toolbox-content-items {
    background: rgb(49 71 91 / 88%);
    box-shadow: 0px 2px 8px 4px rgba(0, 0, 0, 0.25), 0px 0px 0px 1px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    margin: 0 auto;
    padding: 6px;
    text-align: center;

    >div {
        margin-left: 8px;

        &:first-child {
            margin: 0 auto;
        }
    }

    @media (max-width: 500px){
        background: $newToolbarBackgroundColor;
        box-shadow: 0px 2px 8px 4px rgba(0, 0, 0, 0.25), 0px 0px 0px 1px rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin: 0 auto;
        padding: 6px;
        text-align: center;

        >div {
            margin-left: 8px;

            &:first-child {
                margin-left: 0;
            }
        }
    }
}



.rows.toolbar-fc-m {
    margin-left: 4% !important;
}

.overflow-menu {
    font-size: 14px;
    list-style-type: none;
    padding: 8px 0;
    margin-bottom: 0;

    @media (max-width: 600px) {
        background-color: none !important;
    }
    background-color: #333666;

    .profile-text {
        max-width: 150px;
        text-overflow: ellipsis;
        // overflow: hidden;
        // white-space: nowrap;
    }
}

.overflow-menu-item {
    align-items: center;
    color: $overflowMenuItemColor;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    font-size: 14px;
    text-align: center;
    font-weight: 400;
    height: 85px;
    line-height: 24px;
    padding: 8px 16px;
    box-sizing: border-box;

    @media (hover: hover) and (pointer: fine) {
        &:hover {
            background:  #036531;
            color: #fff;
        }
    }

    &-text {
        word-wrap: break-word;
    }

    div {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    &.unclickable {
        cursor: default;
    }
    &.disabled {
        cursor: initial;
        color: #b8c7e0;
    }

    @media (hover: hover) and (pointer: fine) {
        &.unclickable:hover {
            background: inherit;
        }
    }
}

.toolbox-content-wrapper {
@media (max-width: 500px){
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    max-width: 100%;
}
}
.beta-tag {
    background: #36383C;
    border-radius: 3px;
    color: #fff;
    font-size: 12px;
    margin-left: 8px;
    padding: 0 4px;
    text-transform: uppercase;
}

.overflow-menu-item-icon {
    // margin-right: 16px;
    @media (max-width: 500px) {
        padding-right: 7px;
    }

    i {
        display: inline;
        font-size: 24px;
        fill:rgba(0, 0, 0, 0.15)
    }


        > div > svg {

            height: 30px;
            width: 30px;
        }


    @media (hover: hover) and (pointer: fine) {
        i:hover {
            background-color: initial;
        }
    }

    img {
        max-width: 24px;
        max-height: 24px;
    }

    svg {
        fill: #fff;
        height: 20px;
        width: 20px;
    }
}


.overflow-menu-hr {
    border-top: 1px solid #4C4D50;
    border-bottom: 0;
    margin: 8px 0;
}

.toolbox-icon {
    display: flex;
    border-radius: 3px;
    flex-direction: column;
    font-size: 24px;
    height: $newToolbarSize;
    justify-content: center;
    width: $newToolbarSize;

    @media (hover: hover) and (pointer: fine) {
        &:hover {
            background: #036531;
        }
    }

    &.toggled {
        background: $newToolbarButtonToggleColor;
    }

    &.disabled {
        cursor: initial !important;
        background-color: #36383c !important;

        svg {
            fill: #929292 !important;
        }
    }
}

.hangup-button {
    margin-left: 3px;
    margin-right: 3px;
    background-color: $hangupColor;

    @media (hover: hover) and (pointer: fine) {
        &:hover {
            background-color: $hangupHoverColor;
        }
    }

    svg {
        fill: #fff;
    }
}

/**
 * START of fade in animation for main toolbar
 */
.fadeIn {
    opacity: 1;

    @include transition(all .3s ease-in);
}

.fadeOut {
    opacity: 0;

    @include transition(all .3s ease-out);
}

/**
 * Audio and video buttons do not have toggled state.
 */
.audio-preview,
.video-preview {
    .toolbox-icon.toggled {
        background: none;

        &:hover {
            background: $newToolbarButtonHoverColor;
        }
    }

}

.toolbox-button-wth-dialog {
    & > .gtGkeF {
        background: none;
    }
}

.prejoin-preview-dropdown-container {
    & > .gtGkeF {
        background: #333666;
    }
}

/**
 * On small mobile devices make the toolbar full width and pad the invite prompt.
 */
 .toolbox-content-mobile {
    @media (max-width: 500px) {
        margin-bottom: 0;

        .toolbox-content-wrapper {
            width: 100%;
        }

        .toolbox-content-items {
            border-radius: 0;
            display: flex;
            justify-content: space-evenly;
            padding: 6px 6px;
            width: 100%;
        }

        .invite-more-container {
            margin: 0 16px 8px;
        }

        .invite-more-container.elevated {
            margin-bottom: 52px;
        }
    }
}
