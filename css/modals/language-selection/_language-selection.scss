
.language-dropdown-menu {
    max-width: 200px;
    background-color: transparent;
    cursor: pointer;

    .language-selector-trigger-container{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;

        span {
            font-size: 15px;
            font-weight: 500;
        }

        .prejoin-preview-dropdown-icon {
            svg {
                fill: inherit !important;
            }

            svg path {
                fill: inherit !important;
            }
        }
    }
}

// Override global jitsi-icon rule for language dropdown
.language-dropdown-menu .prejoin-preview-dropdown-icon.jitsi-icon {
    svg {
        fill: var(--icon-color, inherit) !important;
    }

    svg path {
        fill: var(--icon-color, inherit) !important;
    }
    }

// Additional specific override for language dropdown arrow
.language-dropdown-arrow.jitsi-icon {
    svg {
        fill: var(--icon-color, inherit) !important;
    }

    svg path {
        fill: var(--icon-color, inherit) !important;
    }
}

    .selected-option{
        background-color: #333666 !important;
        color: #ffffff !important;
    }
}
