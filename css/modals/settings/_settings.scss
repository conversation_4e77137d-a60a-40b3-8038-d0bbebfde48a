.settings-pane {
    display: flex;
    width: 100%;

    &.profile-pane {
        flex-direction: column;
    }

    .auth-name {
        margin-bottom: 4px;
    }

    .calendar-tab,
    .device-selection {
        margin-top: 20px;
    }

    .mock-atlaskit-label {
        color: #000;
        font-size: 12px;
        font-weight: 600;
        line-height: 1.33;
        padding: 20px 0px 4px 0px;
    }

    input[type="checkbox"] + svg + span {
        color: #000000;
    }

    .calendar-tab,
    .more-tab,
    .profile-edit {
        display: flex;
        width: 100%;
    }

    .profile-edit-field {
        flex: 1;
    }
    .settings-sub-pane {
        flex-grow: 1;
    }

    .profile-edit-field {
        margin-right: 20px;
    }

    .language-settings {
        max-width: 50%;
    }

    .calendar-tab {
        align-items: center;
        flex-direction: column;
        font-size: 14px;
        min-height: 100px;
        text-align: center;
    }

    .calendar-tab-sign-in {
        margin-top: 20px;
    }

    .sign-out-cta {
        margin-bottom: 20px;
    }
}

.custom-checkbox {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.custom-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.custom-checkbox svg rect {
    fill: var(--checkbox-background-color, #ffffff); /* Default background */
    stroke: var(--checkbox-border-color, #000000); /* Border color */
    stroke-width: 2px;
    transition: fill 0.2s ease, stroke 0.2s ease;
}

.custom-checkbox svg path {
    fill: transparent; /* Hide the tick by default */
    transition: fill 0.2s ease;
}

/* When the checkbox is checked */
.custom-checkbox input[type="checkbox"]:checked + svg rect {
    fill: var(--checkbox-tick-color, #333666); /* Change background when checked */
}

.custom-checkbox input[type="checkbox"]:checked + svg path {
    fill: var(--checkbox-tick-color, #333666); /* Show the tick when checked */
}
