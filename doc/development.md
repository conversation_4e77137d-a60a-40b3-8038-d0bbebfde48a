# Developing Meet Hour

## Building the sources

Node.js >= 10 and npm >= 6 are required.

On Debian/Ubuntu systems, the required packages can be installed with:
```
sudo apt-get install npm nodejs
cd meet-hour
npm install
make lib-meethour-module
```
Running with webpack-dev-server for development
Use it at the CLI, type

Commands:
```
npm install
```

By default the backend deployment used is beta.shell.groupvi.systems. You can point the Meet-Hours app at a different backend by using a proxy server. To do this, set the WEBPACK_DEV_SERVER_PROXY_TARGET variable:
 
```
export WEBPACK_DEV_SERVER_PROXY_TARGET=https://beta.shell.groupvi.systems
```

To build the Meet Hour application, just type
```
make
```

For running code for development

```
make dev
```

To export source code

```
make source-package
```

### Working with the library sources (lib-meet-hour)

By default the library is build from its git repository sources. The default dependency path in package.json is :
```json
"lib-meet-hour": "jitsi/lib-meet-hour",
```

To work with local copy you must change the path to:
```json
"lib-meet-hour": "file:///Users/<USER>/local-lib-meet-hour-copy",
```

To make the project you must force it to take the sources as 'npm update':
```
npm install lib-meet-hour --force && make
```

Or if you are making only changes to the library:
```
npm install lib-meet-hour --force && make deploy-lib-meet-hour
```

Alternative way is to use [npm link](https://docs.npmjs.com/cli/link).
It allows to link `lib-meet-hour` dependency to local source in few steps:

```bash
cd lib-meet-hour

#### create global symlink for lib-meet-hour package
npm link

cd ../meet-hour

#### create symlink from the local node_modules folder to the global lib-meet-hour symlink
npm link lib-meet-hour
```

 After changes in local `lib-meet-hour` repository, you can rebuild it with `npm run install` and your `meet-hour` repository will use that modified library.
Note: when using node version 4.x, the make file of meet-hour do npm update which will delete the link. It is no longer the case with version 6.x.

If you do not want to use local repository anymore you should run
```bash
cd meet-hour
npm unlink lib-meet-hour
npm install
```
### Running with webpack-dev-server for development

Use it at the CLI, type
```
make dev
```

By default the backend deployment used is `meethour.org`. You can point the meet-hour app at a different backend by using a proxy server. To do this, set the WEBPACK_DEV_SERVER_PROXY_TARGET variable:
```
export WEBPACK_DEV_SERVER_PROXY_TARGET=https://your-example-server.com
make dev
```

The app should be running at https://localhost:8080/

#### Chrome Privacy Error

Newer versions of Chrome may block localhost under https and show `NET::ERR_CERT_INVALID` on the page. To solve this open [chrome://flags/#allow-insecure-localhost](chrome://flags/#allow-insecure-localhost) and select Enable, then press Relaunch or quit and restart Chrome.
