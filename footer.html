<footer id="footer">
  <div class="footer-top">
    <div class="container">
      <div class="row">
        <div class="col-lg-3 col-md-6 footer-contact">
          <h3>Meet Hour LLC<span>.</span></h3>
          <p>
            8825 Stanford , Suite 205<br />
            Columbia, MD 21045 <br /><br /><strong>Phone:</strong>
            <a href="tel:+**************** ">+****************</a><br /><strong
              >Email:</strong
            >
            <a href="mailto:<EMAIL>"><EMAIL></a><br />
          </p>
        </div>
        <div class="col-lg-2 col-md-6 footer-links">
          <h4>Company</h4>
          <ul>
            <li><i class="bx bx-chevron-right"></i> <a href="/">Home</a></li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="/who-we-are.html">Who we are</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="#features">Features</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i> <a href="#pricing">Pricing</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="https://testimonials.shell.groupvi.systems/all" target="_blank"
                >Testimonials</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="/news.html">In the News</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="/contact.html">Contact</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="http://kb.shell.groupvi.systems/" target="_blank">Help Desk</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i> <a href="/faqs">FAQs</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="http://kb.shell.groupvi.systems/category/how-to" target="_blank"
                >Knowledge Base</a
              >
            </li>
          </ul>
        </div>
        <div class="col-lg-3 col-md-6 footer-links">
          <h4>App</h4>
          <ul>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a
                href="https://apps.apple.com/in/app/meet-hour/id1527001689"
                target="_blank"
                >iOS App Download</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a
                href="https://play.google.com/store/apps/details?id=meet.groupvi.systems"
                target="_blank"
                >Android App Download</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a
                href="https://repo.shell.groupvi.systems/download/windows/latest/meet-hour.exe"
                target="_blank"
                >Windows App Download</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a
                href="https://repo.shell.groupvi.systems/download/mac/latest/meet-hour.dmg"
                target="_blank"
                >Mac App Download</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a
                href="https://repo.shell.groupvi.systems/download/linux/latest/meet-hour-amd64.deb"
                target="_blank"
                >Ubuntu App Download</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a
                href="https://repo.shell.groupvi.systems/download/linux/latest/meet-hour-x86_64.AppImage"
                target="_blank"
                >Linux App Download</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a
                href="https://slack.com/apps/A04FDND88BZ-meet-hour-slack-bot"
                target="_blank"
                >Slack Bot</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="/disclaimer.html">Disclaimer</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="/privacy-policy.html">Privacy Policy</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="/terms-conditions.html">Terms & Conditions</a>
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="/refund-and-cancellation.html"
                >Refund & Cancellation Policy</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="https://docs.v-empower.com/docs/MeetHour-API "
                >API Documentation</a
              >
            </li>
            <li>
              <i class="bx bx-chevron-right"></i>
              <a href="https://github.com/v-empower/MeetHour-Web-MobileSDKs"
                >Web & Mobile SDK</a
              >
            </li>
          </ul>
        </div>
        <div class="col-lg-4 col-md-6 footer-newsletter">
          <img class="img-fluid" src="images/footer-img.png" />
        </div>
      </div>
    </div>
  </div>
  <div class="container d-md-flex py-4">
    <div class="mr-md-auto text-center text-md-left">
      <div class="copyright">
        © Copyright <strong><span>Meet Hour LLC.</span></strong
        >. All Rights Reserved
      </div>
    </div>
    <div class="social-links text-center text-md-right pt-3 pt-md-0">
      <a
        href="https://www.facebook.com/meethour/"
        rel="noopener noreferrer"
        target="_blank"
        ><i class="bx bxl-facebook"></i></a
      ><a
        href="https://twitter.com/MeetHourApp/"
        rel="noopener noreferrer"
        target="_blank"
      >
      <svg
      height="14"
      width="14"
      viewBox = '0 0 512 512'
      xmlns = 'http://www.w3.org/2000/svg'>
      <path fill="#ffffff" d = 'M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z' /></svg></a>
      <a
        href="https://www.instagram.com/MeetHourApp/"
        rel="noopener noreferrer"
        target="_blank"
        ><i class="bx bxl-instagram"></i></a
      ><a
        href="https://www.linkedin.com/company/meethour/"
        rel="noopener noreferrer"
        target="_blank"
        ><i class="bx bxl-linkedin"></i></a
      ><a
        href=" https://www.youtube.com/channel/UCbB-py_vQt2lXJ1VMMVIH7w/"
        rel="noopener noreferrer"
        target="_blank"
        ><i class="bx bxl-youtube"></i
      ></a>
    </div>
  </div>
</footer>

<a href="#" class="back-to-top"><i class="icofont-simple-up"></i></a>

<div id="preloader" style="background-color: rgb(51 54 102)"></div>

<!-- Vendor JS Files -->
<script src="static/assets/vendor/jquery/jquery.min.js"></script>
<!-- Template Main JS File -->
<script src="static/assets/js/main.js"></script>

<script type="text/javascript">
  $(document).ready(function () {
    var url = window.location.href;
    //alert(url);
    splitUrl = url.split("?");
    substr = splitUrl[1];

    if (typeof substr != "undefined" && substr == "appview=1") {
      $("#mylink").attr("href", "javascript:void();");
      $("#mylink2").attr("href", "javascript:void();");

      document.getElementById("hiddenmenu").style.display = "none";
      document.getElementById("footer").style.display = "none";
      document.getElementById("backtotop").style.display = "none";
    }
  });
</script>

<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/js-sha256@0.9.0/src/sha256.min.js"></script>
<script
  id="Config"
  src="https://meet.groupvi.systems/libs/v2.4.6/config.js?apiKey=b709af18554cee13bd74d8ab20aad74a4c6d46bd520ede88b54144b40e4ff3a8"
></script>
<script src="./login.js?v=01032023"></script>
<script type="text/javascript">
  var Tawk_API = Tawk_API || {},
    Tawk_LoadStart = new Date();
  (function () {
    var s1 = document.createElement("script"),
      s0 = document.getElementsByTagName("script")[0];
    s1.async = true;
    s1.src = "https://embed.tawk.to/5f37c4564c7806354da69806/default";
    s1.charset = "UTF-8";
    s1.setAttribute("crossorigin", "*");
    s0.parentNode.insertBefore(s1, s0);
  })();
</script>
<!-- Google tag (gtag.js) -->
<script
  async
  src="https://www.googletagmanager.com/gtag/js?id=G-ZH1DZQP6BE"
></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag("js", new Date());

  gtag("config", "G-ZH1DZQP6BE");
</script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script
  async
  src="https://www.googletagmanager.com/gtag/js?id=GA4-166053466-1"
></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag("js", new Date());
  gtag("config", "GA4-166053466-1");
</script>
<!-- Global site tag (gtag.js) - Google Ads: 480457597 -->
<script
  async
  src="https://www.googletagmanager.com/gtag/js?id=AW-480457597"
></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag("js", new Date());
  gtag("config", "AW-480457597");
</script>
<script
  async
  src="https://www.googletagmanager.com/gtag/js?id=AW-480457597"
></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag("js", new Date());
  gtag("config", "AW-480457597");
</script>
<script type="text/javascript">
  _linkedin_partner_id = "2578138";
  window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
  window._linkedin_data_partner_ids.push(_linkedin_partner_id);
</script>
<script type="text/javascript">
  (function () {
    var s = document.getElementsByTagName("script")[0];
    var b = document.createElement("script");
    b.type = "text/javascript";
    b.async = true;
    b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
    s.parentNode.insertBefore(b, s);
  })();
</script>
<noscript>
  <img
    height="1"
    width="1"
    style="display: none"
    alt=""
    src="https://px.ads.linkedin.com/collect/?pid=2578138&fmt=gif"
  />
</noscript>
