/* eslint-disable no-unused-vars, no-var, max-len */
/* eslint sort-keys: ["error", "asc", {"caseSensitive": false}] */

var interfaceConfig = {
    APP_NAME: 'Meet Hour',
    applyMeetingSettings: false, // To apply Conference UI settings on iframe from this page -  https://meet.groupvi.systems/customer/ui_settings
    AUDIO_LEVEL_PRIMARY_COLOR: 'rgba(255,255,255,0.4)',
    AUDIO_LEVEL_SECONDARY_COLOR: 'rgba(255,255,255,0.2)',


    /**
     * A UX mode where the last screen share participant is automatically
     * pinned. Valid values are the string "remote-only" so remote participants
     * get pinned but not local, otherwise any truthy value for all participants,
     * and any falsy value to disable the feature.
     *
     * Note: this mode is experimental and subject to breakage.
     */
    AUTO_PIN_LATEST_SCREEN_SHARE: 'remote-only',

    /**
     * Watermark Background.
     */
    BRAND_WATERMARK_BACKGROUND: 'rgba(93, 118, 155,0.6)',

    BRAND_WATERMARK_LINK: 'https://shell.groupvi.systems',

    /**
     * Change Moderator text in Lobby and Waiting Room page text.
     */
    CHANGE_MODERATOR_NAME: 'Moderator',


    CLOSE_PAGE_GUEST_HINT: false, // A html text to be shown to guests on the close page, false disables it
    /**
     * Whether the connection indicator icon should hide itself based on
     * connection strength. If true, the connection indicator will remain
     * displayed while the participant has a weak connection and will hide
     * itself after the CONNECTION_INDICATOR_HIDE_TIMEOUT when the connection is
     * strong.
     *
     * @type {boolean}
     */
    CONNECTION_INDICATOR_AUTO_HIDE_ENABLED: true,

    /**
     * How long the connection indicator should remain displayed before hiding.
     * Used in conjunction with CONNECTION_INDICATOR_AUTOHIDE_ENABLED.
     *
     * @type {number}
     */
    CONNECTION_INDICATOR_AUTO_HIDE_TIMEOUT: 5000,

    /**
     * If true, hides the connection indicators completely.
     *
     * @type {boolean}
     */
    CONNECTION_INDICATOR_DISABLED: true,

    DEFAULT_BACKGROUND: '#474747',
    DEFAULT_LOCAL_DISPLAY_NAME: 'me',
    DEFAULT_LOGO_URL: 'images/GroupVI-logo.png',
    DEFAULT_REMOTE_DISPLAY_NAME: 'Guest',
    DEFAULT_WELCOME_PAGE_LOGO_URL: 'images/GroupVI-logo.png',

    DISABLE_DOMINANT_SPEAKER_INDICATOR: false,

    DISABLE_FOCUS_INDICATOR: false,

    /**
     * If true, notifications regarding joining/leaving are no longer displayed.
     */
    DISABLE_JOIN_LEAVE_NOTIFICATIONS: false,

    /**
     * If true, presence status: busy, calling, connected etc. Is not displayed.
     */
    DISABLE_PRESENCE_STATUS: false,

    /**
     * Whether the ringing sound in the call/ring overlay is disabled. If
     * {@code undefined}, defaults to {@code false}.
     *
     * @type {boolean}
     */
    DISABLE_RINGING: false,

    /**
     * Whether the speech to text transcription subtitles panel is disabled.
     * If {@code undefined}, defaults to {@code false}.
     *
     * @type {boolean}
     */
    DISABLE_TRANSCRIPTION_SUBTITLES: false,

    /**
     * Whether or not the blurred video background for large video should be
     * displayed on browsers that can support it.
     */
    DISABLE_VIDEO_BACKGROUND: false,

    DISPLAY_WELCOME_FOOTER: true,
    DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD: false,
    DISPLAY_WELCOME_PAGE_CONTENT: true,
    DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT: true,

    ENABLE_DESKTOP_DEEPLINK: true,
    ENABLE_DIAL_OUT: true,
    ENABLE_FACEBOOK_LIVESTREAM: false,
    ENABLE_FEEDBACK_ANIMATION: true, // Enables feedback star animation.

    ENABLE_INSTAGRAM_LIVESTREAM: false,
    FILM_STRIP_MAX_HEIGHT: 120,

    GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false,

    /**
     * Hide the logo on the deep linking pages.
     */
    HIDE_DEEP_LINKING_LOGO: false,


    /**
     * Hide the invite prompt in the header when alone in the meeting.
     */
    HIDE_INVITE_MORE_HEADER: false,

    INITIAL_TOOLBAR_TIMEOUT: 20000,

    LANG_DETECTION: true, // Allow i18n to detect the system language
    LIVE_STREAM_TOOLS: [
        {
            enabled: true,
            Facebook: {
                apiLogin: false,
                enabled: true


            },

            icon: 'IconFacebook',
            title: 'Facebook',
            toggle: false


        },
        {
            enabled: true,
            icon: 'IconYoutube',
            title: 'Youtube',
            toggle: false,
            YouTube: {
                apiLogin: false,
                enabled: true
            }
        },
        {
            enabled: true,
            icon: 'IconTwitch',
            title: 'Twitch',
            toggle: false,
            Twitch: {
                apiLogin: false,
                enabled: true

            }
        },
        {
            enabled: true,
            icon: 'IconLinkedin',
            LinkedIn: {
                apiLogin: false,
                enabled: true

            },
            title: 'LinkedIn',
            toggle: false
        },
        {
            enabled: true,
            icon: 'IconInstagram',
            Instagram: {
                apiLogin: false,
                enabled: true
            },
            title: 'Instagram',
            toggle: false


        },
        {
            CustomRtmp: {
                apiLogin: false,
                enabled: true
            },

            enabled: true,
            icon: 'IconCustom',
            title: 'Custom RTMP',
            toggle: false


        },
        {
            CustomRtmps: {
                apiLogin: false,
                enabled: true
            },
            enabled: true,
            icon: 'IconCustom',
            title: 'Custom RTMPS',
            toggle: false


        },
        {
            enabled: true,
            icon: 'IconVimeo',
            title: 'Vimeo',
            toggle: false,
            Vimeo: {
                apiLogin: false,
                enabled: false

            }
        }
    ],
    LIVE_STREAMING_HELP_LINK: 'https://jitsi.org/live', // Documentation reference for the live streaming feature.
    LOCAL_THUMBNAIL_RATIO: 16 / 9, // 16:9

    /**
     * Maximum coefficient of the ratio of the large video to the visible area
     * after the large video is scaled to fit the window.
     *
     * @type {number}
     */
    MAXIMUM_ZOOMING_COEFFICIENT: 1.3,

    MEET_HOUR_WATERMARK_LINK: 'https://shell.groupvi.systems',

    /**
     * Whether the mobile app Meet Hour is to be promoted to participants
     * attempting to join a conference in a mobile Web browser. If
     * {@code undefined}, defaults to {@code true}.
     *
     * @type {boolean}
     */
    MOBILE_APP_PROMO: true,

    /**
     * Specify custom URL for downloading android mobile app.
     */
    MOBILE_DOWNLOAD_LINK_ANDROID: 'https://play.google.com/store/apps/details?id=meet.groupvi.systems',

    /**
     * Specify custom URL for downloading f droid app.
     */
    MOBILE_DOWNLOAD_LINK_F_DROID: 'https://f-droid.org/en/packages/meet.groupvi.systems/',

    /**
     * Specify URL for downloading ios mobile app.
     */
    MOBILE_DOWNLOAD_LINK_IOS: 'https://apps.apple.com/in/app/meet-hour/id1527001689',

    NATIVE_APP_NAME: 'Meet Hour',

    // Names of browsers which should show a warning stating the current browser
    // has a suboptimal experience. Browsers which are not listed as optimal or
    // unsupported are considered suboptimal. Valid values are:
    // chrome, chromium, edge, electron, firefox, nwjs, opera, safari
    OPTIMAL_BROWSERS: [ 'chrome', 'chromium', 'firefox', 'nwjs', 'electron', 'safari' ],

    POLICY_LOGO: null,
    PROVIDER_NAME: 'Meet Hour',

    /** .........
     * If true, will display recent list
     *
     * @type {boolean}
     */
    RECENT_LIST_ENABLED: true,
    RECORDING_TOOLS: [
        {
            DropBox: {
                apiLogin: false,
                enabled: true


            },
            enabled: true,
            icon: 'IconRec',
            title: 'DropBox Recording',
            toggle: false


        }
    ],
    REMOTE_THUMBNAIL_RATIO: 1, // 1:1

    SETTINGS_SECTIONS: [ 'devices', 'language', 'moderator', 'profile', 'calendar' ],
    SHOW_BRAND_WATERMARK: false,

    /**
    * Decides whether the chrome extension banner should be rendered on the landing page and during the meeting.
    * If this is set to false, the banner will not be rendered at all. If set to true, the check for extension(s)
    * being already installed is done before rendering.
    */
    SHOW_CHROME_EXTENSION_BANNER: false,

    SHOW_DEEP_LINKING_IMAGE: false,
    SHOW_MEET_HOUR_WATERMARK: true,
    SHOW_POWERED_BY: false,
    SHOW_PROMOTIONAL_CLOSE_PAGE: false,

    /*
     * If indicated some of the error dialogs may point to the support URL for
     * help.
     */
    SUPPORT_URL: 'https://community.jitsi.org/',

    TOOLBAR_ALWAYS_VISIBLE: false,

    /**
     * The name of the toolbar buttons to display in the toolbar, including the
     * "More actions" menu. If present, the button will display. Exceptions are
     * "livestreaming" and "recording" which also require being a moderator and
     * some values in config.js to be enabled. Also, the "profile" button will
     * not display for users with a JWT.
     * Notes:
     * - it's impossible to choose which buttons go in the "More actions" menu
     * - it's impossible to control the placement of buttons
     * - 'desktop' controls the "Share your screen" button.
     */
    TOOLBAR_BUTTONS: [
        'microphone',
        'camera',
        'closedcaptions',
        'desktop',
        'fullscreen',
        'fodeviceselection',
        'hangup',
        'profile',
        'chat',
        'recording',
        'livestreaming',
        'participants-pane',
        'etherpad',
        'genericiframe',
        'sharedvideo',
        'settings',
        'raisehand',
        'videoquality',
        'filmstrip',
        'invite',
        'feedback',
        'stats',
        'shortcuts',
        'tileview',
        'select-background',
        'download',
        'help',
        'mute-everyone',
        'security',
        'zoomin',
        'zoomout',
        'embedmeeting',
        'adddonorbox',
        'removedonorbox',
        'voicecommand'
    ],

    TOOLBAR_TIMEOUT: 4000,

    // Browsers, in addition to those which do not fully support WebRTC, that
    // are not supported and should show the unsupported browser page.
    UNSUPPORTED_BROWSERS: [],

    /**
     * Whether to show thumbnails in filmstrip as a column instead of as a row.
     */
    VERTICAL_FILMSTRIP: true,

    // Determines how the video would fit the screen. 'both' would fit the whole
    // screen, 'height' would fit the original video height to the height of the
    // screen, 'width' would fit the original video width to the width of the
    // screen respecting ratio.
    VIDEO_LAYOUT_FIT: 'both',

    /**
     * If true, hides the video quality label indicating the resolution status
     * of the current large video.
     *
     * @type {boolean}
     */
    VIDEO_QUALITY_LABEL_DISABLED: false,

    /**
     * How many columns the tile view can expand to. The respected range is
     * between 1 and 5.
     */
    // TILE_VIEW_MAX_COLUMNS: 5,

    /**
     * To enable Mobile Browser.
     */
    // eslint-disable-next-line sort-keys
    ENABLE_MOBILE_BROWSER: false,

    /**
     * Specify Firebase dynamic link properties for the mobile apps.
     */
    // MOBILE_DYNAMIC_LINK: {
    //     APN: 'meet.groupvi.systems',
    //     APP_CODE: 'meethour-apps',
    //     CUSTOM_DOMAIN: undefined,
    //     IBI: 'meet.groupvi.systems',
    //     ISI: '1527001689'
    // },

    /**
     * Specify mobile app scheme for opening the app from the mobile browser.
     */
    // eslint-disable-next-line sort-keys
    APP_SCHEME: 'meet.groupvi.systems',

    /**
     * Specify the Android app package name.
     */
    // eslint-disable-next-line sort-keys
    ANDROID_APP_PACKAGE: 'meet.groupvi.systems',

    // prjoin footer
    disablePrejoinFooter: false,


    // prejoin header
    disablePrejoinHeader: false,


    /**
     * Generic IFrame to be used for any application you like. You can name the menu entries in "/lang/main.json"
     * The link supports template strings for:
     *  - {room} - The room id
     *  - {lang} - The ISO Language tag.
     */
    genericIFrameTemplateUrl: 'https://wbo.shell.groupvi.systems/#room=meethour-{room},p6jFUzzS3QiMXr2ELVvXfQ',

    /**
     * Override the behavior of some notifications to remain displayed until
     * explicitly dismissed through a user action. The value is how long, in
     * milliseconds, those notifications should remain displayed.
     */
    // ENFORCE_NOTIFICATION_AUTO_DISMISS_TIMEOUT: 15000,

    // List of undocumented settings
    /**
     INDICATOR_FONT_SIZES
     PHONE_NUMBER_REGEX.
    */

    // meethour login for iframe users
    showJoinAsModerator: false,

    // Allow all above example options to include a trailing comma and
    // prevent fear when commenting out the last value.
    // eslint-disable-next-line sort-keys
    makeJsonParserHappy: 'even if last key had a trailing comma'

    // No configuration value should follow this line.
};


/* eslint-enable no-unused-vars, no-var, max-len */
