<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Group VI Companion Extension</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CLKComplicationPrincipalClass</key>
	<string>$(PRODUCT_MODULE_NAME).ComplicationController</string>
	<key>CLKComplicationSupportedFamilies</key>
	<array>
		<string>CLKComplicationFamilyGraphicCorner</string>
		<string>CLKComplicationFamilyGraphicBezel</string>
		<string>CLKComplicationFamilyGraphicCircular</string>
		<string>CLKComplicationFamilyGraphicRectangular</string>
	</array>
	<key>NSExtension</key>
	<dict>
		<key>NSExtensionAttributes</key>
		<dict>
			<key>WKAppBundleIdentifier</key>
			<string>meet.groupvi.systems.watchkit</string>
		</dict>
		<key>NSExtensionPointIdentifier</key>
		<string>com.apple.watchkit</string>
	</dict>
	<key>WKExtensionDelegateClassName</key>
	<string>$(PRODUCT_MODULE_NAME).ExtensionDelegate</string>
</dict>
</plist>
