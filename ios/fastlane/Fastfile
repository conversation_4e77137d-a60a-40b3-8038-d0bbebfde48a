ENV["FASTLANE_SKIP_UPDATE_CHECK"] = "1"
opt_out_usage

default_platform(:ios)

platform :ios do
  desc "Push a new beta build to TestFlight"
  lane :deploy do
    # Make sure we are on a clean tree
    ensure_git_status_clean

    # Set the app identifier
    update_app_identifier(
      xcodeproj: "app/app.xcodeproj",
      plist_path: "src/Info.plist",
      app_identifier: "meet.groupvi.systems"
    )

    # Set the broadcast extension identifier
    update_app_identifier(
      xcodeproj: "app/app.xcodeproj",
      plist_path: "broadcast-extension/Info.plist",
      app_identifier: "meet.groupvi.systems.ScreenRecording"
    )
    update_info_plist(
      xcodeproj: "app/app.xcodeproj",
      plist_path: "src/Info.plist",
      block: proc do |plist|
        plist["RTCScreenSharingExtension"] = "meet.groupvi.systems.BroadcastExtension"
      end
    )

    # Set the (watch) app identifier
    update_app_identifier(
      xcodeproj: "app/app.xcodeproj",
      plist_path: "watchos/app/Info.plist",
      app_identifier: "meet.groupvi.systems.watchkit"
    )

    # Set the (watch) extension identifier
    update_app_identifier(
      xcodeproj: "app/app.xcodeproj",
      plist_path: "watchos/extension/Info.plist",
      app_identifier: "meet.groupvi.systems.watchkit.extension"
    )

    update_info_plist(
      xcodeproj: "app/app.xcodeproj",
      plist_path: "watchos/app/Info.plist",
      block: proc do |plist|
        plist["WKCompanionAppBundleIdentifier"] = "meet.groupvi.systems"
      end
    )

    update_info_plist(
      xcodeproj: "app/app.xcodeproj",
      plist_path: "watchos/extension/Info.plist",
      block: proc do |plist|
        plist["NSExtension"]["NSExtensionAttributes"]["WKAppBundleIdentifier"] = "meet.groupvi.systems.watchkit"
      end
    )

    # Inrement the build number by 1
    increment_build_number(
      build_number: latest_testflight_build_number + 1,
      xcodeproj: "app/app.xcodeproj"
    )

    # Actually build the app
    build_app(
        scheme: "meet-hour",
        include_bitcode: true,
        include_symbols: true,
        export_xcargs: "-allowProvisioningUpdates"
    )

    # Upload the build to TestFlight (but don't distribute it)
    upload_to_testflight(
      beta_app_description: ENV["JITSI_CHANGELOG"],
      beta_app_feedback_email: ENV["JITSI_REVIEW_EMAIL"],
      beta_app_review_info: {
        contact_email: ENV["JITSI_REVIEW_EMAIL"],
        contact_first_name: ENV["JITSI_REVIEW_NAME"],
        contact_last_name: ENV["JITSI_REVIEW_SURNAME"],
        contact_phone: ENV["JITSI_REVIEW_PHONE"],
        demo_account_name: ENV["JITSI_DEMO_ACCOUNT"],
        demo_account_password: ENV["JITSI_DEMO_PASSWORD"],
      },
      changelog: ENV["JITSI_CHANGELOG"],
      demo_account_required: false,
      distribute_external: true,
      groups: ENV["JITSI_BETA_TESTING_GROUPS"],
      reject_build_waiting_for_review: true,
      uses_non_exempt_encryption: false
    )

    # Upload dSYMs to Crashlytics
    download_dsyms
    upload_symbols_to_crashlytics

    # Cleanup
    clean_build_artifacts
    reset_git_repo(skip_clean: true)
  end
end
