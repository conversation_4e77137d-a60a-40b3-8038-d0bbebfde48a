{"addPeople": {"add": "Inviter", "addContacts": "Invitez vos contacts", "contacts": "Contacts", "copyInvite": "Copie de l'invitation à la réunion", "copyLink": "Copier le lien de la réunion", "copyStream": "Copier le lien de diffusion en direct", "countryNotSupported": "Nous ne prenons pas encore en charge cette destination.", "countryReminder": "Vous appelez en dehors des États-Unis ? Assurez-vous de commencer par l'indicatif du pays !", "defaultEmail": "Votre e-mail par défaut", "disabled": "Vous ne pouvez pas inviter des gens.", "doYouWantToRemoveThisPerson": "Voulez-vous supprimer cette personne", "failedToAdd": "Impossible d'ajouter des participants", "footerText": "La numérotation sortante est désactivée.", "googleCalendar": "Google Agenda", "googleEmail": "Courriel <PERSON>", "inviteMoreHeader": "Vous êtes le seul à la réunion", "inviteMoreMailSubject": "Rejoindre la réunion {{appName}}", "inviteMorePrompt": "Inviter plus de personnes", "linkCopied": "Lien copié dans le presse-papiers", "loading": "Recherche de personnes et de numéros de téléphone", "loadingNumber": "Validation du numéro de téléphone", "loadingPeople": "À la recherche de personnes à inviter", "loadingText": "Chargement...", "noResults": "Aucun résultat de recherche correspondant", "noValidNumbers": "Veuillez entrer un numéro de téléphone", "outlookEmail": "Courrier électronique Outlook", "phoneNumbers": "numéros de téléphone", "searchNumbers": "Ajouter des numéros de téléphone", "searchPeople": "Rechercher des personnes", "searchPeopleAndNumbers": "Recherchez des personnes ou ajoutez leurs numéros de téléphone", "searching": "Recherche...", "sendWhatsa[pp": "Whatsapp", "shareInvite": "Partager l'invitation à la réunion", "shareInviteP": "Partager l'invitation à la réunion avec le mot de passe", "shareLink": "Partagez le lien de la réunion pour inviter d'autres personnes", "shareStream": "Partagez le lien de diffusion en direct", "sipAddresses": "adresses SIP", "telephone": "Numéro de téléphone}}", "title": "Inviter des personnes à cette réunion", "yahooEmail": "<PERSON><PERSON><PERSON>"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "Casque audio", "none": "Aucun périphérique audio disponible", "phone": "Téléphone", "speaker": "Conférencier"}, "audioOnly": {"audioOnly": "Faible bande passante"}, "breakoutRooms": {"actions": {"add": "Ajouter une salle de répartition (Beta)", "autoAssign": "Attribuer automatiquement aux salles de répartition", "close": "<PERSON><PERSON><PERSON>", "join": "Rejoindre", "leaveBreakoutRoom": "<PERSON><PERSON><PERSON> la salle de répartition", "more": "Plus", "remove": "<PERSON><PERSON><PERSON><PERSON>", "rename": "<PERSON>mmer", "renameBreakoutRoom": "Renommer la salle de répartition", "sendToBreakoutRoom": "Envoyer un participant à :"}, "breakoutList": "Liste des salles de répartition", "buttonLabel": "Salles de répartition", "defaultName": "Salle de répartition #{{index}}", "hideParticipantList": "Masquer la liste des participants", "mainRoom": "Salle principale", "notifications": {"joined": "Rejoint la salle de répartition \"{{name}}\"", "joinedMainRoom": "Rejoint la salle principale", "joinedTitle": "Salles de répartition"}, "showParticipantList": "Afficher la liste des participants", "showParticipants": "Montrer aux participants", "title": "Salles de répartition"}, "calendarSync": {"addMeetingURL": "Ajouter un lien de réunion", "confirmAddLink": "Souhaitez-vous ajouter un lien Rencontrer l'heure à cet événement ?", "error": {"appConfiguration": "L'intégration du calendrier n'est pas correctement configurée.", "generic": "Une erreur s'est produite. Veuillez vérifier les paramètres de votre calendrier ou essayer d'actualiser le calendrier.", "notSignedIn": "Une erreur s'est produite lors de l'authentification pour voir les événements du calendrier. Veuillez vérifier les paramètres de votre calendrier et réessayer de vous connecter."}, "join": "Rejoindre", "joinTooltip": "Rejoignez la réunion", "nextMeeting": "prochaine réunion", "noEvents": "Il n'y a aucun événement à venir prévu.", "ongoingMeeting": "réunion en cours", "permissionButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "permissionMessage": "L'autorisation Calendrier est requise pour voir vos réunions dans l'application.", "refresh": "Actualiser le calendrier", "today": "<PERSON><PERSON><PERSON>'hui"}, "carmode": {"actions": {"selectSoundDevice": "Sélectionner le périphérique audio"}, "labels": {"buttonLabel": "Mode voiture", "title": "Mode voiture", "videoStopped": "Votre vidéo est arrêtée"}}, "chat": {"enter": "Entrer dans la salle de discussion", "error": "Erreur : votre message n'a pas été envoyé. <PERSON><PERSON> : {{error}}", "fieldPlaceHolder": "Tapez votre message ici", "message": "Message", "messageAccessibleTitle": "{{user}} dit :", "messageAccessibleTitleMe": "moi je dis :", "messageTo": "Message privé à {{recipient}}", "messagebox": "Tapez un message", "nickname": {"popover": "Choisissez un surnom", "title": "Entrez un pseudo pour utiliser le chat"}, "noMessagesMessage": "Il n'y a pas encore de messages dans la réunion. Commencez une conversation ici !", "privateNotice": "Message privé à {{recipient}}", "smileysPanel": "Panneau d'émojis", "tabs": {"chat": "Cha<PERSON>", "polls": "Sondages"}, "title": "Chat et sondages", "titleWithPolls": "Chat et sondages", "you": "toi"}, "chromeExtensionBanner": {"buttonText": "Installer l'extension Chrome", "close": "<PERSON><PERSON><PERSON>", "dontShowAgain": "Ne me montre plus ça", "installExtensionText": "Installer l'extension pour l'intégration de Google Calendar et Office 365"}, "clickandpledge": {"errorDesc": "Veuillez saisir un GUID Click and Pledge Connect valide. Pour plus de détails, visitez - https://connect.clickandpledge.com/", "errorNotification": "GUID de clic et de promesse non valide", "title": "Paramètres de don C&P Connect", "titlenative": "Cliquez et engagez"}, "connectingOverlay": {"joiningRoom": "Je vous connecte à votre réunion..."}, "connection": {"ATTACHED": "Ci-joint", "AUTHENTICATING": "Authentification", "AUTHFAIL": "L'authentification a échoué", "CONNECTED": "Connecté", "CONNECTING": "De liaison", "CONNFAIL": "La connexion a échoué", "DISCONNECTED": "Déconnecté", "DISCONNECTING": "Déconnexion", "ERROR": "<PERSON><PERSON><PERSON>", "FETCH_SESSION_ID": "Obtention de l'ID de session...", "GET_SESSION_ID_ERROR": "Obt<PERSON>r l'erreur d'ID de session : {{code}}", "GOT_SESSION_ID": "Obtention de l'identifiant de session... Terminé", "LOW_BANDWIDTH": "La vidéo pour {{displayName}} a été désactivée pour économiser la bande passante"}, "connectionindicator": {"address": "<PERSON><PERSON><PERSON>:", "audio_ssrc": "SSRC audio :", "bandwidth": "Bande passante estimée :", "bitrate": "Débit binaire :", "bridgeCount": "Nombre de serveurs :", "codecs": "Codecs (A/V) :", "connectedTo": "Connecté à :", "e2e_rtt": "E2E RTT :", "framerate": "Fréquence d'images :", "less": "Affiche<PERSON> moins", "localaddress": "Adresse locale:", "localaddress_plural": "Adresses locales :", "localport": "Port local:", "localport_plural": "Ports locaux :", "maxEnabledResolution": "envoyer max", "more": "Afficher plus", "packetloss": "<PERSON><PERSON> :", "participant_id": "Identifiant du participant :", "quality": {"good": "Bien", "inactive": "Inactif", "lost": "Perdu", "nonoptimal": "Non optimal", "poor": "<PERSON><PERSON><PERSON>"}, "remoteaddress": "<PERSON><PERSON><PERSON> distante :", "remoteaddress_plural": "Adresses distantes :", "remoteport": "Port distant :", "remoteport_plural": "Ports distants :", "resolution": "Résolution:", "savelogs": "Enregistrer les journaux", "status": "Connexion:", "transport": "Transport:", "transport_plural": "Transports:", "video_ssrc": "Vidéo SSRC :"}, "dateUtils": {"earlier": "Plus tôt", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er"}, "deepLinking": {"appNotInstalled": "Utilisez notre application mobile {{app}} pour rejoindre cette réunion sur votre téléphone.", "continueWithBrowser": "Continuer avec le navigateur", "description": "<PERSON>ien ne s'est passé ? Nous avons essayé de lancer votre réunion dans l'application de bureau {{app}}. <PERSON><PERSON><PERSON><PERSON> ou lancez-la dans l'application Web {{app}}.", "descriptionWithoutWeb": "Rien ne s'est passé ? Nous avons essayé de lancer votre réunion dans l'application de bureau {{app}}.", "downloadApp": "Téléchargez l'application", "ifDoNotHaveApp": "Si vous n'avez pas encore l'application :", "ifHaveApp": "Si vous avez déjà l'application :", "ifYouDontHaveTheAppYet": "Si vous n'avez pas encore l'application", "joinInApp": "Rejoignez cette réunion en utilisant l'application", "joinMeetingWithDesktopApp": "Rejoindre une réunion avec l'application de bureau", "launchMeetingInDesktopApp": "Lancer la réunion dans l'application de bureau", "launchWebButton": "Lancement sur le Web", "title": "Lancement de votre réunion dans {{app}}...", "tryAgainButton": "Réessayer sur le bureau"}, "defaultLink": "par exemple {{url}}", "defaultNickname": "ex. <PERSON>", "deviceError": {"cameraError": "Impossible d'accéder à votre caméra", "cameraPermission": "Erreur lors de l'obtention de l'autorisation de la caméra", "microphoneError": "Impossible d'accéder à votre microphone", "microphonePermission": "Erreur lors de l'obtention de l'autorisation du microphone"}, "deviceSelection": {"noPermission": "Permission non accordée", "previewUnavailable": "Aperçu indisponible", "selectADevice": "Sélectionnez un appareil", "testAudio": "Jouer un son de test"}, "dialOut": {"statusMessage": "est maintenant {{status}}"}, "dialog": {"Back": "<PERSON><PERSON>", "Cancel": "Annuler", "IamHost": "Je suis l'hôte", "Ok": "D'ACCORD", "Remove": "<PERSON><PERSON><PERSON>", "Share": "Partager", "Submit": "So<PERSON><PERSON><PERSON>", "WaitForHostMsg": "La conférence <b>{{room}}</b> n'a pas encore commencé. Si vous êtes l'hôte, veuillez vous authentifier. Sinon, veuillez attendre l'arrivée de l'hôte.", "WaitForHostMsgWOk": "La conférence <b>{{room}}</b> n'a pas encore commencé. Si vous êtes l'hôte, veuillez appuyer sur OK pour vous authentifier. Sinon, veuillez attendre l'arrivée de l'hôte.", "WaitforModerator": "Veuillez attendre l'arrivée du modérateur", "WaitforModeratorOk": "<PERSON><PERSON><PERSON>", "WaitingForHost": "En attendant l'hôte...", "WaitingForHostTitle": "En attendant l'hôte...", "Yes": "O<PERSON>", "accessibilityLabel": {"liveStreaming": "Diffusion en direct"}, "add": "Ajouter", "allow": "Permettre", "alreadySharedVideoMsg": "Un autre participant partage déjà une vidéo. Cette conférence n'autorise qu'une seule vidéo partagée à la fois.", "alreadySharedVideoTitle": "Une seule vidéo partagée est autorisée à la fois", "applicationWindow": "Fenêtre d'application", "authenticationRequired": "Authentification requise", "cameraConstraintFailedError": "Votre appareil photo ne répond pas à certaines des contraintes requises.", "cameraNotFoundError": "La caméra n'a pas été trouvée.", "cameraNotSendingData": "Nous ne pouvons pas accéder à votre caméra. Veuillez vérifier si une autre application utilise cet appareil, sélectionnez un autre appareil dans le menu des paramètres ou essayez de recharger l'application.", "cameraNotSendingDataTitle": "Impossible d'accéder à la caméra", "cameraPermissionDeniedError": "Vous n'avez pas autorisé l'utilisation de votre caméra. Vous pouvez toujours participer à la conférence, mais les autres ne vous verront pas. Utilisez le bouton de la caméra dans la barre d'adresse pour résoudre ce problème.", "cameraTimeoutError": "Impossible de démarrer la source vidéo. Un dépassement de délai s'est produit !", "cameraUnknownError": "Impossible d'utiliser l'appareil photo pour une raison inconnue.", "cameraUnsupportedResolutionError": "Votre appareil photo ne prend pas en charge la résolution vidéo requise.", "cannotToggleScreenSharingNotSupported": "Impossible d'activer le partage d'écran : non pris en charge.", "close": "<PERSON><PERSON><PERSON>", "closingAllTerminals": "Fermeture de tous les terminaux", "conferenceDisconnectMsg": "Vous devriez peut-être vérifier votre connexion réseau. Reconnexion dans {{seconds}} secondes...", "conferenceDisconnectTitle": "Vous avez été déconnecté.", "conferenceReloadMsg": "Nous essayons de résoudre ce problème. Reconnexion dans {{seconds}} secondes...", "conferenceReloadTitle": "Malheureusement, quelque chose s'est mal passé.", "confirm": "Confirmer", "confirmNo": "Non", "confirmYes": "O<PERSON>", "connectError": "Oups ! Une erreur s'est produite et nous n'avons pas pu nous connecter à la conférence.", "connectErrorWithMsg": "Oups ! Une erreur s'est produite et nous n'avons pas pu nous connecter à la conférence : {{msg}}", "connecting": "De liaison", "contactSupport": "<PERSON>er le support", "copied": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "customAwsRecording": "Enregistrement AWS personnalisé", "deleteCache": "Supprimer le cache", "dismiss": "<PERSON><PERSON><PERSON>", "displayNameRequired": "Salut ! Comment t'appelles-tu ?", "displayUserName": "", "donationCNPLabel": "Entrez l'URL du formulaire de connexion ou l'URL du widget", "donationCNotificationTitle": "Faites un don via <PERSON>lick and <PERSON>ledge", "donationLabel": "Entrez l'URL de la campagne de dons", "donationNotificationDescription": "Faites un don pour soutenir notre cause", "donationNotificationTitle": "Faire un don via Donorbox", "done": "Fait", "e2eeDescription": "Le chiffrement de bout en bout est actuellement EXPÉRIMENTAL. Veuillez noter que l'activation du chiffrement de bout en bout désactivera les services fournis côté serveur tels que : l'enregistrement, la diffusion en direct et la participation par téléphone. N'oubliez pas non plus que la réunion ne fonctionnera que pour les personnes qui se joignent à la réunion à partir de navigateurs prenant en charge les flux insérables.", "e2eeLabel": "Activer le chiffrement de bout en bout", "e2eeWarning": "AVERTISSEMENT : tous les participants à cette réunion ne semblent pas prendre en charge le chiffrement de bout en bout. Si vous l'activez, ils ne pourront ni vous voir ni vous entendre.", "embedMeeting": "Intégrer la réunion", "enterCdonation": "Exemple : https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "ID de courrier électronique", "enterDisplayName": "Nom et prénom", "enterDisplayNameToJoin": "Veuillez entrer votre nom pour rejoindre", "enterDonation": "Exemple : https://donorbox.org/faire-un-don-à-une-organisation", "enterMeetingId": "Entrez l'ID de la réunion", "enterMeetingPassword": "Entrez le mot de passe de la réunion", "error": "<PERSON><PERSON><PERSON>", "errorMeetingID": "Ajouter un identifiant de réunion", "errorMeetingPassword": "Ajouter un mot de passe pour la réunion", "forceMuteEveryoneDialog": "Êtes-vous sûr de vouloir verrouiller le microphone de tout le monde, sauf des modérateurs ? Ils ne pourront pas déverrouiller le microphone eux-mêmes, mais une fois que vous aurez annulé la désactivation forcée du son, ils pourront réactiver le son et parler.", "forceMuteEveryoneElseDialog": "Mettez-les en sourdine et désactivez leur microphone.", "forceMuteEveryoneElseTitle": "Forcer la mise en sourdine de tout le monde sauf {{whom}} ?", "forceMuteEveryoneElsesVideoDialog": "Une fois la caméra désactivée, ils ne pourront pas activer leur caméra", "forceMuteEveryoneElsesVideoTitle": "Forcer la mise en sourdine de la caméra de tout le monde sauf {{whom}} ?", "forceMuteEveryoneSelf": "toi-même", "forceMuteEveryoneStartMuted": "Tout le monde commence à forcer le silence à partir de maintenant", "forceMuteEveryoneTitle": "Forcer la mise en sourdine de tout le monde ?", "forceMuteEveryonesVideoDialog": "Etes-vous sûr de vouloir verrouiller la vidéo de ce participant ? Il/elle ne pourra pas déverrouiller la vidéo", "forceMuteEveryonesVideoTitle": "Forcer la mise en sourdine de la vidéo de tout le monde ?", "forceMuteParticipantBody": "Forcer le participant à se mettre en sourdine.", "forceMuteParticipantButton": "Forcer la mise en sourdine", "forceMuteParticipantDialog": "Etes-vous sûr de vouloir verrouiller le microphone de ce participant ? Il/elle ne pourra pas déverrouiller le microphone, mais une fois que vous aurez annulé la mise en sourdine forcée, il/elle pourra réactiver le microphone et parler.", "forceMuteParticipantTitle": "Forcer la mise en sourdine de ce participant ?", "forceMuteParticipantsVideoBody": "La vidéo des participants sera désactivée et ils ne pourront plus la réactiver.", "forceMuteParticipantsVideoButton": "Désactiver la caméra", "forceMuteParticipantsVideoTitle": "Désactiver la caméra de ce participant ?", "gracefulShutdown": "Notre service est actuellement en maintenance. Veuillez réessayer ultérieurement.", "grantModeratorDialog": "Etes-vous sûr de vouloir faire de ce participant un modérateur ?", "grantModeratorTitle": "Modérateur de subventions", "guestUser": "Invi<PERSON>", "hangUpLeaveReason": "Cette réunion a été clôturée par le modérateur", "hideShareAudioHelper": "Ne plus afficher cette boîte de dialogue", "incorrectPassword": "Nom d'utilisateur ou mot de passe incorrect", "incorrectRoomLockPassword": "Mot de passe incorrect", "internalError": "Oups ! Une erreur s'est produite. L'erreur suivante s'est produite : {{error}}", "internalErrorTitle": "E<PERSON>ur interne", "kickMessage": "Aïe ! Vous avez été exclu de la rencontre !", "kickParticipantButton": "Supprimer l'utilisateur", "kickParticipantDialog": "Etes-vous sûr de vouloir supprimer ce participant ?", "kickParticipantTitle": "Supprimer ce participant ?", "kickTitle": "Aïe ! Vous avez été exclu de la réunion", "liveStreaming": "Diffusion en direct", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "Impossible lorsque l'enregistrement est actif", "liveStreamingDisabledForGuestTooltip": "Les invités ne peuvent pas démarrer la diffusion en direct.", "liveStreamingDisabledTooltip": "Démarrer la diffusion en direct désactivé.", "localUserControls": "Contrôles utilisateur locaux", "lockMessage": "Impossible de verrouiller la conférence.", "lockRoom": "Ajouter une réunion $t(lockRoomPasswordUppercase)", "lockTitle": "Échec du verrouillage", "login": "Se connecter", "logoutQuestion": "Êtes-vous sûr de vouloir vous déconnecter et arrêter la conférence ?", "logoutTitle": "Déconnexion", "maxUsersLimitReached": "Le nombre maximum de participants a été atteint. La conférence est complète. Veuillez contacter le responsable de la réunion ou réessayer plus tard !", "maxUsersLimitReachedTitle": "Limite maximale de participants atteinte", "meetHourRecording": "Enregistrement de l'heure de la rencontre", "meetingID": "ID de la réunion", "meetingIDandPassword": "Entrez l'identifiant de la réunion et le mot de passe", "meetingPassword": "Mot de passe de la réunion", "messageErrorApi": "Oups ! Quelque chose s'est mal passé", "messageErrorInvalid": "Informations d'identification non valides", "messageErrorNotModerator": "Oups ! vous n'êtes pas modérateur de cette réunion", "messageErrorNull": "Le nom d'utilisateur ou le mot de passe est vide", "micConstraintFailedError": "Votre microphone ne répond pas à certaines des contraintes requises.", "micNotFoundError": "Le microphone n'a pas été trouvé.", "micNotSendingData": "Accédez aux paramètres de votre ordinateur pour désactiver votre micro et régler son niveau", "micNotSendingDataTitle": "Votre micro est coupé par les paramètres de votre système", "micPermissionDeniedError": "Vous n'avez pas autorisé l'utilisation de votre microphone. Vous pouvez toujours participer à la conférence, mais les autres ne vous entendront pas. Utilisez le bouton de l'appareil photo dans la barre d'adresse pour résoudre ce problème.", "micTimeoutError": "Impossible de démarrer la source audio. Un dépassement de délai s'est produit !", "micUnknownError": "Impossible d'utiliser le microphone pour une raison inconnue.", "muteEveryoneDialog": "Êtes-vous sûr de vouloir désactiver le son de tout le monde ? Vous ne pourrez pas les réactiver, mais ils pourront se réactiver eux-mêmes à tout moment.", "muteEveryoneElseDialog": "Une fois désactivés, vous ne pourrez plus les réactiver, mais ils pourront se réactiver eux-mêmes à tout moment.", "muteEveryoneElseTitle": "<PERSON>tre tout le monde en sourdine sauf {{whom}} ?", "muteEveryoneElsesVideoDialog": "Une fois la caméra désactivée, vous ne pourrez plus la rallumer, mais vous pourrez la rallumer à tout moment.", "muteEveryoneElsesVideoTitle": "Désactiver la caméra de tout le monde sauf {{whom}} ?", "muteEveryoneSelf": "toi-même", "muteEveryoneStartMuted": "Tout le monde commence à être muet à partir de maintenant", "muteEveryoneTitle": "Mettre tout le monde en sourdine ?", "muteEveryonesVideoDialog": "Êtes-vous sûr de vouloir désactiver la caméra de tout le monde ? Vous ne pourrez pas la réactiver, mais ils pourront la réactiver à tout moment.", "muteEveryonesVideoDialogOk": "Désactiver", "muteEveryonesVideoTitle": "Désactiver la caméra de tout le monde ?", "muteParticipantBody": "Vous ne pourrez pas les réactiver, mais ils pourront se réactiver eux-mêmes à tout moment.", "muteParticipantButton": "<PERSON><PERSON>", "muteParticipantDialog": "Êtes-vous sûr de vouloir désactiver le son de ce participant ? Vous ne pourrez pas le réactiver, mais il pourra le réactiver lui-même à tout moment.", "muteParticipantTitle": "Couper le son de ce participant ?", "muteParticipantsVideoBody": "Vous ne pourrez pas rallumer la caméra, mais ils pourront la rallumer à tout moment.", "muteParticipantsVideoButton": "Désactiver la caméra", "muteParticipantsVideoDialog": "Êtes-vous sûr de vouloir désactiver la caméra de ce participant ? Vous ne pourrez pas réactiver la caméra, mais il pourra la réactiver à tout moment.", "muteParticipantsVideoTitle": "Désactiver la caméra de ce participant ?", "noDropboxToken": "Aucun jeton Dropbox valide", "noScreensharingInAudioOnlyMode": "Pas de partage d'écran en mode audio uniquement", "password": "Mot de passe", "passwordLabel": "La réunion a été verrouillée par un modérateur. Veuillez saisir le $t(lockRoomPassword) pour rejoindre la réunion.", "passwordNotSupported": "La définition d'une réunion $t(lockRoomPassword) n'est pas prise en charge.", "passwordNotSupportedTitle": "$t(lockRoomPasswordUppercase) non pris en charge", "passwordRequired": "$t(lockRoomPasswordUppercase) requis", "permissionCameraRequiredError": "L'autorisation de la caméra est requise pour participer à des conférences avec vidéo. Veuillez l'accorder dans les paramètres", "permissionErrorTitle": "Permission requise", "permissionMicRequiredError": "L'autorisation du microphone est requise pour participer à des conférences avec audio. Veuillez l'accorder dans les paramètres", "popupError": "Votre navigateur bloque les fenêtres pop-up de ce site. Veuillez activer les fenêtres pop-up dans les paramètres de sécurité de votre navigateur et réessayer.", "popupErrorTitle": "Pop-up bloqué", "readMore": "plus", "recording": "Enregistrement", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "Impossible lorsqu'un flux en direct est actif", "recordingDisabledForGuestTooltip": "Les invités ne peuvent pas démarrer les enregistrements.", "recordingDisabledTooltip": "Démarrer l'enregistrement désactivé.", "rejoinNow": "Rejoignez-nous maintenant", "remoteControlAllowedMessage": "{{user}} a accepté votre demande de contrôle à distance !", "remoteControlDeniedMessage": "{{user}} a rejeté votre demande de contrôle à distance !", "remoteControlErrorMessage": "Une erreur s'est produite lors de la tentative de demande d'autorisations de contrôle à distance à {{user}} !", "remoteControlRequestMessage": "Autoriserez-vous {{user}} à contrôler à distance votre bureau ?", "remoteControlShareScreenWarning": "Notez que si vous appuyez sur « Autoriser », vous partagerez votre écran !", "remoteControlStopMessage": "La séance de contrôle à distance est terminée !", "remoteControlTitle": "Contrôle du bureau à distance", "remoteUserControls": "Contrôles utilisateur à distance de {{username}}", "removeCDonation": "Le guide Click and Pledge a été supprimé", "removeCDonationD": "Le lien de don a été supprimé avec succès", "removeDonation": "Lien de don Donorbox supprimé", "removeDonationD": "Le lien de don a été supprimé avec succès", "removePassword": "Supprimer $t(lockRoomPassword)", "removeSharedVideoMsg": "Êtes-vous sûr de vouloir supprimer votre vidéo partagée ?", "removeSharedVideoTitle": "Supprimer la vidéo partagée", "reservationError": "Erreur du système de réservation", "reservationErrorMsg": "Code d'erreur : {{code}}, message : {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "Redémarrage initié en raison d'une défaillance du pont", "retry": "<PERSON><PERSON><PERSON><PERSON>", "revokeModeration": "Révoquer l'utilisateur en tant que modérateur ?", "revokeModerationTitle": "Révoquer la modération", "screenSharingAudio": "Partager l'audio", "screenSharingFailed": "Oups ! Une erreur s'est produite, nous n'avons pas pu démarrer le partage d'écran !", "screenSharingFailedTitle": "Le partage d’écran a échoué !", "screenSharingPermissionDeniedError": "Oups ! Une erreur s'est produite avec vos autorisations de partage d'écran. Veuillez recharger et réessayer.", "screenSharingUser": "{{displayName}} partage actuellement l'écran", "sendPrivateMessage": "Vous avez récemment reçu un message privé. Aviez-vous l'intention d'y répondre en privé ou souhaitiez-vous envoyer votre message au groupe ?", "sendPrivateMessageCancel": "Envoyer au groupe", "sendPrivateMessageOk": "Envoyer en privé", "sendPrivateMessageTitle": "Envoyer en privé ?", "serviceUnavailable": "Service non disponible", "sessTerminated": "<PERSON><PERSON> terminé", "sessionRestarted": "Appel relancé par le pont", "shareAudio": "<PERSON><PERSON><PERSON>", "shareAudioTitle": "Comment partager de l'audio", "shareAudioWarningD1": "vous devez arrêter le partage d'écran avant de partager votre audio.", "shareAudioWarningD2": "vous devez redémarrer votre partage d'écran et cocher l'option « partager l'audio ».", "shareAudioWarningH1": "Si vous souhaitez partager uniquement l'audio :", "shareAudioWarningTitle": "<PERSON><PERSON> de<PERSON> arrêter le partage d'écran avant de partager l'audio", "shareMediaWarningGenericH2": "Si vous souhaitez partager votre écran et votre audio", "shareScreenWarningD1": "vous devez arrêter le partage audio avant de partager votre écran.", "shareScreenWarningD2": "vous devez arrêter le partage audio, d<PERSON><PERSON><PERSON> le partage d'écran et cocher l'option « partager l'audio ».", "shareScreenWarningH1": "Si vous souhaitez partager uniquement votre écran :", "shareScreenWarningTitle": "V<PERSON> devez arrêter le partage audio avant de partager votre écran", "shareVideoLinkError": "Veuillez fournir un lien YouTube correct.", "shareVideoTitle": "Partager Youtube", "shareYourScreen": "Partagez votre écran", "shareYourScreenDisabled": "Partage d'écran désactivé.", "shareYourScreenDisabledForGuest": "Les invités ne peuvent pas partager leur écran.", "startLiveStreaming": "Diffusion en direct + enregistrement", "startRecording": "Commencer l'enregistrement", "startRemoteControlErrorMessage": "Une erreur s'est produite lors de la tentative de démarrage de la session de contrôle à distance !", "stopLiveStreaming": "<PERSON><PERSON><PERSON><PERSON>", "stopRecording": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "stopRecordingWarning": "Êtes-vous sûr de vouloir arrêter l’enregistrement ?", "stopStreamingWarning": "Êtes-vous sûr de vouloir arrêter la diffusion en direct ?", "streamKey": "Clé de diffusion en direct", "switchInProgress": "Commutation en cours.", "thankYou": "Merci d'utiliser {{appName}} !", "token": "jeton", "tokenAuthFailed": "<PERSON><PERSON><PERSON><PERSON>, vous n'êtes pas autorisé à participer à cet appel.", "tokenAuthFailedTitle": "L'authentification a échoué", "transcribing": "Transcription", "unforceMuteEveryoneDialog": "Êtes-vous sûr de vouloir déverrouiller le microphone de tout le monde ? Ils peuvent se désactiver et parler.", "unforceMuteEveryoneElseDialog": "Annuler les désactiver et leur permettre d'activer leur microphone", "unforceMuteEveryoneElseTitle": "Annuler la mise en sourdine forcée de tout le monde sauf {{whom}} ?", "unforceMuteEveryoneElsesVideoDialog": "Une fois la caméra activée, ils pourront activer leur caméra", "unforceMuteEveryoneElsesVideoTitle": "Activer la caméra de tout le monde sauf {{whom}} ?", "unforceMuteEveryoneSelf": "toi-même", "unforceMuteEveryoneTitle": "Annuler la coupure forcée du microphone de tout le monde ?", "unforceMuteEveryonesVideoDialog": "Êtes-vous sûr de vouloir déverrouiller la vidéo de tout le monde ?", "unforceMuteEveryonesVideoTitle": "Activer la caméra de tout le monde ?", "unforceMuteParticipantBody": "Ann<PERSON>r le mode muet du participant.", "unforceMuteParticipantButton": "Annuler la fonction Forcer la coupure du son", "unforceMuteParticipantDialog": "Êtes-vous sûr de vouloir déverrouiller la vidéo de ce participant ?.", "unforceMuteParticipantTitle": "Annuler la mise en sourdine forcée de ce participant ?", "unforceMuteParticipantsVideoBody": "La vidéo des participants sera activée et ils pourront la réactiver à nouveau", "unforceMuteParticipantsVideoButton": "Activer la caméra", "unforceMuteParticipantsVideoTitle": "Déverrouiller la vidéo de ce participant ?", "unlockRoom": "Supprimer la réunion $t(lockRoomPassword)", "user": "Utilisa<PERSON>ur", "userIdentifier": "Identifiant de l'utilisateur", "userPassword": "Mot de passe utilisateur", "videoLink": "<PERSON>n vid<PERSON>o", "viewUpgradeOptions": "Afficher les options de mise à niveau", "viewUpgradeOptionsContent": "Pour obtenir un accès illimité aux fonctionnalités premium telles que l'enregistrement, les transcriptions, le streaming RTMP et plus encore, vous devrez mettre à niveau votre forfait.", "viewUpgradeOptionsTitle": "Vous avez découvert une fonctionnalité premium !", "yourEntireScreen": "Tout votre écran"}, "documentSharing": {"title": "Livepad"}, "donorbox": {"errorDesc": "Veuillez saisir une URL de campagne Donorbox valide. Pour plus de détails, visitez le site www.donorbox.org", "errorNotification": "URL de boîte de dons non valide", "title": "Ajouter l'URL de la campagne DonorBox", "titlenative": "BOÎTE DE DONATEURS"}, "e2ee": {"labelToolTip": "Les communications audio et vidéo de cet appel sont cryptées de bout en bout"}, "embedMeeting": {"title": "Intégrer cette réunion"}, "feedback": {"average": "<PERSON><PERSON><PERSON>", "bad": "<PERSON><PERSON><PERSON><PERSON>", "detailsLabel": "Parlez-nous-en davantage.", "good": "Bien", "rateExperience": "Évaluez votre expérience de réunion", "star": "<PERSON><PERSON><PERSON>", "veryBad": "<PERSON><PERSON><PERSON>", "veryGood": "Très bien"}, "giphy": {"giphy": "<PERSON><PERSON><PERSON><PERSON>", "noResults": "Aucun résultat trouvé :(", "search": "Rechercher sur GIPHY"}, "helpView": {"header": "Centre d'aide"}, "incomingCall": {"answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "audioCallTitle": "A<PERSON> entrant", "decline": "<PERSON><PERSON><PERSON>", "productLabel": "de l'heure de rencontre", "videoCallTitle": "<PERSON><PERSON> vidéo entrant"}, "info": {"accessibilityLabel": "Afficher les informations", "addPassword": "Ajouter $t(lockRoomPassword)", "cancelPassword": "Annuler $t(lockRoomPassword)", "conferenceURL": "Lien:", "copyNumber": "Numéro de copie", "country": "Pays", "dialANumber": "Pour rejoindre votre réunion, composez l’un de ces numéros, puis entrez le code PIN.", "dialInConferenceID": "ÉPINGLE:", "dialInNotSupported": "Désolé, la connexion n'est actuellement pas prise en charge.", "dialInNumber": "Numérotation :", "dialInSummaryError": "Erreur lors de la récupération des informations de connexion. Veuillez réessayer plus tard.", "dialInTollFree": "Numéro sans frais", "genericError": "<PERSON><PERSON>, quel<PERSON> chose s'est mal passé.", "inviteLiveStream": "Pour visionner la retransmission en direct de cette réunion, cliquez sur ce lien : {{url}}", "invitePhone": "Pour rejoindre la réunion par téléphone, appuyez sur ceci : {{number}},,{{conferenceID}}#", "invitePhoneAlternatives": "Vous recherchez un autre numéro de téléphone pour participer ?\n\nVoir les numéros de téléphone pour participer à la réunion : {{url}}\n\nSi vous vous connectez également via un téléphone de salle, rejoignez la réunion sans vous connecter à l'audio : {{silentUrl}}", "inviteSipEndpoint": "Pour vous connecter en utilisant l'adresse SIP, saisissez ceci : {{sipUri}}", "inviteTextiOSInviteUrl": "Cliquez sur le lien suivant pour rejoindre : {{inviteUrl}}.", "inviteTextiOSJoinSilent": "Si vous vous connectez via un téléphone de salle, utilisez ce lien pour vous connecter sans vous connecter à l'audio : {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} vous invite à une réunion.", "inviteTextiOSPhone": "Pour vous joindre par téléphone, utilisez ce numéro : {{number}},,{{conferenceID}}#. Si vous recherchez un autre numéro, voici la liste complète : {{didUrl}}.", "inviteURLFirstPartGeneral": "Vous êtes invité à participer à une réunion.", "inviteURLFirstPartPersonal": "{{name}} vous invite à une réunion.", "inviteURLSecondPart": "Rejo<PERSON>ez la réunion :\n{{url}}", "label": "Informations de connexion", "liveStreamURL": "Diffusion en direct :", "moreNumbers": "Plus de chiffres", "noNumbers": "Aucun numéro d'appel.", "noPassword": "Aucun", "noRoom": "Aucune salle n'a été spécifiée pour se connecter.", "numbers": "Numé<PERSON>'<PERSON>", "password": "$t(lockRoomPasswordUppercase) :", "sip": "Adresse SIP", "title": "Partager", "tooltip": "Partager le lien et les informations de connexion pour cette réunion"}, "inlineDialogFailure": {"msg": "Nous avons un peu trébuché.", "retry": "Essayer à nouveau", "support": "<PERSON><PERSON><PERSON>", "supportMsg": "Si cela continue à se produire, contactez"}, "inviteDialog": {"alertText": "Impossible d'inviter certains participants.", "header": "Inviter", "searchCallOnlyPlaceholder": "Entrez le numéro de téléphone", "searchPeopleOnlyPlaceholder": "Recherche de participants", "searchPlaceholder": "Participant ou numéro de téléphone", "send": "Envoyer"}, "jitsiHome": "{{logo}} Logo, liens vers la page d'accueil", "keyboardShortcuts": {"focusLocal": "Concentrez-vous sur votre vidéo", "focusRemote": "Se concentrer sur la vidéo d'une autre personne", "fullScreen": "<PERSON><PERSON><PERSON><PERSON> ou quitter le plein écran", "keyboardShortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "localRecording": "A<PERSON>iche<PERSON> ou masquer les commandes d'enregistrement locales", "mute": "<PERSON><PERSON> ou dés<PERSON>r votre microphone", "pushToTalk": "Appuyez pour parler", "raiseHand": "<PERSON>er ou baisser la main", "showSpeakerStats": "Afficher les statistiques des intervenants", "toggleChat": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou fermer le chat", "toggleFilmstrip": "Afficher ou masquer les miniatures des vidéos", "toggleParticipantsPane": "A<PERSON><PERSON>r ou masquer le volet des participants", "toggleScreensharing": "Basculer entre la caméra et le partage d'écran", "toggleShortcuts": "A<PERSON><PERSON><PERSON> ou masquer les raccourcis clavier", "videoMute": "<PERSON><PERSON><PERSON><PERSON> ou arrêter votre camé<PERSON>", "videoQuality": "<PERSON><PERSON>rer la qualité des appels"}, "liveChatView": {"header": "Assistance en direct 24h/24 et 7j/7"}, "liveStreaming": {"addStream": "Ajouter une destination", "busy": "Nous travaillons à libérer des ressources de streaming. Veuillez réessayer dans quelques minutes.", "busyTitle": "Tous les streamers sont actuellement occupés", "changeSignIn": "Changer de compte.", "choose": "Choisissez un flux en direct", "chooseCTA": "Choisissez une option de diffusion. Vous êtes actuellement connecté en tant que {{email}}.", "enterLinkedInUrlWithTheKey": "Entrez l'URL LinkedIn avec la clé", "enterStreamKey": "Saisissez ici votre clé de diffusion en direct YouTube.", "enterStreamKeyFacebook": "Saisissez ici votre clé de diffusion en direct Facebook.", "enterStreamKeyInstagram": "Saisissez ici votre clé de diffusion en direct Instagram.", "enterStreamKeyYouTube": "Saisissez ici votre clé de diffusion en direct {{youtube}}.", "error": "La diffusion en direct a échoué. Veuillez réessayer.", "errorAPI": "Une erreur s'est produite lors de l'accès à vos diffusions YouTube. Veuillez réessayer de vous connecter.", "errorLiveStreamNotEnabled": "La diffusion en direct n'est pas activée sur {{email}}. Veuillez activer la diffusion en direct ou vous connecter à un compte avec la diffusion en direct activée.", "expandedOff": "La diffusion en direct s'est arrêtée", "expandedOn": "La réunion est actuellement diffusée sur YouTube.", "expandedPending": "La diffusion en direct est en cours de démarrage...", "failToStartAutoLiveStreaming": "Impossible de démarrer la diffusion en direct automatique", "failToStartAutoRecording": "Impossible de démarrer l'enregistrement automatique", "failedToStart": "La diffusion en direct n'a pas pu dé<PERSON>rer", "getStreamKeyManually": "Nous n'avons pas pu récupérer de diffusion en direct. Essayez d'obtenir votre clé de diffusion en direct sur YouTube.", "googlePrivacyPolicy": "Politique de confidentialité de Google", "invalidStreamKey": "La clé de diffusion en direct peut être incorrecte.", "limitNotificationDescriptionNative": "Votre streaming sera limité à {{limit}} min. Pour un streaming illimité, essayez {{app}}.", "limitNotificationDescriptionWeb": "En raison d'une forte demande, votre streaming sera limité à {{limit}} minutes. Pour un streaming illimité, essayez <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "Assurez-vous que vous disposez de suffisamment d'espace de stockage disponible sur votre compte.", "note": "Note", "off": "La diffusion en direct est arrêtée", "offBy": "{{name}} a arrêté la diffusion en direct", "on": "La diffusion en direct a commencé", "onBy": "{{name}} a démarré la diffusion en direct", "pending": "Démarrage de la diffusion en direct...", "pleaseContactSupportForAssistance": "<PERSON>euillez contacter le support pour obtenir de l'aide.", "serviceName": "Service de diffusion en direct", "signIn": "Connectez-vous avec Google", "signInCTA": "Connectez-vous ou entrez votre clé de diffusion en direct depuis YouTube.", "signOut": "se déconnecter", "signedInAs": "Vous êtes actuellement connecté en tant que :", "start": "Enregistrement + diffusion en direct", "startService": "Démarrer le service", "streamIdHelp": "Qu'est-ce que c'est ça?", "unavailableTitle": "Diffusion en direct indisponible", "youtubeTerms": "Conditions d'utilisation de YouTube"}, "lobby": {"admit": "Admettre", "admitAll": "<PERSON><PERSON><PERSON> tout", "allow": "Permettre", "backToKnockModeButton": "Pas de mot de passe, demandez à rejoindre à la place", "dialogTitle": "Mode lobby", "disableDialogContent": "Le mode lobby est actuellement activé. Cette fonctionnalité garantit que les participants indésirables ne peuvent pas rejoindre votre réunion. Voulez-vous le désactiver ?", "disableDialogSubmit": "Désactiver", "emailField": "Entrez votre adresse email", "enableDialogPasswordField": "Définir un mot de passe (facultatif)", "enableDialogSubmit": "Activer", "enableDialogText": "Le mode Lobby vous permet de protéger votre réunion en autorisant uniquement les personnes à entrer après l'approbation formelle d'un modérateur.", "enterPasswordButton": "Entrez le mot de passe de la réunion", "enterPasswordTitle": "Entrez le mot de passe pour rejoindre la réunion", "invalidPassword": "Mot de passe invalide", "joinRejectedMessage": "Votre demande d'adhésion a été rejetée par un modérateur.", "joinTitle": "Rejoindre la réunion", "joinWithPasswordMessage": "Tentative de connexion avec mot de passe, veuillez patienter...", "joiningMessage": "Vous rejoindrez la réunion dès que quelqu'un aura accepté votre demande", "joiningTitle": "Demande de participation à la réunion...", "joiningWithPasswordTitle": "Rejoindre avec un mot de passe...", "knockButton": "<PERSON><PERSON><PERSON> à rejoindre", "knockTitle": "Quelqu'un veut rejoindre la réunion", "knockingParticipantList": "Liste des participants au Knocking", "nameField": "Entrez votre nom", "notificationLobbyAccessDenied": "{{targetParticipantName}} a été refusé par {{originParticipantName}}", "notificationLobbyAccessGranted": "{{targetParticipantName}} a été autorisé à rejoindre par {{originParticipantName}}", "notificationLobbyDisabled": "Le lobby a été désactivé par {{originParticipantName}}", "notificationLobbyEnabled": "Le lobby a été activé par {{originParticipantName}}", "notificationTitle": "Hall d'entrée", "passwordField": "Entrez le mot de passe de la réunion", "passwordJoinButton": "Rejoindre", "reject": "<PERSON><PERSON><PERSON>", "rejectAll": "<PERSON><PERSON><PERSON> tout", "toggleLabel": "Activer le <PERSON>"}, "localRecording": {"clientState": {"off": "Désactivé", "on": "Sur", "unknown": "Inconnu"}, "dialogTitle": "Contrôles d'enregistrement locaux", "duration": "<PERSON><PERSON><PERSON>", "durationNA": "N / A", "encoding": "Codage", "label": "<PERSON>r", "labelToolTip": "L'enregistrement local est activé", "localRecording": "Enregistrement local", "me": "<PERSON><PERSON>", "messages": {"engaged": "Enregistrement local activé.", "finished": "La session d'enregistrement {{token}} est terminée. Veuillez envoyer le fichier enregistré au modérateur.", "finishedModerator": "La session d'enregistrement {{token}} est terminée. L'enregistrement de la piste locale a été sauvegardé. Veuillez demander aux autres participants de soumettre leurs enregistrements.", "notModerator": "Vous n'êtes pas le modérateur. Vous ne pouvez pas démarrer ou arrêter l'enregistrement local."}, "moderator": "Mod<PERSON><PERSON>ur", "no": "Non", "participant": "Participant", "participantStats": "Statistiques des participants", "sessionToken": "Jeton de session", "start": "Commencer l'enregistrement", "stop": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "yes": "O<PERSON>"}, "lockRoomPassword": "mot de passe", "lockRoomPasswordUppercase": "Mot de passe", "lonelyMeetingExperience": {"button": "Inviter d'autres personnes", "youAreAlone": "Vous êtes le seul à la réunion"}, "me": "moi", "notify": {"OldElectronAPPTitle": "Vulnérabilité de sécurité !", "connectedOneMember": "{{name}} a rejoint la réunion", "connectedThreePlusMembers": "{{name}} et {{count}} autres personnes ont rejoint la réunion", "connectedTwoMembers": "{{first}} et {{second}} ont rejoint la réunion", "disconnected": "déconnecté", "focus": "Thèmes de la conférence", "focusFail": "{{component}} non disponible - réessayez dans {{ms}} s", "grantedTo": "Droits de modérateur accordés à {{to}}!", "groupTitle": "Notifications", "hostAskedUnmute": "L'hôte souhaite que vous réactiviez le son", "invitedOneMember": "{{name}} a <PERSON><PERSON> invité", "invitedThreePlusMembers": "{{name}} et {{count}} autres personnes ont été invitées", "invitedTwoMembers": "{{first}} et {{second}} ont été invités", "kickParticipant": "{{kicked}} a été supprimé par {{kicker}}", "me": "<PERSON><PERSON>", "moderationInEffectCSDescription": "Veuillez lever la main si vous souhaitez partager votre vidéo", "moderationInEffectCSTitle": "Le partage de contenu est désactivé par le modérateur", "moderationInEffectDescription": "Veuillez lever la main si vous souhaitez parler.", "moderationInEffectTitle": "Le micro est coupé par le modérateur", "moderationInEffectVideoDescription": "Veuillez lever la main si vous souhaitez que votre vidéo soit visible", "moderationInEffectVideoTitle": "La vidéo est coupée par le modérateur", "moderationRequestFromModerator": "L'hôte souhaite que vous réactiviez le son", "moderationRequestFromParticipant": "<PERSON><PERSON><PERSON> parler", "moderationStartedTitle": "La modération a commencé", "moderationStoppedTitle": "La modération est arrêtée", "moderationToggleDescription": "par {{participantDisplayName}}", "moderator": "Droits de modérateur accordés !", "muted": "Vous avez démarré la conversation en mode muet.", "mutedRemotelyDescription": "Vous pouvez toujours réactiver le son lorsque vous êtes prêt à parler. Réactivez le son lorsque vous avez terminé pour éviter tout bruit pendant la réunion.", "mutedRemotelyTitle": "Vous avez été mis en sourdine par {{participantDisplayName}} !", "mutedTitle": "Vous êtes en sourdine !", "newDeviceAction": "Utiliser", "newDeviceAudioTitle": "Nouveau périphérique audio détecté", "newDeviceCameraTitle": "Nouvelle caméra détectée", "oldElectronClientDescription1": "Il semble que vous utilisiez une ancienne version du client Rencontrer l'heure qui présente des vulnérabilités de sécurité connues. Veuillez vous assurer de mettre à jour notre", "oldElectronClientDescription2": "dernière version", "oldElectronClientDescription3": "maintenant!", "passwordRemovedRemotely": "$t(lockRoomPasswordUppercase) supprimé par un autre participant", "passwordSetRemotely": "$t(lockRoomPasswordUppercase) défini par un autre participant", "raiseHandAction": "Lever la main", "raisedHand": "{{name}} aimerait prendre la <PERSON>.", "reactionSounds": "D<PERSON><PERSON>r les sons", "reactionSoundsForAll": "<PERSON><PERSON><PERSON><PERSON> les sons pour tous", "screenShareNoAudio": "La case Partager l'audio n'était pas cochée dans l'écran de sélection de la fenêtre.", "screenShareNoAudioTitle": "Impossible de partager l'audio du système !", "somebody": "Quelqu'un", "startSilentDescription": "Rejoindre la réunion pour activer l'audio", "startSilentTitle": "V<PERSON> avez rejoint sans sortie audio !", "suboptimalBrowserWarning": "Nous craignons que votre expérience de réunion ne soit pas des plus agréables ici. Nous cherchons des moyens d'améliorer cela, mais en attendant, veuil<PERSON>z essayer d'utiliser l'un des <a href='{{recommendedBrowserPageLink}}' target='_blank'>navigateurs entièrement pris en charge</a>.", "suboptimalExperienceTitle": "Avertissement du navigateur", "unmute": "<PERSON><PERSON> le son", "videoMutedRemotelyDescription": "Vous pouvez toujours le rallumer.", "videoMutedRemotelyTitle": "Votre caméra a été désactivée par {{participantDisplayName}} !"}, "participantsPane": {"actions": {"allow": "Permettre aux participants de :", "askUnmute": "<PERSON><PERSON><PERSON> à ré<PERSON>r le son", "blockEveryoneMicCamera": "<PERSON><PERSON>quez le micro et la caméra de tout le monde", "breakoutRooms": "Salles d'évasion", "forceMute": "Forcer la coupure du son", "forceMuteAll": "Forcer la mise en sourdine de tous", "forceMuteAllVideo": "Forcer la mise en sourdine de toutes les vidéos", "forceMuteEveryoneElse": "Forcer la mise en sourdine de tous les autres", "forceMuteEveryoneElseVideo": "Vidéo sur la mise en sourdine forcée de tous les autres", "forceMuteVideo": "Forcer la mise en sourdine de la vidéo", "invite": "Inviter quelqu'un", "mute": "<PERSON><PERSON>", "muteAll": "Tout couper", "muteEveryoneElse": "Couper le son de tout le monde", "startModeration": "<PERSON>r le son ou démarrer la vidéo", "stopEveryonesVideo": "<PERSON><PERSON><PERSON><PERSON>z la vidéo de tout le monde", "stopVideo": "<PERSON><PERSON><PERSON><PERSON> la vidéo", "unForceMute": "annuler forcer la coupure du son", "unForceMuteAll": "Annuler la mise en sourdine forcée de tous", "unForceMuteVideo": "Annuler la mise en sourdine forcée de la vidéo", "unblockEveryoneMicCamera": "<PERSON><PERSON><PERSON><PERSON>quez le micro et la caméra de tout le monde", "unforceMuteAllVideo": "Annuler la fonction Forcer la mise en sourdine de toutes les vidéos", "unforceMuteEveryoneElse": "Annuler la mise en sourdine forcée de tous les autres", "unforceMuteEveryoneElseVideo": "Annuler la mise en sourdine forcée de tout le monde Vidéo"}, "close": "<PERSON><PERSON><PERSON>", "header": "Participants", "headings": {"lobby": "Hall d'entrée ({{count}})", "participantsList": "Participants à la réunion ({{count}})", "waitingLobby": "En attente dans le hall ({{count}})"}, "search": "Rechercher des participants"}, "passwordDigitsOnly": "Jus<PERSON><PERSON>'à {{number}} chiffres", "passwordSetRemotely": "défini par un autre participant", "polls": {"answer": {"skip": "Sauter", "submit": "So<PERSON><PERSON><PERSON>"}, "by": "Par {{ name }}", "create": {"addOption": "Ajouter une option", "answerPlaceholder": "Option {{index}}", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON> un sondage", "pollOption": "Option de sondage {{index}}", "pollQuestion": "Question du sondage", "questionPlaceholder": "Poser une question", "removeOption": "Supprimer l'option", "send": "Envoyer"}, "notification": {"description": "Ouvrez l'onglet Sondages pour voter", "title": "Un nouveau sondage a été ajouté à cette réunion"}, "results": {"changeVote": "Changer le vote", "empty": "Il n'y a pas encore de sondages dans la réunion. Lancez un sondage ici !", "hideDetailedResults": "Masquer les détails", "showDetailedResults": "Aff<PERSON>r les détails", "vote": "Voter"}}, "poweredby": "© Rencontrer l'heure LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "Une conférence se déroule déjà en arrière-plan.", "audioAndVideoError": "Erreur audio et vidéo :", "audioDeviceProblem": "Il y a un problème avec votre appareil audio", "audioOnlyError": "Erreur audio :", "audioTrackError": "Impossible de créer une piste audio.", "callMe": "<PERSON><PERSON><PERSON><PERSON>moi", "callMeAtNumber": "Appelez-moi à ce numéro :", "calling": "<PERSON><PERSON>", "configuringDevices": "Configuration des appareils...", "connectedWithAudioQ": "Vous êtes connecté à l’audio ?", "connection": {"good": "Votre connexion Internet semble bonne !", "nonOptimal": "Votre connexion Internet n'est pas optimale", "poor": "Vous avez une mauvaise connexion Internet"}, "connectionDetails": {"audioClipping": "Nous nous attendons à ce que votre audio soit coupé.", "audioHighQuality": "Nous attendons de votre audio une excellente qualité.", "audioLowNoVideo": "Nous nous attendons à ce que la qualité audio soit faible et qu'il n'y ait pas de vidéo.", "goodQuality": "Génial ! La qualité de vos médias sera excellente.", "noMediaConnectivity": "Nous n'avons pas pu trouver de moyen d'établir une connectivité multimédia pour ce test. Cela est généralement dû à un pare-feu ou à un NAT.", "noVideo": "Nous nous attendons à ce que votre vidéo soit terrible.", "undetectable": "Si vous ne parvenez toujours pas à passer des appels dans votre navigateur, nous vous recommandons de vérifier que vos haut-parleurs, votre microphone et votre caméra sont correctement configurés, que vous avez accordé à votre navigateur les droits d'utilisation de votre microphone et de votre caméra et que la version de votre navigateur est à jour. Si vous rencontrez toujours des problèmes pour passer des appels, vous devez contacter le développeur de l'application Web.", "veryPoorConnection": "Nous nous attendons à ce que la qualité de vos appels soit vraiment terrible.", "videoFreezing": "Nous nous attendons à ce que votre vidéo se fige, devienne noire et soit pixelisée.", "videoHighQuality": "Nous espérons que votre vidéo sera de bonne qualité.", "videoLowQuality": "Nous nous attendons à ce que votre vidéo ait une faible qualité en termes de fréquence d'images et de résolution.", "videoTearing": "Nous nous attendons à ce que votre vidéo soit pixelisée ou comporte des artefacts visuels."}, "copyAndShare": "Copier et partager le lien de la réunion", "dashboard": "Tableau de bord", "daysAgo": "Il y a {{daysCount}} jours", "dialInMeeting": "Connectez-vous à la réunion", "dialInPin": "Connectez-vous à la réunion et entrez le code PIN :", "dialing": "Numérotation", "doNotShow": "Ne plus afficher cet écran", "enterMeetingIdOrLink": "Entrez l'identifiant ou le lien de la réunion", "errorDialOut": "Impossible de composer le numéro", "errorDialOutDisconnected": "Impossible de composer le numéro. Déconnecté", "errorDialOutFailed": "Impossible de composer le numéro. L'appel a échoué", "errorDialOutStatus": "Erreur lors de l'obtention du statut de numérotation sortante", "errorMissingEmail": "Veuillez entrer votre email pour rejoindre la réunion", "errorMissingName": "Veuillez entrer votre nom pour rejoindre la réunion", "errorNameLength": "Veuillez saisir au moins 3 lettres dans votre nom", "errorStatusCode": "<PERSON><PERSON>ur lors de la numérotation, code d'état : {{status}}", "errorValidation": "La validation du numéro a échoué", "features": "Caractéristiques", "guestNotAllowedMsg": "Les invités ne sont pas autorisés à participer à cette réunion", "iWantToDialIn": "Je veux appeler", "initiated": "Appel initié", "invalidEmail": "<PERSON><PERSON> invalide", "joinAMeeting": "Rejoindre une réunion", "joinAudioByPhone": "Rejoignez avec l'audio du téléphone", "joinMeeting": "Rejoindre la réunion", "joinMeetingGuest": "Rejoindre la réunion en tant qu'invité", "joinWithoutAudio": "Rejoignez sans audio", "keyboardShortcuts": "<PERSON><PERSON> les rac<PERSON> clavier", "linkCopied": "Lien copié dans le presse-papiers", "logout": "Déconnexion", "lookGood": "Il semble que votre microphone fonctionne correctement", "maximumAllowedParticipantsErr": "Le nombre maximum de participants autorisés a été atteint pour cette réunion. Contactez l'organisateur de la réunion.", "meetingReminder": "La réunion commencera le {{time}}. Veuillez vous joindre à nouveau au plus tard à l'heure prévue.", "multipleConferenceInitiation": "Initiation à plusieurs conférences", "oops": "Oups !", "oppsMaximumAllowedParticipantsErr": "Oups ! Le nombre maximum de participants autorisés a été atteint. V<PERSON>illez attendre que les participants quittent la réunion ou contacter l'organisateur de la réunion.", "or": "ou", "parallelMeetingsLicencesErr": "Impossible de démarrer la réunion. Assurez-vous de disposer d'une licence active pour participer à des réunions parallèles", "peopleInTheCall": "Les personnes en appel", "pleaseEnterEmail": "Veuillez saisir votre adresse e-mail", "pleaseEnterFullName": "Veuillez saisir le nom complet", "premeeting": "Pré-rencontre", "profile": "Profil", "readyToJoin": "Prêt à nous rejoindre ?", "recentMeetings": "Rencontres récentes", "screenSharingError": "Erreur de partage d'écran :", "showScreen": "Activer l'écran de pré-réunion", "signinsignup": "Se connecter / S'inscrire", "startWithPhone": "Commencez avec l'audio du téléphone", "subScriptionInactiveErr": "Votre abonnement est inactif. Impossible de démarrer la réunion.", "systemUpgradedInformation": "Nous avons mis à niveau notre système vers la version 2.0. Réclamez cette réunion en partageant ce mot de passe de réunion", "userNotAllowedToJoin": "L'utilisateur n'est pas autorisé à rejoindre", "videoOnlyError": "Erreur vidéo :", "videoTrackError": "Impossible de créer une piste vidéo.", "viewAllNumbers": "voir tous les numéros", "waitForModeratorMsg": "Veuillez patienter pendant que le modérateur rejoint l'appel.", "waitForModeratorMsgDynamic": "Veuillez patienter pendant que {{Moderator}} rejoint l'appel.", "youAreNotAllowed": "Vous n'êtes pas autorisé"}, "presenceStatus": {"busy": "<PERSON><PERSON><PERSON><PERSON>", "calling": "Appel...", "connected": "Connecté", "connecting": "De liaison...", "connecting2": "De liaison*...", "disconnected": "Déconnecté", "expired": "Expiré", "ignored": "Ignoré", "initializingCall": "Initialisation de l'appel...", "invalidToken": "<PERSON><PERSON> invalide", "invited": "Invi<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "ringing": "Sonnerie...", "signInAsHost": "Connectez-vous en tant qu'hôte"}, "profile": {"avatar": "avatar", "setDisplayNameLabel": "Votre nom d'affichage", "setEmailInput": "Entrez votre e-mail", "setEmailLabel": "Votre email", "title": "Profil"}, "raisedHand": "J'aimerais parler", "recording": {"authDropboxText": "Télécharger sur Dropbox", "availableS3Space": "Espace utilisé : {{s3_used_space}} sur {{s3_free_space}}", "availableSpace": "Espace disponible : {{spaceLeft}} Mo (environ {{duration}} minutes d'enregistrement)", "beta": "BÊTA", "busy": "Nous travaillons à libérer des ressources d'enregistrement. Veuillez réessayer dans quelques minutes.", "busyTitle": "Tous les enregistreurs sont actuellement occupés", "consentDialog": {"accept": "Je suis d'accord", "disclaimer": "L'enregistrement peut être utilisé à des fins de documentation, de formation ou à d'autres fins commerciales. Si vous ne voulez pas donner son consentement, ve<PERSON><PERSON><PERSON> quitter la réunion maintenant.", "leaveMeeting": "Partir", "message": "Cette réunion est enregistrée. Pour continuer à participer, vous devez donner votre consentement à être enregistré.", "title": "Enregistrement du consentement"}, "copyLink": "Copier le lien", "error": "L'enregistrement a échoué. Veuillez réessayer.", "errorFetchingLink": "Erreur lors de la récupération du lien d'enregistrement.", "expandedOff": "L'enregistrement s'est arrêté", "expandedOn": "La réunion est actuellement en cours d'enregistrement.", "expandedPending": "L'enregistrement est en cours de démarrage...", "failedToStart": "L'enregistrement n'a pas pu démarrer", "fileSharingdescription": "Partager l'enregistrement avec les participants à la réunion", "limitNotificationDescriptionNative": "En raison d'une forte demande, votre enregistrement sera limité à {{limit}} min. Pour des enregistrements illimités, essayez <3>{{app}}</3>.", "limitNotificationDescriptionWeb": "En raison d'une forte demande, votre enregistrement sera limité à {{limit}} minutes. Pour des enregistrements illimités, essayez <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "linkGenerated": "Nous avons généré un lien vers votre enregistrement.", "live": "EN DIRECT", "loggedIn": "Connecté en tant que {{userName}}", "off": "Enregistrement arrêté", "offBy": "{{name}} a arrêté l'enregistrement", "on": "L'enregistrement a commencé", "onBy": "{{name}} a commencé l'enregistrement", "pending": "Préparation de l'enregistrement de la réunion...", "rec": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recLive": "EN DIRECT + ENREGISTREMENT", "serviceDescription": "Votre enregistrement sera sauvegardé par le service d'enregistrement", "serviceDescriptionCloud": "Enregistrement dans le cloud", "serviceName": "Service d'enregistrement", "signIn": "Se connecter", "signOut": "se déconnecter", "unavailable": "Oups ! Le service {{serviceName}} n'est pas disponible actuellement. Nous travaillons à résoudre le problème. Veuillez réessayer ultérieurement.", "unavailableTitle": "Enregistrement indisponible", "uploadToCloud": "Télécharger vers le cloud"}, "sectionList": {"pullToRefresh": "Tirer pour rafraîchir"}, "security": {"about": "Vous pouvez ajouter un $t(lockRoomPassword) à votre réunion. Les participants devront fournir le $t(lockRoomPassword) avant d'être autorisés à rejoindre la réunion.", "aboutReadOnly": "Les participants modérateurs peuvent ajouter un $t(lockRoomPassword) à la réunion. Les participants devront fournir le $t(lockRoomPassword) avant d'être autorisés à rejoindre la réunion.", "insecureRoomNameWarning": "Le nom de la salle n'est pas sécurisé. Des participants indésirables peuvent rejoindre votre conférence. Pensez à sécuriser votre réunion à l'aide du bouton de sécurité.", "securityOptions": "Options de sécurité"}, "settings": {"calendar": {"about": "L'intégration du calendrier {{appName}} est utilisée pour accéder en toute sécurité à votre calendrier afin qu'il puisse lire les événements à venir.", "disconnect": "Déconnecter", "microsoftSignIn": "Connectez-vous avec Microsoft", "signedIn": "Vous accédez actuellement aux événements du calendrier pour {{email}}. Cliquez sur le bouton Déconnecter ci-dessous pour arrêter d'accéder aux événements du calendrier.", "title": "<PERSON><PERSON><PERSON>"}, "desktopShareFramerate": "Fréquence d'images du partage de bureau", "desktopShareHighFpsWarning": "Une fréquence d'images plus élevée pour le partage de bureau peut affecter votre bande passante. V<PERSON> devez redémarrer le partage d'écran pour que les nouveaux paramètres prennent effet.", "desktopShareWarning": "V<PERSON> de<PERSON> redémarrer le partage d’écran pour que les nouveaux paramètres prennent effet.", "devices": "Appareils", "followMe": "Tout le monde me suit", "framesPerSecond": "images par seconde", "incomingMessage": "Message entrant", "language": "<PERSON><PERSON>", "languageSettings": "Paramètres de langue", "loggedIn": "Connecté en tant que {{name}}", "microphones": "Microphones", "moderator": "Mod<PERSON><PERSON>ur", "more": "Plus", "name": "Nom", "noDevice": "Aucun", "noLanguagesAvailable": "Aucune langue disponible", "participantJoined": "Participant rejoint", "participantLeft": "Participant à gauche", "playSounds": "<PERSON><PERSON> le son sur", "sameAsSystem": "Identique au système ({{label}})", "selectAudioOutput": "Sortie audio", "selectCamera": "Caméra", "selectLanguage": "Sélectionner la langue", "selectMic": "Microphone", "sounds": "Sons", "speakers": "Intervenants", "startAudioMuted": "Tout le monde commence en mode silencieux", "startVideoMuted": "Tout le monde commence caché", "talkWhileMuted": "<PERSON><PERSON><PERSON> en mode muet", "title": "Paramètres"}, "settingsView": {"advanced": "<PERSON><PERSON><PERSON>", "alertCancel": "Annuler", "alertOk": "D'ACCORD", "alertTitle": "Avertissement", "alertURLText": "L'URL du serveur saisie n'est pas valide", "buildInfoSection": "Informations sur la construction", "conferenceSection": "Confé<PERSON>ce", "disableCallIntegration": "Désactiver l’intégration des appels natifs", "disableCrashReporting": "Désactiver les rapports d'incident", "disableCrashReportingWarning": "Êtes-vous sûr de vouloir désactiver les rapports d'incident ? Le paramètre sera appliqué après le redémarrage de l'application.", "disableP2P": "Désactiver le mode Peer-To-Peer", "displayName": "Nom d'affichage", "email": "E-mail", "header": "Paramètres", "profileSection": "Profil", "serverURL": "URL du serveur", "showAdvanced": "Afficher les paramètres avancés", "startWithAudioMuted": "Commencer avec le son coupé", "startWithVideoMuted": "Commencer avec la vidéo coupée", "version": "Version"}, "share": {"dialInfoText": "=====\n\nVous souhaitez simplement vous connecter sur votre téléphone ?\n\n{{defaultDialInNumber}}Cliquez sur ce lien pour voir les numéros de téléphone à composer pour cette réunion\n{{dialInfoPageUrl}}", "mainText": "Cliquez sur le lien suivant pour rejoindre la réunion :\n{{roomUrl}}"}, "speaker": "Conférencier", "speakerStats": {"hours": "{{count}} h", "minutes": "{{count}}m", "name": "Nom", "seconds": "{{count}} s", "speakerStats": "Statistiques des intervenants", "speakerTime": "Temps de parole"}, "startupoverlay": {"genericTitle": "La réunion doit utiliser votre microphone et votre caméra.", "policyText": "", "title": "{{app}} doit utiliser votre microphone et votre caméra."}, "suspendedoverlay": {"rejoinKeyTitle": "Rejoindre", "text": "Appuyez sur le bouton <i>Rejoindre</i> pour vous reconnecter.", "title": "Votre appel vidéo a été interrompu car cet ordinateur s'est mis en veille."}, "toolbar": {"Settings": "Paramètres", "Share": "Partager", "accessibilityLabel": {"Settings": "Basculer les paramètres", "audioOnly": "Activer/désactiver l'audio uniquement", "audioRoute": "<PERSON><PERSON><PERSON> le périphérique audio", "boo": "<PERSON><PERSON>", "callQuality": "G<PERSON>rer la qualité vidéo", "carmode": "Mode voiture", "cc": "Activer/désactiver les sous-titres", "chat": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> le chat", "clap": "Taper", "collapse": "Effondrement", "document": "Basculer le document partagé", "donationCLP": "Paramètres de C&P Connect", "donationLink": "Paramètres de DonorBox", "download": "Téléchargez nos applications", "embedMeeting": "Intégrer la réunion", "expand": "Développer", "feedback": "Laisser les commentaires", "fullScreen": "Basculer en plein écran", "genericIFrame": "Activer/désactiver l'application partagée", "giphy": "Basculer le menu GIPHY", "grantModerator": "Modérateur de subventions", "hangup": "<PERSON><PERSON>ter la réunion", "help": "Aide", "invite": "Inviter des gens", "kick": "Participant au coup de pied", "laugh": "Rire", "leaveConference": "<PERSON><PERSON>ter la réunion", "like": "<PERSON><PERSON> lev<PERSON>", "lobbyButton": "Activer/désactiver le mode lobby", "localRecording": "Activer/désactiver les commandes d'enregistrement locales", "lockRoom": "Activer/désactiver le mot de passe de la réunion", "moreActions": "Plus d'actions", "moreActionsMenu": "Menu Plus d'actions", "moreOptions": "Afficher plus d'options", "mute": "<PERSON>r/<PERSON>é<PERSON>r le son", "muteEveryone": "Couper le son de tout le monde", "muteEveryoneElse": "Couper le son de tout le monde", "muteEveryoneElsesVideo": "Désactiver la caméra de tout le monde", "muteEveryonesVideo": "Désactiver la caméra de tout le monde", "participants": "Participants", "party": "<PERSON><PERSON><PERSON>d popper", "pip": "Activer/désactiver le mode Image dans l'image", "privateMessage": "Envoyer un message privé", "profile": "Modifiez votre profil", "raiseHand": "Lever/baisser la main", "reactionsMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON>er le menu des réactions", "recording": "Activer/désactiver l'enregistrement", "remoteMute": "Participant muet", "remoteVideoMute": "Désactiver la caméra du participant", "removeDonation": "Supprimer DonorBox", "rmoveCDonation": "Supprimer C&P", "security": "Options de sécurité", "selectBackground": "Sélectionner l'arrière-plan", "shareRoom": "Inviter quelqu'un", "shareYourScreen": "<PERSON><PERSON><PERSON><PERSON> / <PERSON>rr<PERSON><PERSON> le partage de votre écran", "shareaudio": "Partager l'audio", "sharedvideo": "Activer/désactiver le partage de vidéos YouTube", "shortcuts": "Basculer les raccourcis", "show": "Spectacle sur scène", "speakerStats": "Activer/désactiver les statistiques des intervenants", "surprised": "<PERSON><PERSON><PERSON>", "tileView": "Basculer la vue en mosaïque", "toggleCamera": "Basculer la caméra", "toggleFilmstrip": "Basculer la pellicule", "toggleReactions": "Basculer les réactions", "videoblur": "Activer/dés<PERSON>r le flou vidéo", "videomute": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>"}, "addPeople": "Ajoutez des personnes à votre appel", "audioOnlyOff": "Désactiver le mode faible bande passante", "audioOnlyOn": "Activer le mode faible bande passante", "audioRoute": "<PERSON><PERSON><PERSON> le périphérique audio", "audioSettings": "Paramètres audio", "authenticate": "Authentifier", "boo": "<PERSON><PERSON>", "callQuality": "G<PERSON>rer la qualité vidéo", "chat": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> le chat", "clap": "Taper", "closeChat": "<PERSON><PERSON><PERSON> le chat", "closeParticipantsPane": "<PERSON><PERSON><PERSON> le volet des participants", "closeReactionsMenu": "<PERSON><PERSON>er le menu des réactions", "disableNoiseSuppression": "Désactiver la suppression du bruit", "disableReactionSounds": "Vous pouvez dés<PERSON>r les sons de réaction pour cette réunion", "documentClose": "Fermer LivePad", "documentOpen": "Partager LivePad", "donationCLP": "Paramètres de C&P Connect", "donationLink": "Paramètres de DonorBox", "download": "Téléchargez nos applications", "e2ee": "Chiffrement de bout en bout", "embedMeeting": "Intégrer la réunion", "enterFullScreen": "Voir en plein écran", "enterTileView": "Accéder à la vue en mosaïque", "exitFullScreen": "<PERSON><PERSON><PERSON> le mode plein écran", "exitTileView": "<PERSON><PERSON><PERSON> la vue en mosaïque", "feedback": "Laisser les commentaires", "genericIFrameClose": "<PERSON><PERSON><PERSON><PERSON> le <PERSON> blanc", "genericIFrameOpen": "Partager le tableau blanc", "genericIFrameWeb": "Tableau blanc", "hangUpText": "E<PERSON>-vous sûr de vouloir rac<PERSON>cher ?", "hangUpforEveryOne": "Raccrocher pour tout le monde", "hangUpforMe": "Raccrochez seulement pour moi", "hangup": "Partir", "help": "Aide", "hideReactions": "Masquer la réaction", "iOSStopScreenShareAlertMessage": "Veuillez arrêter le partage d'écran avant HangUp.", "iOSStopScreenShareAlertTitle": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "invite": "Inviter des gens", "inviteViaCalendar": "Inviter via le calendrier", "laugh": "Rire", "leaveConference": "<PERSON><PERSON>ter la réunion", "like": "<PERSON><PERSON> lev<PERSON>", "lobbyButtonDisable": "Désactiver le mode lobby", "lobbyButtonEnable": "Activer le mode lobby", "login": "Se connecter", "logout": "Déconnexion", "lowerYourHand": "Baisse ta main", "moreActions": "Plus d'actions", "moreOptions": "Plus d'options", "mute": "<PERSON>r/<PERSON>é<PERSON>r le son", "muteEveryone": "Couper le son de tout le monde", "muteEveryonesVideo": "Désactiver la caméra de tout le monde", "noAudioSignalDesc": "Si vous ne l'avez pas volontairement désactivé à partir des paramètres système ou du matériel, envisagez de changer d'appareil.", "noAudioSignalDescSuggestion": "Si vous ne l'avez pas volontairement désactivé à partir des paramètres système ou du matériel, envisagez de passer à l'appareil suggéré.", "noAudioSignalDialInDesc": "Vous pouvez également vous connecter en utilisant :", "noAudioSignalDialInLinkDesc": "Numé<PERSON>'<PERSON>", "noAudioSignalTitle": "Il n'y a aucune entrée provenant de votre micro !", "noisyAudioInputDesc": "Il semble que votre microphone fasse du bruit, pensez à le couper ou à changer d'appareil.", "noisyAudioInputTitle": "Votre microphone semble être bruyant !", "openChat": "<PERSON><PERSON><PERSON><PERSON><PERSON> le chat", "openReactionsMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu des réactions", "participants": "Participants", "party": "Célébration", "pip": "Mode Image dans l'image", "privateMessage": "Envoyer un message privé", "profile": "Modifiez votre profil", "raiseHand": "Lever/baisser la main", "raiseYourHand": "Lève la main", "reactionBoo": "Envoyer une réaction de huée", "reactionClap": "Envoyer une réaction d'applaudissements", "reactionLaugh": "Envoyer une réaction de rire", "reactionLike": "Envoyer une réaction de pouce levé", "reactionParty": "Envoyer une réaction de party popper", "reactionSurprised": "Envoyer une réaction surprise", "removeDonation": "Supprimer DonorBox", "rmoveCDonation": "Supprimer C&P", "security": "Options de sécurité", "selectBackground": "Sélectionner l'arrière-plan", "shareRoom": "Inviter quelqu'un", "shareaudio": "Partager l'audio", "sharedvideo": "Partager Youtube", "shortcuts": "Aff<PERSON>r les raccourcis", "showReactions": "Afficher la réaction", "speakerStats": "Statistiques des intervenants", "startScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "startSubtitles": "<PERSON><PERSON><PERSON><PERSON> les sous-titres", "stopAudioSharing": "Arr<PERSON><PERSON> le partage audio", "stopScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "stopSharedVideo": "<PERSON><PERSON><PERSON>ter la vidéo YouTube", "stopSubtitles": "<PERSON><PERSON><PERSON><PERSON> les sous-titres", "surprised": "<PERSON><PERSON><PERSON>", "talkWhileMutedPopup": "Vous essayez de parler ? Votre son est coupé.", "tileViewToggle": "Basculer la vue en mosaïque", "toggleCamera": "Basculer la caméra", "videoSettings": "Paramètres vidéo", "videomute": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>", "voiceCommand": "<PERSON><PERSON><PERSON><PERSON>r la commande vocale", "whiteBoardOpen": "Partager le tableau blanc", "zoomin": "Zoom avant", "zoomout": "Zoom arri<PERSON>"}, "transcribing": {"ClosedCaptions": "Légendes fermées", "LiveCaptions": "Sous-titres en direct", "NoCaptionsAvailable": "Aucun sous-titre disponible", "OpenCloseCaptions": "Légendes ouvertes / fermes", "Transcribing": "Transcription", "TranscribingNotAvailable": "Transcription non disponible", "TranscriptionLangDefaultNote": "Remarque: La transcription n'est pas disponible dans la langue de réunion sélectionnée {{language}}, donc sera par défaut en anglais.", "TranscriptionLanguageCannotBeChangedOngoingCall": "La langue de transcription ne peut pas être modifiée lors d'un appel en cours. Veuillez rejoindre pour prendre effet.", "TranscriptionLanguageCantChange": "La langue de transcription peut '\\ c't change", "Transcriptions": "Transcriptions", "Translate": "<PERSON><PERSON><PERSON><PERSON>", "TranslateTo": "Traduire en", "Translating": "Traduction", "TranslationNotAvailable": "Traduction non disponible", "ccButtonTooltip": "<PERSON><PERSON><PERSON><PERSON> / <PERSON>rr<PERSON><PERSON> les sous-titres", "error": "La transcription a échoué. Veuillez réessayer.", "expandedLabel": "La transcription est actuellement en cours", "failedToStart": "La transcription n'a pas pu dé<PERSON>rer", "labelToolTip": "La réunion est en cours de transcription", "off": "La transcription est arrêtée", "pending": "Préparation de la transcription de la réunion...", "sourceLanguageDesc": "La langue de la réunion est actuellement définie sur {{sourceLanguage}}", "sourceLanguageHere": "Vous pouvez le changer ici", "start": "Commencer à afficher les sous-titres", "stop": "<PERSON><PERSON><PERSON><PERSON> d'afficher les sous-titres", "subtitlesOff": "Désactivé", "subtitlesTitle": "Sous-titres", "tr": "Tr", "transcriptionQuotaExceeded": "Quota de transcription dépassé pour ce mois", "transcriptionQuotaExceededTitle": "Quota de transcription dépassé"}, "userMedia": {"androidGrantPermissions": "Sélectionnez <b><i>Autoriser</i></b> lorsque votre navigateur demande des autorisations.", "chromeGrantPermissions": "Sélectionnez <b><i>Autoriser</i></b> lorsque votre navigateur demande des autorisations.", "edgeGrantPermissions": "Sélectionnez <b><i><PERSON><PERSON></i></b> lorsque votre navigateur demande des autorisations.", "electronGrantPermissions": "Veuillez accorder la permission d'utiliser votre caméra et votre microphone", "firefoxGrantPermissions": "Sélectionnez <b><i>Partager l'appareil sélectionné</i></b> lorsque votre navigateur demande des autorisations.", "iexplorerGrantPermissions": "Sélectionnez <b><i>OK</i></b> lorsque votre navigateur demande des autorisations.", "nwjsGrantPermissions": "Veuillez accorder la permission d'utiliser votre caméra et votre microphone", "operaGrantPermissions": "Sélectionnez <b><i>Autoriser</i></b> lorsque votre navigateur demande des autorisations.", "react-nativeGrantPermissions": "Sélectionnez <b><i>Autoriser</i></b> lorsque votre navigateur demande des autorisations.", "safariGrantPermissions": "Sélectionnez <b><i>OK</i></b> lorsque votre navigateur demande des autorisations."}, "videoSIPGW": {"busy": "Nous travaillons à la libération de ressources. Veuillez réessayer dans quelques minutes.", "busyTitle": "Le service de chambre est actuellement occupé", "errorAlreadyInvited": "{{displayName}} d<PERSON><PERSON><PERSON> invité", "errorInvite": "La conférence n'est pas encore établie. Veuillez réessayer plus tard.", "errorInviteFailed": "Nous travaillons à résoudre le problème. Veuillez réessayer plus tard.", "errorInviteFailedTitle": "L'invitation de {{displayName}} a échoué", "errorInviteTitle": "Erreur lors de l'invitation de la salle", "pending": "{{displayName}} a <PERSON><PERSON> invité"}, "videoStatus": {"audioOnly": "Aud", "audioOnlyExpanded": "Vous êtes en mode faible bande passante. Dans ce mode, vous ne recevrez que l'audio et le partage d'écran.", "callQuality": "Qualité vidéo", "hd": "Haute définition", "hdTooltip": "Visionnage de vidéos haute définition", "highDefinition": "Haute définition", "labelTooiltipNoVideo": "Pas de vidéo", "labelTooltipAudioOnly": "Mode faible bande passante activé", "ld": "LD", "ldTooltip": "Visionnage de vidéos en basse définition", "lowDefinition": "Basse définition", "onlyAudioAvailable": "Seul l'audio est disponible", "onlyAudioSupported": "Nous ne prenons en charge que l'audio dans ce navigateur.", "sd": "SD", "sdTooltip": "Visionnage d'une vidéo en définition standard", "standardDefinition": "Définition standard", "uhd": "Ultra HD", "uhdTooltip": "Visionnage de vidéos en ultra haute définition", "uhighDefinition": "Ultra Haute Définition"}, "videothumbnail": {"connectionInfo": "Informations de connexion", "domute": "<PERSON><PERSON>", "domuteOthers": "Couper le son de tout le monde", "domuteVideo": "Désactiver la caméra", "domuteVideoOfOthers": "Désactiver la caméra de tout le monde", "flip": "<PERSON><PERSON><PERSON>", "grantModerator": "Modérateur de subventions", "kick": "Supprimer l'utilisateur", "moderator": "Mod<PERSON><PERSON>ur", "mute": "Le participant est mis en sourdine", "muted": "En sourdine", "remoteControl": "Télécommande marche/arrêt", "show": "Spectacle sur scène", "videoMuted": "Caméra désactivée", "videomute": "Le participant a arrêté la caméra"}, "virtualBackground": {"addBackground": "Ajouter un arrière-plan", "appliedCustomImageTitle": "Image personnalisée téléchargée", "apply": "Appliquer", "blur": "Se brouiller", "customImg": "Image personnalisée", "deleteImage": "Supprimer l'image", "desktopShare": "Partage de bureau", "desktopShareError": "Impossible de créer un partage de bureau", "enableBlur": "<PERSON><PERSON> le flou", "image1": "Plage", "image2": "<PERSON>r blanc neutre", "image3": "Chambre blanche vide", "image4": "Lampadaire noir", "image5": "<PERSON><PERSON><PERSON>", "image6": "<PERSON><PERSON><PERSON>", "image7": "Lever du soleil", "none": "Aucun", "pleaseWait": "<PERSON>'il vous plaît, attendez...", "removeBackground": "Supprimer l'arrière-plan", "slightBlur": "Légèrement flou", "switchBackgroundTitle": "Changer d'arrière-plan", "title": "Arrière-plans virtuels", "uploadedImage": "Image téléchargée {{index}}", "virtualImagesTitle": "Images virtuelles intégrées", "webAssemblyWarning": "WebAssembly non pris en charge"}, "voicecommand": {"activePIPLabel": "PIP actif", "clickOnMic": "Cliquez sur la commande Mic et Voice", "hints": {"StopScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "closeLivePad": "Fermer LivePad", "closeWhiteboard": "<PERSON><PERSON><PERSON> le tableau blanc", "closeYoutube": "<PERSON><PERSON><PERSON> You<PERSON>e", "hints": "Conseils", "invitePeople": "Inviter des gens", "lowerHand": "Main basse", "openChatBox": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte de <PERSON>", "openClickAndPledge": "<PERSON><PERSON><PERSON><PERSON>, cliquez et engagez", "openDonorbox": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte de dons", "openFullScreen": "Ou<PERSON><PERSON>r en plein écran", "openLivePad": "Ouvrir LivePad", "openLivestream": "Diffusion en direct ouverte", "openParticipantPane": "<PERSON>u<PERSON><PERSON>r le volet Participant", "openRecording": "Enregistre<PERSON> ouvert", "openSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "openSpeakerStats": "Statistiques des intervenants ouverts", "openVideoQualityDialog": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte de dialogue de qualité vidéo", "openVirtualBackground": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'arrière-plan virtuel", "openWhiteboard": "<PERSON><PERSON><PERSON><PERSON><PERSON> le tableau blanc", "openYoutube": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "raiseHand": "Lever la main", "removeClickAndPledge": "Supp<PERSON>er le clic et le gage", "removeDonorbox": "Supprimer Donorbox", "startScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran"}, "inActivePIPLabel": "Dans PIP actif", "pleaseWaitWeAreRecording": "<PERSON><PERSON><PERSON>z patienter, nous enregistrons", "vcLabel": "Commande vocale", "voiceCommandForMeethour": "Commande vocale pour l'heure de rendez-vous"}, "volumeSlider": "Curseur de volume", "welcomepage": {"accessibilityLabel": {"join": "Appuyez pour rejoindre", "roomname": "Entrez l'ID de la réunion"}, "addMeetingName": "Ajouter le nom de la réunion", "appDescription": "N'hésitez pas à discuter en vidéo avec toute l'équipe. En fait, invitez tous ceux que vous connaissez. {{app}} est une solution de visioconférence entièrement cryptée et 100 % open source que vous pouvez utiliser toute la journée, tous les jours, gratuitement, sans avoir besoin de créer de compte.", "audioVideoSwitch": {"audio": "Voix", "video": "Vidéo"}, "calendar": "<PERSON><PERSON><PERSON>", "connectCalendarButton": "Connectez votre calendrier", "connectCalendarText": "Connectez votre calendrier pour afficher toutes vos réunions dans {{app}}. De plus, ajoutez des réunions {{provider}} à votre calendrier et démarrez-les en un clic.", "developerPlan": "Plan du développeur", "enterRoomTitle": "<PERSON><PERSON><PERSON>rer une nouvelle réunion ou saisir le nom d'une salle existante", "enterprisePlan": "Plan Entreprise", "enterpriseSelfHostPlan": "Plan d'auto-hébergement d'entreprise", "features": "Caractéristiques", "footer": {"allRightsReserved": "Tous droits réservés", "androidAppDownload": "Téléchargement de l'application Android", "apiDocumentation": "Documentation de l'API", "app": "Application", "blog": "Blog", "company": "Entreprise", "contact": "Contact", "copyright": "Droits d'auteur", "copyrightText": "Copyright 2020 - 2024 Rencontrer l'heure LLC. Tous droits réservés", "developers": "Développeurs", "disclaimer": "Clause de non-responsabilité", "download": "Télécharger", "email": "E-mail", "faqs": "FAQ", "followUs": "Suivez-nous", "helpDesk": "Service d'assistance", "home": "<PERSON><PERSON>", "iOSAppDownload": "Téléchargement de l'application iOS", "inTheNews": "Dans l'actualité", "integrations": "Intégrations", "knowledgeBase": "Base de connaissances", "meethour": "Heure de rencontre", "meethourLLC": "Heure de rencontre LLC", "officeAddress": "8825 Stanford, bureau 205 Columbia, MD 21045", "phone": "Téléphone", "privacyPolicy": "politique de confidentialité", "productPresentation": "Présentation du produit", "refundCancellationPolicy": "Politique de remboursement et d'annulation", "termsConditions": "Conditions générales", "testimonials": "Témoignages", "webMobileSDK": "Kit de développement logiciel (SDK) Web et mobile", "whoAreYou": "Qui es-tu"}, "forHospitals": "Pour les hôpitaux", "freeBannerDescription": "Des visioconférences en qualité HD gratuites et illimitées comme jamais auparavant. Rejoignez la réunion où que vous soyez.", "freePlan": "Plan gratuit", "getHelp": "FAQ", "go": "<PERSON><PERSON><PERSON> ou rejoindre une réunion", "goSmall": "<PERSON><PERSON><PERSON> ou rejoindre une réunion", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Accélérez le développement avec des SDK prédéfinis.", "apiStatus": "Statut de l'API", "appointmentSchedulingVideoConference": "Prise de rendez-vous et vidéoconférence.", "blog": "Blog", "customIntegrationDedicatedSupport": "Intégration personnalisée et support dédié", "customTailoredVideoMeetings": "Réunions vidéo sur mesure.", "developer": "Promoteur", "developers": "Développeurs", "documentation": "Documentation", "eMail": "E-mail", "edTech": "Edtech", "engagingOnlineLearningForEducators": "Apprentissage en ligne engageant pour les éducateurs.", "engagingVirtualEventExperiences": "Expériences d’événements virtuels engageantes.", "enterprise": "Entreprise", "enterpriseSelfHost": "Entreprise auto-hébergée", "features": "Caractéristiques", "fitness": "Aptitude", "free": "<PERSON><PERSON><PERSON>", "fundraiseEffortlesslyWithinVideoConferences": "Collectez des fonds sans effort grâce aux vidéoconférences.", "fundraisingDonate": "Collecte de fonds / Faire un don", "fundraisingDonateOnline": "Collecte de fonds / Faire un don en ligne", "getStarted": "Commencer", "hdQualityVideoConferenceApp": "Application de vidéoconférence de qualité HD", "help": "Aide", "helpDesk": "Service d'assistance", "highQualityLiveEventStreaming": "Streaming d'événements en direct de haute qualité.", "hostVideoConferenceOnYourServers": "H<PERSON><PERSON>ez une vidéoconférence sur vos serveurs.", "industries": "Industries", "integrateVideoCallWithinYourWebsiteApp": "Intégrez l'appel vidéo à votre site Web/application.", "interactiveVirtualLearningSolutions": "Solutions d'apprentissage virtuel interactives.", "joinAMeeting": "Rejoindre une réunion", "knowledgeBase": "Base de connaissances", "liveStreaming": "Diffusion en direct", "meethour": "Heure de rencontre", "myCaly": "Mon Caly", "myCalyPricing": "<PERSON><PERSON><PERSON>.", "mycaly": "MonCaly", "noAdsRecordingLiveStreaming": "Pas de publicité + enregistrement + diffusion en direct.", "noTimeLimitGroupCalls": "Aucune limite de temps pour les appels individuels et de groupe.", "preBuiltSDKs": "SDK pré-construits", "pricing": "<PERSON><PERSON><PERSON>", "pro": "Pro", "products": "Produits", "resources": "Ressources", "scheduleADemo": "Planifier une démo", "simplifiedAPIReferences": "Références API simplifiées", "smoothVideoOnboardingExperience": "Expérience d'intégration vidéo fluide.", "solutions": "Solutions", "stayuptodateWithOurBlog": "Restez à jour avec notre blog", "systemHealthStatusandUpdates": "État de santé du système et mises à jour", "tailoredSolutionsForYourHealthcareNeeds": "Des solutions sur mesure pour vos besoins de santé.", "telehealth": "Télésanté", "useCases": "Cas d'utilisation", "videoConference": "Vidéoconférence", "videoConferencePlans": "Plans de vidéoconférence", "videoConferencePricing": "Tarifs de la visioconférence.", "videoConferencing": "Vidéoconférence", "videoKYC": "Vidéo KYC", "virtualClassrooms": "Classes virtuelles", "virtualEvents": "Événements virtuels", "virtualSolutionForHomeFitness": "Solution virtuelle pour le fitness à domicile.", "webinarSessionsWithIndustryLeaders": "Sessions de webinaires avec des leaders de l'industrie.", "webinars": "Webinaires"}, "headerSubtitle": "Réunions sécurisées et de qualité HD", "headerTitle": "Heure de rencontre", "info": "Informations de connexion", "invalidMeetingID": "ID de réunion non valide", "jitsiOnMobile": "Rencontrer l'heure sur mobile – téléchargez nos applications et démarrez une réunion où que vous soyez", "join": "CRÉER / REJOINDRE", "joinAMeeting": "Rejoindre une réunion", "logo": {"calendar": "Logo du calendrier", "desktopPreviewThumbnail": "Miniature d'aperçu du bureau", "googleLogo": "Logo Google", "logoDeepLinking": "Logo de la rencontre Jitsi", "microsoftLogo": "Logo Microsoft", "policyLogo": "Logo de la politique"}, "meetingDate": "Date de la réunion", "meetingDetails": "Détails de la réunion", "meetingIsReady": "La réunion est prête", "mobileDownLoadLinkAndroid": "Télécharger l'application mobile pour Android", "mobileDownLoadLinkFDroid": "Téléchargez l'application mobile pour F-Droid", "mobileDownLoadLinkIos": "Télécharger l'application mobile pour iOS", "moderatedMessage": "Ou <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">réservez une URL de réunion</a> à l'avance où vous êtes le seul modérateur.", "oopsDeviceClockorTimezoneErr": "Oups ! Une erreur s'est produite. Assurez-vous que l'horloge/le fuseau horaire de votre appareil sont exacts", "oopsThereSeemsToBeProblem": "Oups, il semble y avoir un problème", "pleaseWaitForTheStartMeeting": "<PERSON><PERSON><PERSON>z attendre que le {{moderator}} démarre cette réunion.", "preRegistrationMsg": "Cette réunion nécessite une préinscription. Inscrivez-vous dans le navigateur et rejoignez-nous à partir du lien de courrier électronique d'invitation.", "pricing": "<PERSON><PERSON><PERSON>", "privacy": "Confidentialité", "privateMeetingErr": "Il semble que vous rejoigniez une réunion privée. Veuillez vous connecter en utilisant l'adresse e-mail d'invitation.", "proPlan": "Plan Pro", "recentList": "<PERSON><PERSON><PERSON>", "recentListDelete": "Supprimer l'entrée", "recentListEmpty": "Votre liste récente est actuellement vide. Discutez avec votre équipe et vous retrouverez ici toutes vos réunions récentes.", "reducedUIText": "Bienvenue sur {{app}} !", "registerNow": "Ins<PERSON>rivez-vous maintenant", "roomNameAllowedChars": "Le nom de la réunion ne doit contenir aucun de ces caractères : ?, &, :, ', \", %, #.", "roomname": "Entrez l'ID de la réunion", "roomnameHint": "Entrez le nom ou l'URL de la salle que vous souhaitez rejoindre. Vous pouvez inventer un nom, il suffit de le faire savoir aux personnes que vous rencontrez afin qu'elles saisissent le même nom.", "scheduleAMeeting": "Planifier une réunion", "sendFeedback": "Envoyer des commentaires", "shiftingVirtualMeetToReality": "Passer d'une réunion virtuelle à une réunion réelle", "solutions": "Solutions", "startMeeting": "Commencer la réunion", "terms": "Termes", "timezone": "<PERSON><PERSON> ho<PERSON>", "title": "Solution de visioconférence 100 % gratuite, illimitée, cryptée de bout en bout et de qualité HD", "tryNowItsFree": "Essayez maintenant, c'est gratuit", "waitingInLobby": "En attente dans le hall. {{moderator}} vous laissera bi<PERSON><PERSON><PERSON> entrer.", "youtubeHelpTutorial": "Tu<PERSON><PERSON><PERSON> d'aide YouTube"}}