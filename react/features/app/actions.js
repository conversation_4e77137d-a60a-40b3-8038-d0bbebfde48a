// @flow

import { jitsiLocalStorage } from '@jitsi/js-utils';
import i18next from 'i18next';
import _ from 'lodash';
import type { Dispatch } from 'redux';

import { API_ID } from '../../../modules/API/constants';
import { setRoom } from '../base/conference';
import {
    configWillLoad,
    createFakeConfig,
    loadConfigError,
    restoreConfig,
    setConfig,
    storeConfig
} from '../base/config';
import { apiKEY } from '../base/config/constants';
import { disconnect, setLocationURL } from '../base/connection';
import { setJWT, setJwtStorage } from '../base/jwt';
import { loadConfig, loadinterfaceConfig } from '../base/lib-meet-hour';
import { MEDIA_TYPE } from '../base/media';
import { toState } from '../base/redux';
import { createDesiredLocalTracks, isLocalCameraTrackMuted, isLocalTrackMuted } from '../base/tracks';
import {
    addHashParamsToURL,
    getBackendSafeRoomName,
    getLocationContextRoot,
    parseURIString,
    toURLString
} from '../base/util';
import { checkIfNotReactNative } from '../base/util/checkOS';
import { isVpaasMeeting } from '../billing-counter/functions';
import { clearNotifications, showNotification } from '../notifications';
import { setFatalError } from '../overlay';

import {
    getDefaultURL,
    getName, getPackageIDForMobile
} from './functions';
import logger from './logger';
import { stateType } from './stateTypes';

declare var interfaceConfig: Object;


/**
 * Triggers an in-app navigation to a specific route. Allows navigation to be
 * abstracted between the mobile/React Native and Web/React applications.
 *
 * @param {string|undefined} uri - The URI to which to navigate. It may be a
 * full URL with an HTTP(S) scheme, a full or partial URI with the app-specific
 * scheme, or a mere room name.
 * @param {boolean|undefined} desktopApp - If Desktop App enable.
 * @returns {Function}
 */
export function appNavigate(uri: ?string, desktopApp: ?boolean = false) {
    return async (dispatch: Dispatch<any>, getState: Function) => {
        let location = parseURIString(uri);

        if (checkIfNotReactNative()) {
            const jwt = getState()[stateType.jwt]?.jwt;
            const jwtData = getState()[stateType.jwt]?.jwtData;

            dispatch(setJWT(desktopApp && jwt ? jwt : null));
            dispatch(setJwtStorage(desktopApp && jwtData ? jwtData : null));
        }

        // If the specified location (URI) does not identify a host, use the app's
        // default.
        if (!location || !location.host) {
            const defaultLocation = parseURIString(getDefaultURL(getState));

            if (location) {
                location.host = defaultLocation.host;

                // FIXME Turn location's host, hostname, and port properties into
                // setters in order to reduce the risks of inconsistent state.
                location.hostname = defaultLocation.hostname;
                location.pathname
                    = defaultLocation.pathname + location.pathname.substr(1);
                location.port = defaultLocation.port;
                location.protocol = defaultLocation.protocol;
            } else {
                location = defaultLocation;
            }
        }

        location.protocol || (location.protocol = 'https:');
        const { contextRoot, host, room } = location;
        const locationURL = new URL(location.toString());

        // Disconnect from any current conference.
        // FIXME: unify with web.
        if (navigator.product === 'ReactNative') {
            dispatch(disconnect());
            dispatch(setJWT(null));
            dispatch(setJwtStorage(null));
        }

        // There are notifications now that gets displayed after we technically left
        // the conference, but we're still on the conference screen.
        dispatch(clearNotifications());

        dispatch(configWillLoad(locationURL, room));

        let protocol = location.protocol.toLowerCase();

        // The React Native app supports an app-specific scheme which is sure to not
        // be supported by fetch.
        protocol !== 'http:' && protocol !== 'https:' && (protocol = 'https:');

        // eslint-disable-next-line max-len

        let mobileObject;
        let paramsForMobile;

        if (navigator.product === 'ReactNative') {
            mobileObject = getPackageIDForMobile;
            paramsForMobile = `&package_id=${mobileObject?.packageId}&deviceType=${mobileObject?.deviceType}`;
        }

        const version = Math.round(Math.random() * 10);

        const baseURL = `${protocol}//${host === 'meethour.io' ? 'api.meethour.io/libs/v2.4.6' : host}${contextRoot || '/'}`;

        let url = `${baseURL}config.js?v=${version}&apiKey=${apiKEY}${_.isEmpty(paramsForMobile) ? '' : paramsForMobile}`;

        const intUrl = `${baseURL}interface_config.js?v=${version}&apiKey=${apiKEY}${_.isEmpty(paramsForMobile) ? '' : paramsForMobile}`;

        // XXX In order to support multiple shards, tell the room to the deployment.
        room && (url += `?room=${getBackendSafeRoomName(room)}`);

        let config;

        // Avoid (re)loading the config when there is no room.
        if (!room) {
            config = restoreConfig(baseURL);
        }

        let intConfig;

        if (!config) {
            try {
                config = await loadConfig(url);
                // eslint-disable-next-line no-negated-condition
                if (navigator.product === 'ReactNative') {
                    intConfig = !intConfig && await loadinterfaceConfig(intUrl);
                    if (intConfig) {
                        config.interfaceConfig = intConfig;
                    }
                }
                dispatch(storeConfig(baseURL, config));
            } catch (error) {
                config = restoreConfig(baseURL);

                if (!config) {
                    if (room) {
                        dispatch(loadConfigError(error, locationURL));

                        return;
                    }

                    // If there is no room (we are on the welcome page), don't fail, just create a fake one.
                    logger.warn('Failed to load config but there is no room, applying a fake one');
                    config = createFakeConfig(baseURL);
                }
            }
        }

        if (getState()['features/base/config'].locationURL !== locationURL) {
            dispatch(loadConfigError(new Error('Config no longer needed!'), locationURL));

            return;
        }
        logger.log('appNavigate', locationURL, room);
        dispatch(setLocationURL(locationURL));
        dispatch(setConfig(config));
        dispatch(setRoom(room));

        // FIXME: unify with web, currently the connection and track creation happens in conference.js.
        if (room && navigator.product === 'ReactNative') {
            dispatch(createDesiredLocalTracks());

            // if (uri === undefined) {
            //     console.log('connectNative');
            //     dispatch(createDesiredLocalTracks());
            //     dispatch(connect());
            // }

        }
    };
}

/**
 * Redirects to another page generated by replacing the path in the original URL
 * with the given path.
 *
 * @param {(string)} pathname - The path to navigate to.
 * @returns {Function}
 */
export function redirectWithStoredParams(pathname: string) {
    return (dispatch: Dispatch<any>, getState: Function) => {
        const { locationURL } = getState()['features/base/connection'];
        const newLocationURL = new URL(locationURL.href);

        newLocationURL.pathname = pathname;
        window.location.assign(newLocationURL.toString());
    };
}

/**
 * Assigns a specific pathname to window.location.pathname taking into account
 * the context root of the Web app.
 *
 * @param {string} pathname - The pathname to assign to
 * window.location.pathname. If the specified pathname is relative, the context
 * root of the Web app will be prepended to the specified pathname before
 * assigning it to window.location.pathname.
 * @param {string} hashParam - Optional hash param to assign to
 * window.location.hash.
 * @returns {Function}
 */
export function redirectToStaticPage(pathname: string, hashParam: ?string) {
    return () => {
        const windowLocation = window.location;
        let newPathname = pathname;

        if (!newPathname.startsWith('/')) {
            // A pathname equal to ./ specifies the current directory. It will be
            // fine but pointless to include it because contextRoot is the current
            // directory.
            newPathname.startsWith('./')
                && (newPathname = newPathname.substring(2));
            newPathname = getLocationContextRoot(windowLocation) + newPathname;
        }

        if (hashParam) {
            windowLocation.hash = hashParam;
        }

        windowLocation.pathname = newPathname;
    };
}

/**
 * Reloads the page.
 *
 * @protected
 * @returns {Function}
 */
export function reloadNow() {
    return (dispatch: Dispatch<Function>, getState: Function) => {
        dispatch(setFatalError(undefined));

        const state = getState();
        const { locationURL } = state['features/base/connection'];

        // Preserve the local tracks muted state after the reload.
        const newURL = addTrackStateToURL(locationURL, state);

        logger.info(`Reloading the conference using URL: ${locationURL}`);

        if (navigator.product === 'ReactNative') {
            dispatch(appNavigate(toURLString(newURL)));
        } else {
            dispatch(reloadWithStoredParams());
        }
    };
}

/**
 * Adds the current track state to the passed URL.
 *
 * @param {URL} url - The URL that will be modified.
 * @param {Function|Object} stateful - The redux store or {@code getState} function.
 * @returns {URL} - Returns the modified URL.
 */
function addTrackStateToURL(url, stateful) {
    const state = toState(stateful);
    const tracks = state['features/base/tracks'];
    const isVideoMuted = isLocalCameraTrackMuted(tracks);
    const isAudioMuted = isLocalTrackMuted(tracks, MEDIA_TYPE.AUDIO);

    return addHashParamsToURL(new URL(url), { // use new URL object in order to not pollute the passed parameter.
        'config.startWithAudioMuted': isAudioMuted,
        'config.startWithVideoMuted': isVideoMuted
    });

}

/**
 * Reloads the page by restoring the original URL.
 *
 * @returns {Function}
 */
export function reloadWithStoredParams() {
    return (dispatch: Dispatch<any>, getState: Function) => {
        const state = getState();
        const { locationURL } = state['features/base/connection'];

        // Preserve the local tracks muted states.
        const newURL = addTrackStateToURL(locationURL, state);
        const windowLocation = window.location;
        const oldSearchString = windowLocation.search;

        windowLocation.replace(newURL.toString());

        if (newURL.search === oldSearchString) {
            // NOTE: Assuming that only the hash or search part of the URL will
            // be changed!
            // location.replace will not trigger redirect/reload when
            // only the hash params are changed. That's why we need to call
            // reload in addition to replace.
            windowLocation.reload();
        }
    };
}

/**
 * Check if the welcome page is enabled and redirects to it.
 * If requested show a thank you dialog before that.
 * If we have a close page enabled, redirect to it without
 * showing any other dialog.
 *
 * @param {Object} options - Used to decide which particular close page to show
 * or if close page is disabled, whether we should show the thankyou dialog.
 * @param {boolean} options.showThankYou - Whether we should
 * show thank you dialog.
 * @param {boolean} options.feedbackSubmitted - Whether feedback was submitted.
 * @returns {Function}
 */
export function maybeRedirectToWelcomePage(options: Object = {}) {
    return (dispatch: Dispatch<any>, getState: Function) => {

        const {
            enableClosePage
        } = getState()['features/base/config'];

        // if close page is enabled redirect to it, without further action
        if (enableClosePage) {
            if (isVpaasMeeting(getState())) {
                redirectToStaticPage('/');

                return;
            }

            const { jwt } = getState()[stateType.jwt];

            let hashParam;

            // save whether current user is guest or not, and pass auth token,
            // before navigating to close page
            jitsiLocalStorage.setItem('guest', !jwt);
            jitsiLocalStorage.setItem('jwt', jwt);

            let path = 'close.html';

            if (interfaceConfig.SHOW_PROMOTIONAL_CLOSE_PAGE) {
                if (Number(API_ID) === API_ID) {
                    hashParam = `#meet_hour_external_api_id=${API_ID}`;
                }
                path = 'close3.html';
            } else if (!options.feedbackSubmitted) {
                const { room } = getState()['features/base/conference'];
                const roomName = getBackendSafeRoomName(room);
                const getValue = getState()[stateType.mtApi].desktopapp;

                hashParam = `${roomName ? `?roomName=${roomName}&desktopapp=${getValue ? getValue : 0}` : `?desktopapp=${getValue ? getValue : 0}`}`;
                path = 'close2.html';
            }

            dispatch(redirectToStaticPage(`static/${path}`, hashParam));

            return;
        }

        // else: show thankYou dialog only if there is no feedback or Close page is closed
        if (options.showThankYou || !enableClosePage) {
            dispatch(showNotification({
                titleArguments: { appName: getName() },
                titleKey: 'dialog.thankYou'
            }));

            if (navigator.product !== 'ReactNative') {
                const ele = document.getElementById('preloaderConference');

                if (ele) {
                    ele.innerHTML = `<div class="text-center"><style>
                    #preloaderConference::before {
                        content: '';
                        position: absolute;
                        top: calc(10% - 30px);
                        left: calc(50% - 30px);
                        width: 60px;
                        height: 2000px;
                        color: rgb(26, 204, 141);
                        border-width: 6px;
                        border-style: solid;
                        border-color: transparent !important;
                        border-image: initial;
                        border-radius: 50%;
                        animation: none;
                        border-color: transparent;
                        font-size: 30px;
                    }
                    </style>
                    <br/><br/>
                    <div style="position:relative;top:35vh;margin:0;text-align:center;">
                    <img src="${interfaceConfig.DEFAULT_LOGO_URL}"
                    alt="companylogo" style="width:180px;position:relative;margin:0;"/>
                    <br/><br/>
                    <h4 style="color: #fff;
                    font-family: Poppins, sans-serif;
                    font-size: 20px !important;
                    font-weight: 500;
                    line-height: 1.2;
                ">${i18next.t('dialog.thankYou')}
            </h4></div></div>`;
                    ele.style.zIndex = '9999';
                    ele.style.background = typeof interfaceConfig !== 'undefined'
                        && interfaceConfig?.applyMeetingSettings === true ? interfaceConfig?.DEFAULT_BACKGROUND : '#333666';
                    ele.style.opacity = '100%';
                    ele.style.display = 'block';
                }
            }

        } else if (getState()['features/base/config'].enableWelcomePage) {
            // if Welcome page is enabled redirect to welcome page after 3 sec, if
            // there is a thank you message to be shown, 0.5s otherwise.
            setTimeout(
                () => {
                    dispatch(redirectWithStoredParams('/'));
                },
                options.showThankYou ? 3000 : 500);
        }
    };
}
