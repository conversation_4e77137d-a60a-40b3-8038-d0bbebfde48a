/* @flow */
/* eslint-disable */

import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import _ from 'lodash';
import React, { Component } from 'react';
import { Text, TextInput, View, TouchableOpacity, Linking } from 'react-native';
import { connect as reduxConnect } from 'react-redux';
import type { Dispatch } from 'redux';
import jwtDecode from 'jwt-decode';

import { ColorSchemeRegistry } from '../../../base/color-scheme';
import { toJid } from '../../../base/connection';
import { connect } from '../../../base/connection/actions.native';
import {
    CustomSubmitDialog,
    FIELD_UNDERLINE,
    PLACEHOLDER_COLOR,
    _abstractMapStateToProps,
    inputDialog as inputDialogStyle
} from '../../../base/dialog';
import { translate } from '../../../base/i18n';
import { setJWT, setJwtStorage } from '../../../base/jwt/actions';
import { MHConnectionErrors } from '../../../base/lib-meet-hour';
import type { StyleType } from '../../../base/styles';
import { cancelLogin } from '../../actions';

// Register styles.
import './styles';
import { setAccessTokenNative } from '../../../base/mtApi';
import { updateSettings } from '../../../base/settings';
import moment from 'moment';
import { isDomainWeborNative } from '../../../base/util/checkOS';
import { apiKEY } from '../../../base/config/constants';
import { sha256 } from 'js-sha256';


/**
 * The type of the React {@link Component} props of {@link LoginDialog}.
 */
type Props = {

    /**
     * {@link MHConference} that needs authentication - will hold a valid
     * value in XMPP login + guest access mode.
     */
    _conference: Object,

    /**
     * The server hosts specified in the global config.
     */
    _configHosts: Object,

    /**
     * Indicates if the dialog should display "connecting" status message.
     */
    _connecting: boolean,

    /**
     * The color-schemed stylesheet of the base/dialog feature.
     */
    _dialogStyles: StyleType,

    /**
     * The error which occurred during login/authentication.
     */
    _error: Object,

    /**
     * The progress in the floating range between 0 and 1 of the authenticating
     * and upgrading the role of the local participant/user.
     */
    _progress: number,

    /**
     * The color-schemed stylesheet of this feature.
     */
    _styles: StyleType,

    /**
     * Redux store dispatch method.
     */
    dispatch: Dispatch<any>,

    /**
     * Invoked to obtain translated strings.
     */
    t: Function,

    /**
     * roomName
     */
    roomName: string,
};

/**
 * The type of the React {@link Component} state of {@link LoginDialog}.
 */
type State = {

    /**
     * The user entered password for the conference.
     */
    password: string,

    /**
     * The user entered local participant name.
     */
    username: string,

    /**
     * User's access token.
     */
    accessToken: string,

    /**
     * Message prompted if error
     */
     messageError: Object,

     /**
      * _connecting
      */
      _connecting: boolean
};

/**
 * Dialog asks user for username and password.
 *
 * First authentication configuration that it will deal with is the main XMPP
 * domain (config.hosts.domain) with password authentication. A LoginDialog
 * will be opened after 'CONNECTION_FAILED' action with
 * 'MHConnectionErrors.PASSWORD_REQUIRED' error. After username and password
 * are entered a new 'connect' action from 'features/base/connection' will be
 * triggered which will result in new XMPP connection. The conference will start
 * if the credentials are correct.
 *
 * The second setup is the main XMPP domain with password plus guest domain with
 * anonymous access configured under 'config.hosts.anonymousdomain'. In such
 * case user connects from the anonymous domain, but if the room does not exist
 * yet, Jicofo will not allow to start new conference. This will trigger
 * 'CONFERENCE_FAILED' action with MHConferenceErrors.AUTHENTICATION_REQUIRED
 * error and 'authRequired' value of 'features/base/conference' will hold
 * the {@link MHConference} instance. If user decides to authenticate, a
 * new/separate XMPP connection is established and authentication is performed.
 * In case it succeeds, Jicofo will assign new session ID which then can be used
 * from the anonymous domain connection to create and join the room. This part
 * is done by {@link MHConference#authenticateAndUpgradeRole} in
 * lib-meet-hour.
 *
 * See {@link https://github.com/jitsi/jicofo#secure-domain} for a description
 * of the configuration parameters.
 */
class LoginDialog extends Component<Props, State> {
    /**
     * Initializes a new LoginDialog instance.
     *
     * @param {Object} props - The read-only properties with which the new
     * instance is to be initialized.
     */
    constructor(props: Props) {
        super(props);

        this.state = {
            username: '',
            password: '',
            accessToken: '',
            messageError: {
                key: ''
            },
            _connecting: false
        };

        // Bind event handlers so they are only bound once per instance.
        this._onCancel = this._onCancel.bind(this);
        this._onLogin = this._onLogin.bind(this);
        this._onPasswordChange = this._onPasswordChange.bind(this);
        this._onUsernameChange = this._onUsernameChange.bind(this);
    }

    // eslint-disable-next-line require-jsdoc
    componentDidUpdate(prevProps, prevState) {
        if (this.state.accessToken !== prevState.accessToken) {

            const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = getState()['features/base/config'];

            const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

            // not using this dialog
            axios(`${API_URL_PREFIX}${API_VERSION_PREFIX}/customer/user_details`, {
                method: 'post',
                headers: {
                    'content-Type': 'application/json',
                    'Authorization': `Bearer ${this.state.accessToken}`
                },
                data: {
                    client_id: MH_CLIENT_ID,
                    credentials: mesh256,
                    ...isDomainWeborNative,
                    meeting_id: this.props.roomName
                }
            }).then(async (res2) => {

                if (res2.status === 200) {
                    const jwt = res2?.data?.data?.jwt;
                    const jwtPayload = jwt ? jwtDecode(jwt) : null;
                    const jwtHeader = jwt ? jwtDecode(jwt, { header: true }) : null
                    const accessToken = jwtHeader?.access_token;
                    if(accessToken){
                        this.props.dispatch(setAccessTokenNative(accessToken))
                        this.props.dispatch(updateSettings({
                            accessToken
                        }))
                    }

                    if (jwtPayload?.moderator === true) {
                        this.props.dispatch(setJWT(jwt));
                        const r = this.props.dispatch(connect());

                        return r;
                    }
                     else if(jwt && !jwtPayload?.moderator) {
                        this.props.dispatch(setJwtStorage(jwt));
                        const r = this.props.dispatch(connect());
                        
                        return r;
                     } else {
                        this.setState(prevState => {
                            return {
                                ...prevState,
                                messageError: {
                                   key: 'dialog.messageErrorNotModerator'
                                },
                                _connecting: false
                            }
                        })

                     }
                }
            })
    .catch(e => console.error('errbois', e));
        }
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const {
            _connecting: connecting,
            _dialogStyles,
            _styles: styles,
            t
        } = this.props;
        const { _connecting} = this.state

        return (
            <CustomSubmitDialog
                okDisabled = { _connecting }
                onCancel = { this._onCancel }
                onSubmit = { this._onLogin }
                okKey = { _connecting ? 'presenceStatus.connecting' : 'dialog.Ok' }>
                <View style = { styles.loginDialog }>
                    <Text
                        style = {{ fontSize: 20,
                            fontWeight: 'bold',
                            textAlign: 'center' }}>Login</Text>
                    <TextInput
                        autoCapitalize = { 'none' }
                        autoCorrect = { false }
                        onChangeText = { this._onUsernameChange }
                        placeholder = { '<EMAIL>' }
                        placeholderTextColor = { PLACEHOLDER_COLOR }
                        style = { _dialogStyles.field }
                        underlineColorAndroid = { FIELD_UNDERLINE }
                        value = { this.state.username } />
                    <TextInput
                        autoCapitalize = { 'none' }
                        onChangeText = { this._onPasswordChange }
                        placeholder = { t('dialog.userPassword') }
                        placeholderTextColor = { PLACEHOLDER_COLOR }
                        secureTextEntry = { false }
                        style = { [
                            _dialogStyles.field,
                            inputDialogStyle.bottomField
                        ] }
                        underlineColorAndroid = { FIELD_UNDERLINE }
                        value = { this.state.password } />
                    { this._renderMessage() }
                    <TouchableOpacity
                        style = {{ flexDirection: 'row',
                            margin: 10 }}
                        onPress = { () => Linking.openURL('https://meet.groupvi.systems/register') }>
                        <Text
                            style = {{ fontSize: 14,
                                margin: 2,
                                color: '#333666',
                                textAlign: 'center' }} >
                            Don't have an account?
                        </Text>
                        <Text
                            style = {{ fontSize: 14,
                                margin: 2,
                                color: '#333666',
                                textAlign: 'center',
                                textDecorationLine: 'underline' }} >
                            Register
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style = {{ flexDirection: 'row',
                            margin: 10 }}
                        onPress = { () => Linking.openURL('https://meet.groupvi.systems/meetings/create') }>
                        <Text
                            style = {{ fontSize: 14,
                                margin: 2,
                                textAlign: 'left' }} >
                            Note: Schedule a meeting from
                        </Text>
                        <Text
                            style = {{ fontSize: 14,
                                margin: 2,
                                color: '#333666',
                                textAlign: 'center',
                                textDecorationLine: 'underline' }} >
                            Portal
                        </Text>
                    </TouchableOpacity>
                </View>
            </CustomSubmitDialog>
        );
    }

    /**
     * Renders an optional message, if applicable.
     *
     * @returns {ReactElement}
     * @private
     */
    _renderMessage() {
        const {
            _connecting: connecting,
            _error: error,
            _progress: progress,
            _styles: styles,
            t
        } = this.props;
        const {
            messageError
        } = this.state;

        let messageKey;
        let messageIsError = false;
        const messageOptions = {};

        if (progress && progress < 1) {
            messageKey = 'connection.FETCH_SESSION_ID';
        } else if (error) {
            const { name } = error;

            if (name === MHConnectionErrors.PASSWORD_REQUIRED) {
                // Show a message that the credentials are incorrect only if the
                // credentials which have caused the connection to fail are the
                // ones which the user sees.
                const { credentials } = error;

                if (credentials
                        && credentials.jid
                            === toJid(
                                this.state.username,
                                this.props._configHosts)
                        && credentials.password === this.state.password) {
                    messageKey = 'dialog.incorrectPassword';
                    messageIsError = true;
                }
            } else if (name) {
                messageKey = 'dialog.connectErrorWithMsg';
                messageOptions.msg = `${name} ${error.message}`;
                messageIsError = true;
            }
        } else if (connecting) {
            messageKey = 'connection.CONNECTING';
        }

        if (!_.isEmpty(messageError?.key)) {
            const { messageError } = this.state
            const message = t(messageError.key);
            const messageStyles = [
                styles.dialogText,
                styles.errorMessage
            ];

            return (
                <Text style = { messageStyles }>
                    { message }
                </Text>
            );
        }

        return null;
    }

    _onUsernameChange: (string) => void;

    /**
     * Called when user edits the username.
     *
     * @param {string} text - A new username value entered by user.
     * @returns {void}
     * @private
     */
    _onUsernameChange(text) {
        this.setState({
            username: text.trim()
        });
    }

    _onPasswordChange: (string) => void;

    /**
     * Called when user edits the password.
     *
     * @param {string} text - A new password value entered by user.
     * @returns {void}
     * @private
     */
    _onPasswordChange(text) {
        this.setState({
            password: text
        });
    }

    _onCancel: () => void;

    /**
     * Notifies this LoginDialog that it has been dismissed by cancel.
     *
     * @private
     * @returns {void}
     */
    _onCancel() {
        this.props.dispatch(cancelLogin());
    }

    _onLogin: () => void;

    /**
     * Notifies this LoginDialog that the login button (OK) has been pressed by
     * the user.
     *
     * @private
     * @returns {void}
     */
    _onLogin() {
        const { password, username } = this.state;
        if(_.isEmpty(username) || _.isEmpty(password)){
            this.setState(prevState => {
                return { ...prevState,
                    messageError: {
                        key: 'dialog.messageErrorNull'
                    }
                }})
                return;
        }
        this.setState(prevState => {
            return{
                ...prevState,
                _connecting: true
            }
        })
        const loginApi = {
            grant_type: 'password',
            client_secret: '6b703a9a2574e3dfb6c1b671589840ac54b4d69098bd8ab3467c2fd615e9312b',
            client_id: config.MH_CLIENT_ID,
            username,
            password
        };


        axios('https://meet.groupvi.systems/oauth/token', {
            method: 'post',
            headers: {
                'content-Type': 'application/json'
            },
            data: JSON.stringify(loginApi)
        }).then(res => {
            if (res.status === 200) {
                const resData = res.data;
                if (resData.access_token) {
                    this.setState({
                        accessToken: resData.access_token
                    });
                } else {
                    this.setState(prevState => {
                        return { ...prevState,
                            messageError: {
                                key: 'dialog.messageErrorApi'
                            },
                            _connecting: false
                        }})
                }


            } 
        })
.catch(e => {
    this.setState(prevState => {
        return { ...prevState,
            messageError: {
                key: 'dialog.messageErrorInvalid'
            },
            _connecting: false
        }})
});


        // const { _conference: conference, dispatch } = this.props;
        // const jid = toJid(username, this.props._configHosts);
        // let r;

        // // If there's a conference it means that the connection has succeeded,
        // // but authentication is required in order to join the room.
        // if (conference) {
        //     r = dispatch(authenticateAndUpgradeRole(jid, password, conference));
        // } else {
        //     r = dispatch(connect(jid, password));
        // }

        // return r;
    }
}

/**
 * Maps (parts of) the Redux state to the associated props for the
 * {@code LoginDialog} component.
 *
 * @param {Object} state - The Redux state.
 * @private
 * @returns {Props}
 */
function _mapStateToProps(state) {
    const {
        error: authenticateAndUpgradeRoleError,
        progress,
        thenableWithCancel
    } = state['features/authentication'];
    const { authRequired } = state['features/base/conference'];
    const { hosts: configHosts } = state['features/base/config'];
    const {
        connecting,
        error: connectionError
    } = state['features/base/connection'];

    return {
        ..._abstractMapStateToProps(state),
        _conference: authRequired,
        _configHosts: configHosts,
        _connecting: Boolean(connecting) || Boolean(thenableWithCancel),
        _error: connectionError || authenticateAndUpgradeRoleError,
        _progress: progress,
        _styles: ColorSchemeRegistry.get(state, 'LoginDialog'),
        roomName: state['features/base/conference'].room
    };
}

export default translate(reduxConnect(_mapStateToProps)(LoginDialog));
