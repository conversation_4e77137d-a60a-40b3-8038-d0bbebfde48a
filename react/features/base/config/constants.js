/**
 * The prefix of the {@code localStorage} key into which {@link storeConfig}
 * stores and from which {@link restoreConfig} restores.
 *
 * @protected
 * @type string
 */
export const _CONFIG_STORE_PREFIX = 'config.js';

/**
 * The list of all possible UI buttons.
 *
 * @protected
 * @type Array<string>
 */
export const TOOLBAR_BUTTONS = [
    'microphone', 'camera', 'closedcaptions', 'desktop', 'embedmeeting', 'fullscreen',
    'fodeviceselection', 'hangup', 'profile', 'chat', 'recording', 'reactions',
    'livestreaming', 'etherpad', 'genericiframe', 'sharedvideo', 'settings', 'raisehand',
    'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
    'tileview', 'select-background', 'download', 'help', 'mute-everyone', 'mute-video-everyone', 'security'
];


/**
 * Api Url for joining prejoin.
 */
export const PrejoinApi = 'meeting/save_meeting_guest_details';

export const apiKEY = '800ca36dd718208c895cca9bd0228cab6be54825a3ee59b3807222e0d68ef145';

export const mtDomain = 'meet.groupvi.systems';

export const androidPackageId = 'meet.groupvi.systems';

export const iosPackageId = 'meet.groupvi.systems';


