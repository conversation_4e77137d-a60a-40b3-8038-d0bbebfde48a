
// @flow
import { Box, makeStyles } from '@material-ui/core';
import _ from 'lodash';
import React, { useState } from 'react';

import { GrowUpDown, UpperParentDiv } from './Styles';

type Props = {

    /**
     * Align Text.
     */
    align: ?string,

    /**
     * Children.
     */
    children: any,

    /**
     * Height of the dialog.
     */
     height: string,

    /**
     * Dialog Color String.
     */
    dialogColor: ?string,

    /**
     * IsOpen.
     */
     isOpen: ?boolean,

     /**
      * OnDialogHandle.
      */
      onDialogHandle: ?Function,

      /*
      urlFunction
       */
      urlFunction: ?Function,

      /**
       * OnTimeOutHandle.
       */
       onTimeOutHandle: ?Function

}


const useStyles = makeStyles(() => {
    return {
        signinLi: {
            '& a': {
                display: 'inline-block',
                backgroundColor: '#036531',
                color: '#fff',
                padding: '7px 14px !important',
                borderRadius: '5px 5px 0px 0px',
                '&:hover': {
                    backgroundColor: '#334560'
                },
                '&:before': {
                    content: 'none !important'
                }
            }

        },
        upperDialog: { alignItems: 'center',
            width: '95px',
            justifyContent: 'center',
            display: 'flex',
            cursor: 'pointer',
            background: 'rgb(92,125,175)',
            textAlign: 'center',
            marginLeft: '10px',
            marginRight: '4px',
            color: '#fff',
            borderRadius: '5px 5px 0px 0px' }
    };
});


/**
 * Upper Dialog.
 *
 * @returns {any}
 */
export default function UpperDialog({
    align,
    children,

    // height = '25%',
    isOpen,
    urlFunction,
    onTimeOutHandle,
    onDialogHandle,
    dialogColor
}: Props) {

    const [ upDown, setUpDown ] = useState(false);
    const elementRef = React.useRef();
    const isMounted = React.useRef(false);
    const divRef = React.useRef();
    const [ userHeight, setUserHeight ] = React.useState(0);
    const [ dialogHeight, setDialogHeight ] = React.useState('0');
    const styles = useStyles();

    React.useEffect(() => {
        if (isOpen === true) {
            setOpen();
        }

        isMounted.current = true;
    }, [ JSON.stringify(isOpen) ]);

    /**
     * Open Dialog.
     *
     * @returns {void}
     */
    function setOpen() {
        // const growUp = document.getElementById('growUpDown');

        if (divRef?.current?.clientHeight && divRef?.current?.clientHeight > 0) {

            onDialogHandle();
            setUpDown(false);
            setDialogHeight('0');
            setTimeout(() => {
                setUserHeight(0);
                onTimeOutHandle && onTimeOutHandle(15000);
            }, 500);
        } else {
            // console.log('grppp', growUp);
            setUpDown(true);
            setUserHeight('25%');
            setTimeout(() => {
                setDialogHeight('100vh');

                // divRef.current.height = '100vh';
            }, 500);
        }

    }

    React.useEffect(() => {
        const myElementToCheckIfClicksAreInsideOf = document.querySelector('#idd');

        document.body.addEventListener('click', e => {

            if (!_.isNil(divRef?.current?.clientHeight) && divRef?.current?.clientHeight !== 0 && !myElementToCheckIfClicksAreInsideOf.contains(e.target)) {
                setDialogHeight('0');
                setTimeout(() => {
                    setUserHeight(0);
                    onTimeOutHandle && onTimeOutHandle(15000);
                }, 500);
                onDialogHandle();
                setUpDown(false);
            }

        });

    }, []);

    const setIcon = () => {
        if (align !== 'top') {
            if (upDown) {
                return 'Close Ad';
            }

            return 'Open Ad';
        }

        // console.log('dfrt', upDown);
        if (upDown) {
            return 'Close Ad';
        }


        return 'Open Ad';
    };


    return (
        <div
            id = 'idd'
            ref = { elementRef }>
            <UpperParentDiv
                height = { userHeight }>
                <GrowUpDown
                    dialogColor = { dialogColor }
                    height = { dialogHeight }
                    id = 'growUpDown'
                    innerRef = { divRef }>
                    { children }
                </GrowUpDown>
                {
                    <Box display = { 'flex' }>
                        <div
                            className = { styles.upperDialog }
                            onClick = { setOpen }>
                            { setIcon() }
                        </div>
                        { urlFunction && <div className = { styles.signinLi }>
                            <a
                                href = { urlFunction() }
                                rel = 'noopener noreferrer'
                                target = '_blank'>
                                Remove Ads
                            </a>
                        </div>}
                    </Box>

                }
            </UpperParentDiv>

        </div>
    );
}
