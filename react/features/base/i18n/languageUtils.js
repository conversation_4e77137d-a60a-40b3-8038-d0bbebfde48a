// @flow

import logger from '../../analytics/logger';

import i18next, { DEFAULT_LANGUAGE, LANGUAGES } from './i18next';

/**
 * Initializes the language from user details API response.
 * Sets the language based on data.strip_details.language_preference,
 * defaults to 'en' if no data is available.
 *
 * @param {Object} userDetails - The user details object from the API response.
 * @returns {string} The language code that was set.
 */
export function initializeLanguageFromUserDetails(userDetails: ?Object): string {
    let languageToSet = DEFAULT_LANGUAGE;

    try {
        // Extract language preference from user details
        const languagePreference = userDetails?.strip_details?.language_preference;

        if (languagePreference && typeof languagePreference === 'string') {
            // Validate that the language is supported
            if (LANGUAGES.includes(languagePreference)) {
                languageToSet = languagePreference;
                logger.info(`Setting language from user preference: ${languagePreference}`);
            } else {
                logger.warn(`Unsupported language preference: ${languagePreference}, using default: ${DEFAULT_LANGUAGE}`);
            }
        } else {
            logger.info(`No language preference found in user details, using default: ${DEFAULT_LANGUAGE}`);
        }

        // Set the language in i18next
        i18next.changeLanguage(languageToSet);

    } catch (error) {
        logger.error('Error initializing language from user details:', error);

        // Fallback to default language
        i18next.changeLanguage(DEFAULT_LANGUAGE);
        languageToSet = DEFAULT_LANGUAGE;
    }

    return languageToSet;
}
