// @flow
import { BrowserDetection, jitsiLocalStorage } from '@jitsi/js-utils';
import axios from 'axios';
import { sha256 } from 'js-sha256';
import _, { isNil } from 'lodash';
import moment from 'moment';
import type { Dispatch } from 'redux';

import {
    CHROME,
    FIREFOX,
    INTERNET_EXPLORER,
    OPERA,
    SAFARI
} from '../../../../browser';
import { updateDropboxToken } from '../../dropbox';
import { showErrorNotification } from '../../notifications';
import { RECORDING_TYPES, getSessionStatusToShow } from '../../recording';
import { toggleRequestingSubtitles } from '../../subtitles/actions';
import logger from '../app/logger';
import { getCurrentConference } from '../conference';
import { apiKEY } from '../config/constants';
import { JWT_STORAGE, SET_RENDER_CONTENT, setJWT, setJwtStorage } from '../jwt';
import { MHRecordingConstants } from '../lib-meet-hour';
import { getLocalParticipant } from '../participants';
import { getBackendSafeRoomName } from '../util';
import { isDomainWeborNative } from '../util/checkOS';

import {
    AUTO_RECORD_STOP, INVALID_MEETING_JSON, IS_RECORDING_AND_LIVESTREAMING_ON, PRE_REG_API,
    SEND_AUTO_RECORD_COMMAND, SET_ACCESS_TOKEN_NATIVE, SET_ATTENDANCE_API, SET_AUTO_PASSWORD, SET_DESKTOP_APP, SET_IS_SWITCHING_BREAKOUT_ROOM, SET_PASSWORD_FORM_NATIVE,
    SET_USER_DETAILS, SET_VOICE_COMMAND_STRING
} from './actionTypes';
import { ULR_PATHS } from './constants';
import { fetchApi, fetchAxiosApi } from './functions';

declare var config: Object;

/**
* Maps the names of the browsers from bowser to the internal names defined in
* ./browsers.js.
*/
const bowserNameToJitsiName = {
    'Chrome': CHROME,
    'Chromium': CHROME,
    'Opera': OPERA,
    'Firefox': FIREFOX,
    'Internet Explorer': INTERNET_EXPLORER,
    'Safari': SAFARI
};

export const recordingandLivestreamingOn = value => {
    return {
        type: IS_RECORDING_AND_LIVESTREAMING_ON,
        isRecordingandLivestreamingOn: value
    };
};


/**
 *  Fecth Api of the conference.
 *
 * @returns {Object}
 */
export function fetchpreRegApi() {
    return async (dispatch: Dispatch<any>, getState: Function) => {
        const room = getBackendSafeRoomName(getState()['features/base/conference'].room);
        const { MH_CLIENT_ID } = getState()['features/base/config'];

        const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
        const pCode = getState()['features/base/mtApi'].md5password;
        const isPCode = isNil(pCode) ? {} : {
            pcode: pCode
        };
        const preRegObject = {
            client_id: MH_CLIENT_ID,
            credentials: mesh256,
            ...isDomainWeborNative,
            'meeting_id': room,
            ...isPCode
        };
        const preRegString = JSON.stringify(preRegObject);
        const state1 = getState()['features/base/jwt'];
        const usersAccessToken = jitsiLocalStorage.getItem('accessToken');
        const isAccessToken = state1.accessToken ?? usersAccessToken === 'null' ? null : usersAccessToken;
        const preRegApi = await fetchApi('meeting/pre_registration_check',
            'post',
            isAccessToken ? {
                'Authorization': `Bearer ${isAccessToken}`,
                'Content-Type': 'application/json'
            } : {
                'Content-Type': 'application/json'
            }, preRegString);
        const preRegData = await preRegApi;


        const isNotValid = Boolean(!preRegData.success);
        const mhAd = document.querySelector('#mhAd') || null;

        const isjwtStored = getState()['features/base/jwt'].jwt ?? getState()['features/base/jwt'].jwtData;

        if (typeof preRegData === 'object' && preRegData.hasOwnProperty('display_ads') && preRegData?.display_ads === false) {
            mhAd && mhAd.remove();
        }

        if (isNotValid && isjwtStored) {
            const jwt = undefined;
            const jwtStorage = undefined;

            dispatch(
                setJWT(jwt));
            dispatch(
                {
                    type: JWT_STORAGE,
                    jwtData: jwtStorage
                });
        }
        const userAccessToken = jitsiLocalStorage.getItem('accessToken') ?? null;
        const accessTokenExists = getState()['features/base/jwt'].accessToken ?? userAccessToken === 'null' ? null : userAccessToken;

        if ((_.isNil(isjwtStored) && _.isNil(accessTokenExists)) || config?.iAmRecorder === true) {
            dispatch(setJWTforGuest());

        }

        return dispatch({
            type: PRE_REG_API,
            preRegData

        });


    };
}

/**
 *  Fecth Api of the conference.
 *
 * @returns {Object}
 */
export function fetchPreRegNativeApi() {
    return async (dispatch: Dispatch<any>, getState: Function) => {
        try {
            // First, dispatch an action to indicate loading
            dispatch({
                type: PRE_REG_API,
                preRegData: { loading: true }
            });

            const room = getBackendSafeRoomName(getState()['features/base/conference'].room);
            const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = getState()['features/base/config'];
            const moement = moment().seconds(0)
                .milliseconds(0)
                .format('X');

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
            const pCode = getState()['features/base/mtApi'].md5password;
            const isPCode = isNil(pCode) ? {} : {
                pcode: pCode
            };
            const preRegObject = {
                client_id: MH_CLIENT_ID,
                credentials: mesh256,
                ...isDomainWeborNative,
                'meeting_id': room,
                ...isPCode
            };

            const preRegString = JSON.stringify(preRegObject);
            const urlConfg = `${API_URL_PREFIX}${API_VERSION_PREFIX}meeting/pre_registration_check`;
            const preRegApi = await axios({
                url: urlConfg,
                method: 'post',
                data: preRegString,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const preRegData = preRegApi.data;

            const isNotValid = Boolean(!preRegData.success);
            const isjwtStored = getState()['features/base/jwt'].jwt ?? getState()['features/base/jwt'].jwtData;

            if (isNotValid && isjwtStored) {
                const jwt = undefined;
                const jwtStorage = undefined;

                dispatch(setJWT(jwt));
                dispatch({
                    type: JWT_STORAGE,
                    jwtData: jwtStorage
                });
            }

            const accessTokenExists = getState()['features/base/jwt']?.accessToken;

            if (_.isNil(isjwtStored) && _.isNil(accessTokenExists)) {
                dispatch(setJWTforGuest());
            }

            return dispatch({
                type: PRE_REG_API,
                preRegData
            });
        } catch (error) {
            logger.error('Error in fetchPreRegNativeApi:', error);

            // Dispatch error state
            return dispatch({
                type: PRE_REG_API,
                preRegData: { success: false,
                    error: error.message }
            });
        }
    };
}

/**
 * Sets the default page index of {@link WelcomePageLists}.
 *
 * @param {number} userd - Object details of the user.
 * @returns {{
 *     type: SET_WELCOME_PAGE_LISTS_DEFAULT_PAGE,
 *     pageIndex: number
 * }}
 */
export function setuserDetails(userd: ?Object) {
    return {
        type: SET_USER_DETAILS,
        userd
    };
}


export const setAccessTokenNative = (tokn: string) => {
    return {
        type: SET_ACCESS_TOKEN_NATIVE,
        tokn
    };
};


export const setPasswordForm = (formBool: boolean) => {

    return {
        type: SET_PASSWORD_FORM_NATIVE,
        formBool
    };
};


export const setAttendanceApi = logout => {
    return {
        type: SET_ATTENDANCE_API,
        isLogout: logout ? logout : false
    };
};

export const setInvalidMeeting = data => {
    return {
        type: INVALID_MEETING_JSON,
        data
    };
};

/**
 *  User Attendance when it joins the conference.
 *
 * @param {Object} getState - State Reducer .
 * @returns {any}
 */
export async function sendUserAttendance({ getState, isLogout = false }) {
    const state = getState();
    const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = state['features/base/config'];

    try {
        const format1 = 'YYYY-MM-DD HH:mm:ss';
        const currentDateTime = moment().utc()
            .format(format1);
        const bowser = new BrowserDetection();
        const browserName = bowser.getName();
        let accessType = 'web';
        const participant = getLocalParticipant(state);

        for (const i in bowserNameToJitsiName) {
            if (i !== browserName) {
                accessType = 'app';
            }
        }
        const roomName = getState()['features/base/conference'].room;
        let dataJson;

        try {
            const ClientIP = await axios({
                url: 'https://api.ipify.org/?format=json',
                timeout: 8000,
                method: 'get',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            // eslint-disable-next-line no-negated-condition
            if (typeof ClientIP !== 'undefined') {

                const ClientIPData = await ClientIP?.data;

                const dataGeo = `${API_URL_PREFIX}${API_VERSION_PREFIX}geolocation?api_key=${apiKEY}&ip_address=${ClientIPData?.ip}`;
                const getData = await axios({
                    url: dataGeo,
                    timeout: 8000,
                    method: 'get',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).catch(e => logger.error('Error in fetching geolocation bros', e?.status));

                // eslint-disable-next-line max-depth
                if (getData) {
                    dataJson = await getData?.data;
                }
            }
        } catch (error) {
            logger.error('Error in fetching geolocation bros', error);

        }


        const { occurenceId } = state['features/base/mtApi'];

        const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');


        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

        let userObject = {};

        if (isLogout === true) {
            userObject = {
                logout_datetime: currentDateTime

            };
        } else {
            userObject = {
                login_datetime: currentDateTime

            };
        }

        userObject = {
            ...userObject,
            client_id: MH_CLIENT_ID,
            credentials: mesh256,
            ...isDomainWeborNative,
            // eslint-disable-next-line camelcase
            meeting_id: roomName,
            name: participant?.name,
            // eslint-disable-next-line camelcase
            occurrence_id: occurenceId,
            // eslint-disable-next-line camelcase
            is_host: state['features/base/jwt'].jwt ? 1 : 0,
            // eslint-disable-next-line camelcase
            email_id: participant?.email,
            // eslint-disable-next-line camelcase
            user_agent: browserName,
            // eslint-disable-next-line camelcase
            access_type: accessType,
            ip: dataJson?.IPv4,
            geolocation: dataJson
        };


        const userJson = JSON.stringify(userObject);
        const prejoinAP = `${API_URL_PREFIX}${API_VERSION_PREFIX}meeting/save_meeting_attendance`;

        await axios({
            url: prejoinAP,
            timeout: 8000,
            method: 'post',
            headers: { 'Content-Type': 'application/json' },
            data: userJson
        }).catch(es => logger.error('Error in fetching prejoin api For guest', es));

        // await sendUserData.json();
    } catch (e) {
        logger.error('Error in fetching geolocation');
    }
}


export const setVoiceCommandString = (strng: string) => {
    return {
        type: SET_VOICE_COMMAND_STRING,
        vsString: strng
    };
};

/**
 * Sets jwt to guests user for interface config and config overwrite.
 *
 * @returns {any}
 */
export function setJWTforGuest() {
    return async (dispatch: Dispatch<any>, getState: Function) => {
        const room = getBackendSafeRoomName(getState()['features/base/conference'].room);
        const isjwtStored = getState()['features/base/jwt'].jwt ?? getState()['features/base/jwt'].jwtData;
        const conf = getState()['features/base/config'];
        const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);


        if (_.isNil(isjwtStored)) {
            const referenceParam = {
                'meeting_id': room,
                client_id: conf.MH_CLIENT_ID,
                credentials: mesh256,
                ...isDomainWeborNative
            };
            const jwtResponseForGuest = await fetchAxiosApi('getjwt',
                'POST',
                {
                    'Content-Type': 'application/json',
                    'Accept': '*'
                }, referenceParam, conf).catch(err => logger.error('error in api', err));


            if ((jwtResponseForGuest?.success && conf.interfaceConfig?.applyMeetingSettings === true) || (jwtResponseForGuest.jwt && conf?.iAmRecorder === true)) {
                dispatch(setJwtStorage(jwtResponseForGuest.jwt));
            }

            return dispatch({
                type: SET_RENDER_CONTENT,
                shouldRender: true
            });

        }
    };
}


/**
 * Auto Live Streaming.
 *
 * @param {any} {dispatch
 * @param {any} getState}
 * @returns {any}
 */
export async function _autoLiveStreaming({ dispatch, getState }) {
    const state = getState();
    const { accessToken, confSettings } = state['features/base/jwt'];
    // eslint-disable-next-line no-shadow, no-unused-vars
    const { interfaceConfig, transcription, dropbox = {}, ...mbConfig } = state['features/base/config'];
    const { _requestingSubtitles } = state['features/subtitles'];
    const { stopAutoRecord = false } = state['features/base/mtApi'];

    if (Boolean(stopAutoRecord) || (confSettings?.AUTO_START_RECORDING !== 1 && confSettings?.AUTO_START_LIVESTREAMING !== 1)) {
        return;
    }

    const conference = getCurrentConference(state);
    const userId = state['features/base/jwt']?.meetingDetails;
    const room = getBackendSafeRoomName(getState()['features/base/conference'].room);
    const { MH_CLIENT_ID, API_URL_PREFIX, API_VERSION_PREFIX } = getState()['features/base/config'];
    const recordURL = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/saverecordings`;
    const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

    const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

    const liveStreamObject = {
        client_id: MH_CLIENT_ID,
        credentials: mesh256,
        ...isDomainWeborNative,
        'meeting_id': room
    };
    const headers = accessToken ? {
        'Content-Type': 'application/json',
        'Authorization': `${'Bearer '}${accessToken}`
    } : {
        'Content-Type': 'application/json'
    };
    const getLiveStreamData = await fetchAxiosApi(ULR_PATHS.AUTO_LIVE_STREAM, 'POST', headers, liveStreamObject, mbConfig);
    const getRecData = await fetchAxiosApi(ULR_PATHS.AUTO_REC_STREAM, 'POST', headers, liveStreamObject, mbConfig);

    // Stopping auto recording if api is failing or if Meet Hour storage consumption is more than available space
    if (!getRecData || (getRecData && getRecData?.success === false) || getRecData?.code === 1032 || getRecData?.code === 1033) {
        dispatch(showErrorNotification({ descriptionKey: 'liveStreaming.makeSureYouHaveEnoughStorageAvailableOnYourAccount',
            titleKey: 'liveStreaming.failToStartAutoRecording' }));

        return;
    }

    // Stopping auto live streaming if api is failing or if Subscription of user doesn't allow livestreaming
    if (!getLiveStreamData || (getLiveStreamData && getLiveStreamData?.success === false) || getRecData?.code === 1206) {
        dispatch(showErrorNotification({ descriptionKey: 'liveStreaming.pleaseContactSupportForAssistance',
            titleKey: 'liveStreaming.failToStartAutoLiveStreaming' }));

        return;
    }

    if (getRecData?.default_recording_storage !== 'Dropbox') {
        dispatch(updateDropboxToken(getRecData?.settings?.dropbox_access_token?.access_token,
            getRecData?.settings?.dropbox_access_token?.refresh_token, getRecData?.settings?.dropbox_access_token?.expires));

    }
    const decryptUrl = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/recordsettings_decrypt`;

    // console.log('getLiveStreamData', getLiveStreamData, getRecData);
    const streamStatus = getSessionStatusToShow(state, MHRecordingConstants.mode.STREAM);
    const recStatus = getSessionStatusToShow(state, MHRecordingConstants.mode.FILE);

    const recData = getRecData?.default_recording_storage === 'Dropbox' ? {
        'token': getRecData?.settings?.dropbox_access_token
    } : {
        settings: getRecData.settings,
        storage: getRecData.default_recording_storage,
        s3_free_space: getRecData?.s3_free_space ? getRecData?.s3_free_space : null,
        s3_used_space: getRecData?.s3_used_space ? getRecData?.s3_used_space : null
    };

    if ((typeof recStatus === 'undefined' || recStatus === MHRecordingConstants.status.OFF)
        && confSettings?.AUTO_START_RECORDING === 1 && (!confSettings.AUTO_START_LIVESTREAMING || confSettings.AUTO_START_LIVESTREAMING !== 1)) {
        const appData = JSON.stringify({
            'file_recording_metadata': {
                'upload_credentials': {
                    'service_name': recData?.storage !== 'Dropbox' && !_.isNil(recData?.storage) ? 'awss3' : RECORDING_TYPES.DROPBOX,
                    'recordingType': recData.storage,
                    ...recData,
                    'userid': userId?.id,
                    _accessToken: accessToken,
                    client_id: MH_CLIENT_ID,
                    credentials: mesh256,
                    ...isDomainWeborNative,
                    decryptUrl,
                    'urlSaved': recordURL,
                    api_key: apiKEY,
                    'startTiming': new Date().getTime(),
                    // eslint-disable-next-line camelcase
                    'meetingid': room
                }
            }
        });

        conference.startRecording({
            mode: MHRecordingConstants.mode.FILE,
            appData
        });
        conference.sendCommand(SEND_AUTO_RECORD_COMMAND, {
            value: true
        });

        if (transcription?.enabled && (confSettings?.AUTO_CAPTION_ON_RECORDING ?? transcription?._autoCaptionOnRecord)) {
            if (confSettings?.TRANSCRIPTION_QUOTA_EXCEEDED === 1 ?? false) {
                dispatch(showErrorNotification(
                    {
                        descriptionKey: 'transcribing.transcriptionQuotaExceeded',
                        titleKey: 'transcribing.transcriptionQuotaExceededTitle'
                    }));
            } else if (_requestingSubtitles === true) {
                // no action
            } else {
                dispatch(toggleRequestingSubtitles());
            }
        }
    }

    if ((((streamStatus === MHRecordingConstants.status.OFF || typeof streamStatus === 'undefined')
        && (recStatus === MHRecordingConstants.status.OFF || typeof recStatus === 'undefined'))) && confSettings?.AUTO_START_LIVESTREAMING === 1) {
        const appData = JSON.stringify({
            'file_recording_metadata': {
                'upload_credentials': {
                    'service_name': recData?.storage !== 'Dropbox' && !_.isNil(recData?.storage) ? 'awss3' : RECORDING_TYPES.DROPBOX,
                    'recordingType': recData.storage,
                    ...recData,
                    'userid': userId?.id,
                    _accessToken: accessToken,
                    client_id: MH_CLIENT_ID,
                    credentials: mesh256,
                    app_key: dropbox.appKey,
                    r_token: typeof getRecData?.settings === 'object' ? getRecData?.settings?.dropbox_refresh_token : null,
                    ...isDomainWeborNative,
                    'urlSaved': recordURL,
                    api_key: apiKEY,
                    decryptUrl,
                    'startTiming': new Date().getTime(),
                    // eslint-disable-next-line camelcase
                    'meetingid': room
                }
            }
        });

        const liveStreamData = Object.values(getLiveStreamData.data).filter(it => it.key !== undefined)
.map(its => its.key);

        if (getLiveStreamData.data?.Recording?.enable === true && liveStreamData.length) {
            dispatch(recordingandLivestreamingOn(true));
            conference.sendCommand(IS_RECORDING_AND_LIVESTREAMING_ON, {
                value: true
            });
        }

        const liveStreamEndObj = getLiveStreamData.data?.Recording?.enable === true && liveStreamData.length ? {
            mode: MHRecordingConstants.mode.FILE,
            appData,
            streamId: liveStreamData[0] ? liveStreamData[0] : null,
            fbstreamId: liveStreamData[1] ? liveStreamData[1] : null
        } : getLiveStreamData.data?.Recording?.enable === true ? {
            mode: MHRecordingConstants.mode.FILE,
            appData
        } : {
            mode: MHRecordingConstants.mode.STREAM,
            streamId: liveStreamData[0] ? liveStreamData[0] : null,
            fbstreamId: liveStreamData[1] ? liveStreamData[1] : null,
            igStreamid: liveStreamData[2] ? liveStreamData[2] : null
        };

        conference.startRecording(liveStreamEndObj);
        conference.sendCommand(SEND_AUTO_RECORD_COMMAND, {
            value: true
        });

        if (transcription?.enabled && (confSettings?.AUTO_CAPTION_ON_RECORDING ?? transcription?._autoCaptionOnRecord)) {
            if (confSettings?.TRANSCRIPTION_QUOTA_EXCEEDED === 1 ?? false) {
                dispatch(showErrorNotification(
                    {
                        descriptionKey: 'transcription.transcriptionQuotaExceeded',
                        titleKey: 'transcription.transcriptionQuotaExceededTitle'
                    }));
            } else if (_requestingSubtitles === true) {
                // no action
            } else {
                dispatch(toggleRequestingSubtitles());
            }
        }
    }


}


export const setAutoPassword = pssword => {
    return {
        type: SET_AUTO_PASSWORD,
        md5password: pssword
    };
};

export const autoRecordStop = autostop => {
    return {
        type: AUTO_RECORD_STOP,
        stopAutoRecord: autostop
    };
};

export const setDesktopApp = val => {
    return {
        type: SET_DESKTOP_APP,
        desktopapp: val
    };
};

export const setIsSwitchingBreakoutRoom = isSwitchingBreakOutRoom => {
    return {
        type: SET_IS_SWITCHING_BREAKOUT_ROOM,
        isSwitchingBreakOutRoom
    };
};

