/* eslint-disable valid-jsdoc */
/* eslint-disable max-len */

// @flow
/* eslint-disable camelcase */
// import { AudioSettingsButton, VideoSettingsButton } from '../../../../toolbox/components/web';
// import { Avatar } from '../../../avatar';

import { jitsiLocalStorage } from '@jitsi/js-utils';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import { ReactInterval } from 'meet-hour-react-interval-hook';
import moment from 'moment';
import React, { PureComponent } from 'react';
import type { Dispatch } from 'redux';

import { _getDefaultPhoneNumber, isSharingEnabled, sharingFeatures } from '../../../../invite';
import DialInSection from '../../../../invite/components/add-people-dialog/web/DialInSection';
import LanguagesDropdown from '../../../../languages-component/components/LanguagesDropdown';
import { StartSound } from '../../../../lobby/actions.web';
import { joinConference } from '../../../../prejoin';
import { Footer } from '../../../../welcome/components/Footer';
import { Header } from '../../../../welcome/components/Header';
import { apiKEY, mtDomain } from '../../../config/constants';
import { translate } from '../../../i18n';
import { AudioOffMute, AudioOn, Icon, IconExpandLess, IconExpandMore, IconNotAllowed } from '../../../icons';
import { fetchApi } from '../../../mtApi';
import { connect } from '../../../redux';
import { getBackendSafeRoomName } from '../../../util';
import GetMediaQuery from '../../../util/GetMediaQuery';
import { allowUrlSharing } from '../../functions';


import { ClaimMeetingDialog } from './ClaimMeetingDialog';
import ConnectionStatus from './ConnectionStatus';
import CopyMeetingUrl from './CopyMeetingUrl';
import Preview from './Preview';
import { styleWeb } from './style';

declare var interfaceConfig: Object;
declare var config: Object;

const stylePremeet = {
    width552: { width: '170px' },
    margintop558: { marginTop: '12px' },
    floatRight: { float: 'right' }
};


type Props = {


    /**
     * The Redux dispatch function.
     */
     dispatch: Dispatch<any>,

    /**
     * AccessToken of the application.
     */
     _accessToken: ?string;

    /**
     * Children component(s) to be rendered on the screen.
     */
    children: React$Node,

    /**
     * Footer to be rendered for the page (if any).
     */
    footer?: React$Node,

    /**
     * The name of the participant.
     */
    name?: string,

    /**
     * Indicates whether the avatar should be shown when video is off.
     */
    showAvatar: boolean,

    /**
     * Conference.
     */
    conference?: Object,

    /**
     * Guest user not allowed to view this conference.
     */
     guestAllowed: boolean,

    /**
     * Jwt.
     */
    _jwt?: Object,


    /**
     * IsLobby screen enabled.
     */
    isLobby?: Boolean,

    /**
     * Indicates whether the label and copy url action should be shown.
     */
    showConferenceInfo: boolean,

    /**
     * Loading message.
     */
    isLoadingMessage: string,

    /**
     * Local Participant details.
     */
    _localParticipant: ?Object,

    /**
     * Did the person join before the host.
     */
    isPrejoin: boolean,

    /**
     * Title of the screen.
     */
    title: string,

    /**
     * The 'Skip prejoin' button to be rendered (if any).
     */
     skipPrejoinButton?: React$Node,

    /**
     * DIal in section 'render' of the page.
     */
    _dialInVisible: ?boolean, //

    /**
     * Phone render or not.
     */
    _phoneNumber: ?string,

    /**
     * Room name .
     */
     roomName: ?string,

    /**
     * User Details.
     */
    _userDetails: ?string,

     /*
     *
     */
     virtualBackgroundDialog?: React$Node,

    /**
     * True if the preview overlay should be muted, false otherwise.
     */
    videoMuted?: boolean,

    /**
     * New Text.
     */
     newText: ?string,

     /**
      * Pre registration data.
      */
      pregData: ?Object,

    /**
     * The video track to render as preview (if omitted, the default local track will be rendered).
     */
    videoTrack?: Object,

    /**
     * JwtStored when the user is not a moderator.
     */
     _jwtStored: ?Object,


    /**
     * Lobby screen.
     */
    _lobbyScreen: ?Object,


    _playSound: Function,

    _stopSound: Function,

    _registerSound: Function,
    _soundBool: boolean,
    _joinConference: Function
}

type State = {
    onAccordian: boolean,
    audioState: boolean
}

/**
 * Implements a pre-meeting screen that can be used at various pre-meeting phases, for example
 * on the prejoin screen (pre-connection) or lobby (post-connection).
 */
class PreMeetingScreen extends PureComponent<Props, State> {
    /**
     * Default values for {@code Prejoin} component's properties.
     *
     * @static
     */
    static defaultProps = {
        showAvatar: true,
        showConferenceInfo: true,
        width: window.innerWidth
    };

    /**
     * Initializes a new {@code PreMeetingScreen} instance.
     *
     * @inheritdoc
     */
    constructor(props) {
        super(props);

        this.state = {
            onAccordian: false,
            audioState: props?._lobbyScreen?._isLobbyScreenVisible ?? false,
            participantCount: 0
        };

        this._onAccordian = this._onAccordian.bind(this);
        this._onChangeIconState = this._onChangeIconState.bind(this);
        this._renderClaimMeeting = this._renderClaimMeeting.bind(this);
        this.someInterval = this.someInterval.bind(this);
        this.intervalRef = React.createRef(null);
    }


    /**
     * Shows additional content in WelcomePage.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {

        // if (_isPrejoin && _isLobbyScreenVisible) {
        //     console.log('_isPrejoin', _isPrejoin, _isLobbyScreenVisible);

        // }


        // const { conference } = this.props;

        // if (!conference && document.body) {
        //     document.body.style.overflowY = 'scroll';
        // }


        // this.props._registerSound(USER_WAITING_REGISTER, USER_WAITING_SOUND);


    }

    /**
     * 描述.
     *
     * @returns {any}
     */
    async someInterval() {
        const {
            roomName: _roomName
        } = this.props;

        const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
        const roomName = {
            client_id: config.MH_CLIENT_ID,
            credentials: mesh256,
            domain: mtDomain,
            // eslint-disable-next-line camelcase
            meeting_id: _roomName
        };

        const userAccessToken = jitsiLocalStorage.getItem('accessToken') ?? this.props._accessToken;

        const headers = _.isNil(userAccessToken) ? {
            'Content-Type': 'application/json'
        } : {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${userAccessToken}`
        };

        const participantCountObject = await fetchApi('meeting/totalparticipants', 'post', headers, JSON.stringify(roomName));

        const participantCount = participantCountObject?.data?.total_participant ? Number(participantCountObject?.data?.total_participant) : 0;
        const { allow_to_join = false } = participantCountObject?.data ?? {};

        // console.log('participantCount', participantCount, participantCountObject);

        // eslint-disable-next-line no-invalid-this
        this.setState(prevState => {
            return {
                ...prevState,
                participantCount,
                allow_to_join
            };
        });

    }

    /**
     * Removes the classname used for custom styling of the welcome page.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidUpdate(prevProps, prevState) {
        const { _lobbyScreen } = this.props;
        const _isLobbyScreenVisible = _lobbyScreen?._isLobbyScreenVisible ? _lobbyScreen?._isLobbyScreenVisible : null;
        const _isPrejoin = _lobbyScreen?._isPrejoin ? _lobbyScreen?._isPrejoin : null;

        // console.log('componentDidUpdate', _isPrejoin, _isLobbyScreenVisible, this.state.participantCount);
        if (_isPrejoin && _isLobbyScreenVisible && prevState.participantCount !== this.state.participantCount
        ) {
            // console.log('sdsd', _isPrejoin, _isLobbyScreenVisible, this.state.participantCount);
            // console.log('sdsddsdd', this.intervalRef.current);
            if (this.state.participantCount >= 1) {
                // clearInterval(this.interval);
                typeof this.intervalRef.current.stop === 'function' && this.intervalRef.current.stop();
                this.props._joinConference();

            }

        }
    }


    /**
     * Shows additional content in WelcomePage.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentWillUnmount() {
        const removeAdditionalContent = document.getElementById('welcome-page-additional-card-template');

        if (removeAdditionalContent) {
            removeAdditionalContent.innerHTML = '';
        }

        if (document.body) {
            document.body.classList.remove('welcome-page');
        }

        clearInterval(this.interval);
    }

    onMobileResponsive = () => {
        if (interfaceConfig?.applyMeetingSettings === true) {

            document.body.style.setProperty('background-color', interfaceConfig.DEFAULT_BACKGROUND, 'important');
        }
    };


    _onChangeIconState: () => void;

    /**
     * Cotrols the 'Dialog'.
     *
     * @private
     * @returns {void}
     */
    _onChangeIconState() {
        if (this.props._soundBool) {
            this.props._playSound(!this.props._soundBool);
        } else {
            this.props._playSound(!this.props._soundBool);
        }
    }


    _onAccordian: () => void;

    /**
     * Cotrols the 'Dialog'.
     *
     * @private
     * @returns {void}
     */
    _onAccordian() {
        this.setState(prevState => {
            return {
                ...prevState,
                onAccordian: !prevState.onAccordian
            };
        });
    }


    _renderClaimMeeting: () => React$Node;

    /**
     * Cotrols the 'Dialog'.
     *
     * @private
     * @returns {void}
     */
    _renderClaimMeeting() {
        return (
            <ClaimMeetingDialog />
        );
    }


    /**
     * Implements {@code PureComponent#render}.
     *
     * @inheritdoc
     */
    render() {
        const { name, showAvatar, showConferenceInfo, videoMuted,
            videoTrack, _phoneNumber, _dialInVisible, _jwt, _jwtStored,
            isLoadingMessage, _accessToken, pregData, _lobbyScreen, t } = this.props;
        const showSharingButton = allowUrlSharing();
        const accessTkn = _accessToken ?? jitsiLocalStorage.getItem('accessToken');
        const _isLobbyScreenVisible = _lobbyScreen?._isLobbyScreenVisible ? _lobbyScreen?._isLobbyScreenVisible : null;
        const _isPrejoin = _lobbyScreen?._isPrejoin ? _lobbyScreen?._isPrejoin : null;
        let meetingTime;
        const meetingDate = pregData?.meeting_start_time;

        if (meetingDate) {


            meetingTime = moment(pregData?.meeting_start_time).locale('en-gb')
            .format('DD MMM YYYY - h:mm A');
        }

        const h4Meeting = {
            ...styleWeb.meetingTime,
            textAlign: 'start'
        };

        const preMeetingStyle = interfaceConfig?.applyMeetingSettings === true ? { background: `${interfaceConfig.DEFAULT_BACKGROUND}` } : styleWeb.premeetingScreen;

        return (

            <div
                className = { `premeeting-screen ${interfaceConfig?.applyMeetingSettings === true ? 'addColumn' : ''}` }
                id = 'lobby-screen'
                style = { preMeetingStyle }>
                {/* <Watermarks /> */}
                { _isPrejoin && _isLobbyScreenVisible && <ReactInterval
                    callback = { this.someInterval }
                    enabled = { _isPrejoin && _isLobbyScreenVisible }
                    ref = { this.intervalRef }
                    timeout = { 8000 } />}
                {!videoMuted && <div className = 'preview-overlay' />}
                <GetMediaQuery isMobile = { this.onMobileResponsive } />
                <Header
                    prejoin = { true } />
                <ConnectionStatus />
                <div
                    className = { interfaceConfig?.applyMeetingSettings === true ? `container1 interfaceFlex ${interfaceConfig.disablePrejoinHeader === false ? ' marginInc8' : ''}` : 'container1 marginInc' }
                    style = { styleWeb.containers }>
                    <div className = 'rows' >
                        {
                            interfaceConfig?.applyMeetingSettings === true && interfaceConfig?.disablePrejoinHeader === false
                            && <div style = { styleWeb.languageContainer }>
                                <LanguagesDropdown textColor = '#ffffff' />
                            </div>
                        }
                        {
                            pregData && pregData.success && !pregData.claim && (pregData.allow_guest === 1 || _jwt || _jwtStored) && !_isLobbyScreenVisible && (
                                <>
                                    <div className = 'col-m-12 '>

                                        { interfaceConfig?.applyMeetingSettings !== true && <>
                                            <div className = 'text-center'>


                                                <h4
                                                    style = { styleWeb.alignh4 }>

                                                    {t('welcomepage.meetingIsReady')}
                                                </h4>

                                            </div>

                                            <div
                                                className = 'meeting-link-desktop text-center' >
                                                {showConferenceInfo && <CopyMeetingUrl />}
                                                {
                                                    pregData.meeting_start_time && pregData.timezone && (
                                                        <>
                                                            <hr />
                                                            <h4 style = { h4Meeting }>
                                                                {t('welcomepage.meetingDate')}:  { meetingTime }
                                                            </h4>
                                                            <hr />
                                                            <h4 style = { h4Meeting }>
                                                                {t('welcomepage.timezone')}:   { pregData.timezone }
                                                            </h4>
                                                        </>
                                                    )
                                                }
                                                {/*
                                        shell.groupvi.systems/VEmpowerTeamFollowUp <a href = '#'><i
                                    style = {{
                                        fontSize:'20px', color:'#727272'
                                    }}
                                    class = 'bx bx-copy' /></a> */}
                                                {
                                                    _phoneNumber
                                    && _dialInVisible
                                    && <DialInSection phoneNumber = { _phoneNumber } />
                                                }
                                                {/* <span style = { styleWeb.spanStyle }>Dial-in:</span> ******.647.1431
                                <span style = { styleWeb.spanStyle }>
                                    PIN:</span> 1547 2872 98#
                                <a href = '#'><i
                                    className = 'bx bx-copy'
                                    style = { styleWeb.spanIstyle } />
                                </a>
                                <br />
                                <span
                                    style = { styleWeb.spanStyle1 }>
                                    <a
                                        href = '#'
                                        style = { styleWeb.aStyle } >More Numbers</a>
                                </span> */}
                                            </div>


                                            <div
                                                className = 'meeting-link-mobile text-center'>

                                                <div className = 'wrapper center-block'>
                                                    <div
                                                        aria-multiselectable = 'true'
                                                        className = 'panel-group'
                                                        id = 'accordion'
                                                        role = 'tablist' >
                                                        <div className = 'panel panel-default'>
                                                            <div
                                                                className = { `panel-heading
                                                ${this.state.onAccordian ? 'active' : ''}` }
                                                                id = 'headingOne'
                                                                role = 'tab'>
                                                                <h4
                                                                    className = 'panel-title'
                                                                    onClick = { this._onAccordian }>
                                                                    <a
                                                                        aria-controls = 'collapseOne'
                                                                        aria-expanded = 'true'
                                                                        className = 'panel-title1'
                                                                        data-parent = '#accordion'
                                                                        data-toggle = 'collapse'
                                                                        role = 'button'
                                                                        style = { styleWeb.panelTitle1 }>
                                                                        <div
                                                                            style = { styleWeb.panelTitlediv }>
                                                                            {t('welcomepage.meetingDetails')}
                                                                        </div>
                                                                        <Icon
                                                                            color = '#000000'
                                                                            src = { this.state.onAccordian ? IconExpandLess
                                                                                : IconExpandMore } />
                                                                    </a>
                                                                </h4>
                                                            </div>
                                                            <div
                                                                aria-labelledby = 'headingOne'
                                                                className = { `panel-collapse collapse in
                                                ${this.state.onAccordian ? 'show' : ''}` }
                                                                id = 'collapseOne'

                                                                role = 'tabpanel' >
                                                                <div
                                                                    className = 'panel-body'
                                                                    style = { styleWeb.panelBody }>


                                                                    {showSharingButton ? <CopyMeetingUrl /> : null}
                                                                    {
                                                                        pregData.meeting_start_time && pregData.timezone && (
                                                                            <>
                                                                                <h4 style = { h4Meeting }>
                                                                                    {t('welcomepage.meetingDate')}:  {}
                                                                                </h4>
                                                                                <h4 style = { h4Meeting }>
                                                                                    {t('welcomepage.timezone')}: { pregData.timezone}
                                                                                </h4>
                                                                            </>
                                                                        )
                                                                    }
                                                                    {/* <a href = '#'>
                                                        <i
                                                            className = 'bx bx-copy'
                                                            style = { styleWeb.conferenceLink } /></a> */}
                                                                    <hr />
                                                                    {
                                                                        _phoneNumber
                                && _dialInVisible
                                    && <DialInSection phoneNumber = { _phoneNumber } />
                                                                    }

                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>


                                            </div>
                                        </>
                                        }


                                    </div>
                                    <div className = 'col-m-6'>
                                        <div className = 'preview-log-conference'>
                                            <Preview
                                                name = { name }
                                                showAvatar = { showAvatar }
                                                videoMuted = { videoMuted }
                                                videoTrack = { videoTrack } />
                                            { this.props.virtualBackgroundDialog }
                                        </div>
                                    </div>
                                    <div className = { interfaceConfig?.applyMeetingSettings === true ? 'col-m-6 bg-white-radius-20' : 'col-m-6' }>
                                        <div className = 'content'>

                                            { this.props.children }


                                        </div>
                                    </div>
                                </>
                            )
                        }

                        {
                            // eslint-disable-next-line camelcase
                            pregData && (pregData?.success === false || pregData?.allow_guest === 0) && !pregData?.claim && !_jwt && !_jwtStored && !_isLobbyScreenVisible ? (
                                <>
                                    <div className = 'col-md-12 col-sm-12'>
                                        <br /><br />
                                        <div
                                            className = 'invalidMeeting text-center'
                                            style = { styleWeb.alignCenter }>
                                            { _.isEmpty(isLoadingMessage)
                                                ? pregData.success && pregData.allow_guest === 0 ? (
                                                    <img
                                                        className = 'img-fluid'
                                                        src = 'images/privacy.png'
                                                        style = { styleWeb.loadingImg } />
                                                )
                                                // eslint-disable-next-line camelcase
                                                    : (
                                                        <Icon
                                                            className = 'img-fluid'
                                                            src = { IconNotAllowed } />
                                                    )

                                                : <img
                                                    className = 'img-fluid'
                                                    src = 'images/loading2.gif'
                                                    style = { styleWeb.loadingImg } />}

                                            <h4
                                                style = { styleWeb.h4Style }>
                                                {_.isEmpty(isLoadingMessage) ? pregData && (pregData.allow_guest === 0 || pregData.success) ? t('welcomepage.privateMeetingErr') : pregData.code === 403 ? t('welcomepage.oopsDeviceClockorTimezoneErr') : pregData.message : isLoadingMessage}


                                            </h4>


                                        </div>
                                        <br /><br />
                                    </div>
                                </>
                            ) : null
                        }
                        { // Claim meeting dialog
                            pregData && pregData.success && pregData.claim === true && !_jwt && !_jwtStored && !_isLobbyScreenVisible && (
                                <>
                                    <div className = 'col-d-12 col-sm-12'>
                                        <br /><br />
                                        <div
                                            className = 'invalidMeeting'
                                            style = { styleWeb.alignCenter }>
                                            { accessTkn ? this._renderClaimMeeting() : <div className = 'text-center'>
                                                <img
                                                    className = 'img-fluid'
                                                    src = 'images/Upgrade-2.0.png'
                                                    style = { stylePremeet.width552 } />
                                                <h4
                                                    style = { styleWeb.h4Style }>
                                                    {t('prejoin.systemUpgradedInformation')}
                                                </h4>

                                                <ul
                                                    className = 'list-unstyled'
                                                    style = { stylePremeet.margintop558 } >
                                                    <li className = 'signin-li' ><a
                                                        href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${window.location.href}&device_type=web&response_type=get` }
                                                        style = { styleWeb.signInButton }>{t('prejoin.signinsignup')}</a></li>
                                                </ul>
                                            </div>
                                            }


                                        </div>
                                        <br /><br />
                                    </div>
                                </>
                            )
                        }
                        {
                            pregData && pregData.success && _isLobbyScreenVisible && (
                                <>
                                    <div className = 'col-d-12 col-sm-12'>
                                        <br /><br />
                                        <div
                                            className = 'invalidMeeting text-center'
                                            style = { styleWeb.alignCenter }>
                                            <div style = { stylePremeet.floatRight }>
                                                <Icon
                                                    onClick = { this._onChangeIconState }
                                                    src = { this.props._soundBool ? AudioOn : AudioOffMute } />
                                            </div>
                                            <img
                                                className = 'img-fluid'
                                                src = 'images/loading2.gif'
                                                style = { styleWeb.loadingImg } />
                                            <h4
                                                style = { styleWeb.h4Style }>

                                                {_isLobbyScreenVisible && !_isPrejoin
                                    && t('welcomepage.waitingInLobby', { moderator: interfaceConfig?.CHANGE_MODERATOR_NAME ? interfaceConfig?.CHANGE_MODERATOR_NAME : 'Moderator' })}
                                                {_isPrejoin && _isLobbyScreenVisible
                                            && t('welcomepage.pleaseWaitForTheStartMeeting', { moderator: interfaceConfig?.CHANGE_MODERATOR_NAME ? interfaceConfig?.CHANGE_MODERATOR_NAME : 'Moderator' })}
                                            </h4>


                                        </div>
                                        <br /><br />
                                    </div>
                                </>
                            )
                        }
                        { !pregData && (!pregData?.success || pregData?.allow_guest === 0) && !_jwt && !_jwtStored && (
                            <>
                                <div className = 'col-md-12 col-sm-12'>
                                    <br /><br />
                                    <div
                                        className = 'invalidMeeting text-center'
                                        style = { styleWeb.alignCenter }>
                                        <h4
                                            style = { styleWeb.h4Style }>
                                            {t('welcomepage.oopsThereSeemsToBeProblem')}

                                        </h4>


                                    </div>
                                    <br /><br />
                                </div>
                            </>
                        )}
                        { pregData && (pregData?.success === false || pregData?.allow_guest === 0) && pregData.message === 'Invalid Credentials' && _jwt && _jwtStored && (
                            <>
                                <div className = 'col-md-12 col-sm-12'>
                                    <br /><br />
                                    <div
                                        className = 'invalidMeeting text-center'
                                        style = { styleWeb.alignCenter }>
                                        <Icon
                                            className = 'img-fluid'
                                            src = { IconNotAllowed }
                                            style = { styleWeb.notAllowed } />
                                        <h4
                                            style = { styleWeb.h4Style }>

                                            {t('welcomepage.oopsDeviceClockorTimezoneErr')}
                                        </h4>


                                    </div>
                                    <br /><br />
                                </div>
                            </>
                        )}


                    </div>
                </div>

                { this.props.footer }
                <Footer prejoin = { true } />
            </div>
        );
    }
}


/**
 * Maps state to the pros for premeeting screen.
 *
 * @param {*} state - State of the whole application.
 *
 * @returns {Object}
 */
export function mapStateToProps(state: Object) {
    const { mediaQueries } = state['features/base/responsive-ui'];
    const participants = state['features/base/participants'];
    const participant = participants.filter(i => i.id === 'local');
    const dialIn = state['features/invite'];
    const { userDetails } = state['features/base/mtApi'];
    const { lobbyScreen, soundBool } = state['features/lobby'];
    const { jwt, accessToken, jwtData } = state['features/base/jwt'];
    const phoneNumber = dialIn && dialIn.numbers ? _getDefaultPhoneNumber(dialIn.numbers) : undefined;
    const { pregData } = state['features/base/mtApi'];
    const roomName = getBackendSafeRoomName(state['features/base/conference'].room);


    // const stateJwt = state['features/base/jwt'];

    return {
        _smallDevices: mediaQueries,
        _localParticipant: participant,
        _phoneNumber: phoneNumber,
        _dialInVisible: isSharingEnabled(sharingFeatures.dialIn),
        _jwt: jwt,
        _accessToken: accessToken,
        _userDetails: userDetails,
        pregData,
        roomName,
        _jwtStored: jwtData,
        _lobbyScreen: lobbyScreen,
        _soundBool: soundBool

    };
}

/**
 * Maps state to the pros for premeeting screen.
 *
 * @param {*} dispatch - State of the whole application.
 *
 * @returns {Object}
 */
function mapDispatchToProps(dispatch: Function) {
    return {
        // eslint-disable-next-line new-cap
        _playSound: param => dispatch(StartSound(param)),
        _joinConference: () => dispatch(joinConference())
    };
}

export default translate(connect(mapStateToProps, mapDispatchToProps)(PreMeetingScreen));
