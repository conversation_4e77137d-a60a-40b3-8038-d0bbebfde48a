/* @flow */

import React, { Component } from 'react';

import { stateType } from '../../../../app/stateTypes';
import { isVpaasMeeting } from '../../../../billing-counter/functions';
import { translate } from '../../../i18n';
import { connect } from '../../../redux';

declare var interfaceConfig: Object;

/**
 * The CSS style of the element with CSS class {@code rightwatermark}.
 *
 * @private
 */
const _RIGHT_WATERMARK_STYLE = {
    backgroundImage: 'url(images/watermark.png)'
};

/**
 * The type of the React {@code Component} props of {@link Watermarks}.
 */
type Props = {

    /**
     * The link used to navigate to on logo click.
     */
    _logoLink: string,

    /**
     * The url for the logo.
     */
    _logoUrl: string,

    /**
     * Logo Background.
     */
    _logoBackground: String,

    /**
     * If the Meet Hour watermark should be displayed or not.
     */
    _showMHWatermark: boolean,

    /**
     * The default value for the Meet Hour logo URL.
     */
    defaultMHLogoURL: ?string,

    /**
     * Show Watermark in prejoin page.
     */
    _prejoinPage: Boolean,

    /**
     * Invoked to obtain translated strings.
     */
    t: Function,

    /**
     * State of the conference.
     */
    state: Object,
    isDeskTop: Boolean,
    iAmRecorder: Boolean,
    SHOW_MEET_HOUR_WATERMARK: Boolean,
    displayLogoInRecorder: Boolean
};

/**
 * The type of the React {@code Component} state of {@link Watermarks}.
 */
type State = {

    /**
     * The url to open when clicking the brand watermark.
     */
    brandWatermarkLink: string,

    /**
     * Whether or not the brand watermark should be displayed.
     */
    showBrandWatermark: boolean,

    /**
     * Whether or not the show the "powered by Meet Hour.org" link.
     */
    showPoweredBy: boolean,
};

/**
 * A Web Component which renders watermarks such as Jits, brand, powered by,
 * etc.
 */
class Watermarks extends Component<Props, State> {
    /**
     * Initializes a new Watermarks instance.
     *
     * @param {Object} props - The read-only properties with which the new
     * instance is to be initialized.
     */
    constructor(props: Props) {
        super(props);

        const showBrandWatermark = interfaceConfig.SHOW_BRAND_WATERMARK;

        this.state = {
            brandWatermarkLink: showBrandWatermark
                ? interfaceConfig.BRAND_WATERMARK_LINK
                : '',
            showBrandWatermark,
            showPoweredBy: interfaceConfig.SHOW_POWERED_BY
        };
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        return (
            <div>
                {this._renderMHWatermark()}
                {this._renderBrandWatermark()}
                {this._renderPoweredBy()}
            </div>
        );
    }

    /**
     * Renders a brand watermark if it is enabled.
     *
     * @private
     * @returns {ReactElement|null} Watermark element or null.
     */
    _renderBrandWatermark() {
        let reactElement = null;

        if (this.state.showBrandWatermark) {
            reactElement = (
                <div
                    className = 'watermark rightwatermark'
                    style = { _RIGHT_WATERMARK_STYLE } />
            );

            const { brandWatermarkLink } = this.state;

            if (brandWatermarkLink) {
                reactElement = (
                    <a
                        href = { brandWatermarkLink }
                        target = '_new'>
                        {reactElement}
                    </a>
                );
            }
        }

        return reactElement;
    }

    /**
     * Renders a Meet Hour Logo watermark based on usecases.
     *
     * @private
     * @returns {ReactElement|null} Watermark element or null.
     */
    render_logoFinal() {

        const {
            _logoLink,
            _logoUrl,
            _logoBackground
        } = this.props;

        let reactElement = null;

        const style = {
        // backgroundImage: `url(${_logoUrl})`,
            maxWidth: 500,
            maxHeight: 300,
            backgroundColor: _logoBackground,
            paddingLeft: '10px',
            borderRadius: '0 5px 5px 0',
            zIndex: 0
        };
        const imgStyle = {
            width: '80%',
            padding: '7px 7px',
            height: '-webkit-fill-available'
        };

        reactElement = (
            <div
                className = 'watermark leftwatermark'
                style = { style }>
                <img
                    alt = 'logo'
                    src = { _logoUrl }
                    style = { imgStyle } />
            </div>
        );

        if (_logoLink) {
            reactElement = (
                <a
                    href = { _logoLink }
                    target = '_new'>
                    {reactElement}
                </a>
            );
        }

        return reactElement;
    }

    /**
     * Renders a Meet Hour watermark if it is enabled.
     *
     * @private
     * @returns {ReactElement|null}
     */
    _renderMHWatermark() {
        const {
            SHOW_MEET_HOUR_WATERMARK,
            isDeskTop,
            iAmRecorder,
            displayLogoInRecorder
        } = this.props;
        let reactElement = null;

        // This is Recorder case
        if (iAmRecorder) {
            if (displayLogoInRecorder) {
                reactElement = this.render_logoFinal();
            }

        } else if (!isDeskTop) {
            reactElement = null; // This is Desktop app case, no logo in Desktop app
        } else if (SHOW_MEET_HOUR_WATERMARK === true) { // This is participant case

            reactElement = this.render_logoFinal();
        }

        return reactElement;
    }

    /**
     * Renders a powered by block if it is enabled.
     *
     * @private
     * @returns {ReactElement|null}
     */
    _renderPoweredBy() {
        if (this.state.showPoweredBy) {
            const { t } = this.props;

            return (
                <a
                    className = 'poweredby'
                    href = 'https://shell.groupvi.systems?web-embed=true'
                    target = '_new'>
                    <span>{t('poweredby')}</span>
                </a>
            );
        }

        return null;
    }
}

/**
 * Maps parts of Redux store to component prop types.
 *
 * @param {Object} state - Snapshot of Redux store.
 * @param {Object} ownProps - Component's own props.
 * @returns {Props}
 */
function _mapStateToProps(state, ownProps) {
    const {
        customizationReady,
        customizationFailed,
        defaultBranding,
        useDynamicBrandingData,
        logoClickUrl,
        logoImageUrl
    } = state['features/dynamic-branding'];
    const isValidRoom = state['features/base/conference'].room;

    const desktopApp = state[stateType.mtApi].desktopapp;
    const {
        DEFAULT_LOGO_URL,
        MEET_HOUR_WATERMARK_LINK,
        SHOW_MEET_HOUR_WATERMARK,
        BRAND_WATERMARK_BACKGROUND,
        displayLogoInRecorder = true
    } = interfaceConfig;
    let _showMHWatermark
        = (customizationReady
            && !customizationFailed
            && SHOW_MEET_HOUR_WATERMARK)
        || !isValidRoom;
    let _logoUrl = logoImageUrl;
    let _logoLink = logoClickUrl;
    const _logoBackground = BRAND_WATERMARK_BACKGROUND;
    const iAmRecorder = state['features/base/config'].iAmRecorder;

    if (useDynamicBrandingData) {
        if (isVpaasMeeting(state)) {
            // don't show logo if request fails or no logo set for vpaas meetings
            _showMHWatermark = !customizationFailed && Boolean(logoImageUrl);
        } else if (defaultBranding) {
            _logoUrl = DEFAULT_LOGO_URL;
            _logoLink = MEET_HOUR_WATERMARK_LINK;
        }
    } else {
        // When there is no custom branding data use defaults
        _logoUrl = ownProps.defaultMHLogoURL || DEFAULT_LOGO_URL;
        _logoLink = MEET_HOUR_WATERMARK_LINK;
    }

    const isDeskTop = (interfaceConfig.ENABLE_DESKTOP_DEEPLINK && !desktopApp) ?? true;

    return {
        _logoLink,
        _logoUrl,
        _logoBackground,
        _showMHWatermark,
        SHOW_MEET_HOUR_WATERMARK,
        _prejoinPage: state['features/prejoin']?.showPrejoin,
        isDeskTop,
        iAmRecorder,
        displayLogoInRecorder
    };
}

export default connect(_mapStateToProps)(translate(Watermarks));
