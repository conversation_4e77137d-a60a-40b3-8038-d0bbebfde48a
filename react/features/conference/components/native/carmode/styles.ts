
/**
 * The size of the microphone icon.
 */
const MICROPHONE_SIZE = 180;

/**
 * The styles of the safe area view that contains the title bar.
 */
const titleBarSafeView = {
    left: 0,
    position: 'absolute',
    right: 0,
};

/**
 * The styles of the native components of Carmode.
 */
export default {

    bottomContainer: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        bottom: 56
    },

    /**
     * {@code Conference} Style.
     */
    conference: {
        backgroundColor: "#040404",
        flex: 1,
        justifyContent: 'center',
        flexDirection: 'column',
        display: 'flex',
    },

    microphoneStyles: {
        container: {
            borderRadius: MICROPHONE_SIZE / 2,
            height: MICROPHONE_SIZE,
            maxHeight: MICROPHONE_SIZE,
            justifyContent: 'center',
            overflow: 'hidden',
            width: MICROPHONE_SIZE,
            maxWidth: MICROPHONE_SIZE,
            flex: 1,
            zIndex: 1,
            elevation: 1
        },

        icon: {
            color: "#FFF",
            fontSize: MICROPHONE_SIZE * 0.45,
            // fontWeight: '100'
        },

        iconContainer: {
            alignItems: 'center',
            alignSelf: 'stretch',
            flex: 1,
            justifyContent: 'center',
            backgroundColor: "#3D3D3D"
        },

        unmuted: {
            borderWidth: 4,
            borderColor: "#1EC26A"
        }
    },

    qualityLabelContainer: {
        borderRadius: 6,
        flexShrink: 1,
        paddingHorizontal: 2,
        justifyContent: 'center',
        marginTop: 8
    },

    roomTimer: {
        "fontSize": 14,
        "lineHeight": 18,
        "fontWeight": "600",
        "letterSpacing": 0,
        color: "#FFF",
        textAlign: 'center'
    },

    titleView: {
        width: 152,
        height: 28,
        backgroundColor: "#292929",
        borderRadius: 12,
        alignSelf: 'center'
    },

    title: {
        margin: 'auto',
        textAlign: 'center',
        paddingVertical: 4,
        paddingHorizontal: 16,
        color: "#C2C2C2"
    },

    soundDeviceButton: {
        marginBottom: 16,
        width: 240,
        backgroundColor: '#036531',
        flexDirection: 'row',
        borderRadius: 6,
        alignItems: 'center',
        gap: 6,
        justifyContent: 'center',
        paddingVertical: 16
    },
    soundDeviceButtonText: {
        fontWeight: 600,
        color: "white",
    },

    leaveMeatingButton: {
        marginBottom: 16,
        width: 240,
        backgroundColor: '#880000',
        flexDirection: 'row',
        borderRadius: 6,
        alignItems: 'center',
        gap: 6,
        justifyContent: 'center',
        paddingVertical: 16
    },
    leaveMeetingButtonText: {
        fontWeight: 600,
        color: "white",
    },

    endMeetingButton: {
        width: 240,
        backgroundColor: "#CB2233"
    },

    headerLabels: {
        borderBottomLeftRadius: 3,
        borderTopLeftRadius: 3,
        flexShrink: 1,
        paddingHorizontal: 2,
        justifyContent: 'center'
    },

    titleBarSafeViewColor: {
        backgroundColor: "#040404",
        color: "#FFFF",
        alignItems: 'center',
        justifyContent: 'center',
        paddingTop: 30,
        paddingBottom: 20
    },

    microphoneContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: "#040404"
    },

    titleBarWrapper: {
        alignItems: 'center',
        // flex: 1,
        marginBottom: 16,
        flexDirection: 'row',
        justifyContent: 'center'
    },

    roomNameWrapper: {
        flexDirection: 'row',
        marginRight: 8,
        flexShrink: 1,
        flexGrow: 1
    },

    roomNameView: {
        backgroundColor: 'rgba(0,0,0,0.6)',
        flexShrink: 1,
        justifyContent: 'center',
        paddingHorizontal: 8
    },

    roomName: {
        color: "#FFF",
        "fontSize": 14,
        "lineHeight": 18,
        "fontWeight": "600",
        "letterSpacing": 0
    },

    titleBar: {
        alignSelf: 'center',
        color: "#FFF",
    },

    videoStoppedLabel: {
        "fontSize": 16,
         "lineHeight": 24,
         "fontWeight": "400",
         "letterSpacing": 0,
        color: "#FFF",
        marginBottom: 16,
        textAlign: 'center',
        width: '100%'
    },

    connectionIndicatorIcon: {
        fontSize: 20
    }
};
