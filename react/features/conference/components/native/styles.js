import { Platform } from 'react-native';

import { ColorSchemeRegistry, schemeColor } from '../../../base/color-scheme';
import { BoxModel, ColorPalette, fixAndroidViewClipping } from '../../../base/styles';
import { FILMSTRIP_SIZE } from '../../../filmstrip';

export const NAVBAR_GRADIENT_COLORS = [ '#000000FF', '#00000000' ];
export const INSECURE_ROOM_NAME_LABEL_COLOR = ColorPalette.warning;

// From brand guideline
const BOTTOM_GRADIENT_HEIGHT = 290;
const DEFAULT_GRADIENT_SIZE = 100;

/**
 * The styles of the feature conference.
 */
export default {

    bottomGradient: {
        bottom: 0,
        flexDirection: 'column',
        justifyContent: 'flex-end',
        minHeight: DEFAULT_GRADIENT_SIZE,
        left: 0,
        position: 'absolute',
        right: 0
    },

    /**
     * {@code Conference} Style.
     */
    conference: fixAndroidViewClipping({
        alignSelf: 'stretch',
        backgroundColor: ColorPalette.appBackground,
        flex: 1
    }),

    displayNameContainer: {
        margin: 10
    },

    gradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        flex: 1
    },

    gradientStretchBottom: {
        height: BOTTOM_GRADIENT_HEIGHT
    },

    gradientStretchTop: {
        height: Platform.OS === 'android' ? DEFAULT_GRADIENT_SIZE : 150,
        backgroundColor: '#1C2B41',
        opacity: 0.64
    },

    /**
     * View that contains the indicators.
     */
    indicatorContainer: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        margin: BoxModel.margin
    },

    /**
     * Indicator container for wide aspect ratio.
     */
    indicatorContainerWide: {
        marginRight: FILMSTRIP_SIZE + BoxModel.margin
    },

    labelWrapper: {
        flexDirection: 'column',
        position: 'absolute',
        right: 0,
        top: 0
    },

    lonelyButton: {
        alignItems: 'center',
        borderRadius: 24,
        flexDirection: 'row',
        height: 48,
        justifyContent: 'space-around',
        paddingHorizontal: 12
    },

    lonelyButtonComponents: {
        marginHorizontal: 6
    },

    lonelyMeetingContainer: {
        alignSelf: 'stretch',
        alignItems: 'center',
        padding: BoxModel.padding * 2
    },

    lonelyMessage: {
        paddingVertical: 12
    },

    navBarButton: {
        iconStyle: {
            color: ColorPalette.white,
            fontSize: 24
        },

        underlayColor: 'transparent'
    },

    navBarContainer: {
        flexDirection: 'column',
        left: 0,
        position: 'absolute',
        right: 0,
        top: 0
    },

    navBarSafeView: {
        left: 0,
        position: 'absolute',
        right: 0,
        top: 0
    },

    navBarWrapper: {
        flex: 1,
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        height: 60,
        justifyContent: 'space-between',
        paddingHorizontal: 14
    },
    navBarImage: { height: 35,
        width: 150,
        borderRadius: 8,
        backgroundColor: 'white' },

    viewTimer: { flexDirection: 'row',
        alignItems: 'center' },

    roomTimer: {
        marginLeft: 3,
        color: ColorPalette.white,
        fontSize: 15,
        opacity: 0.8
    },
    navBarTitle: {
        height: 45,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row' },

    roomName: {
        color: ColorPalette.white,
        fontSize: 17,
        fontWeight: '400'
    },

    roomNameWrapper: {
        marginLeft: 6

        // flexDirection: 'column',
        // alignItems: 'center',
        // left: 0,
        // paddingHorizontal: 48,
        // position: 'absolute',
        // right: 0
    },

    roomNumberCountWrapper: {
        // flexDirection: 'column',
        // alignItems: 'center'
        // left: 0,
        // marginTop: 40,
        // position: 'absolute',
        // right: 0
    },

    /**
     * The style of the {@link View} which expands over the whole
     * {@link Conference} area and splits it between the {@link Filmstrip} and
     * the {@link Toolbox}.
     */
    toolboxAndFilmstripContainer: {
        bottom: 0,
        flexDirection: 'column',
        justifyContent: 'flex-end',
        left: 0,
        position: 'absolute',
        right: 0,

        // Both on Android and iOS there is the status bar which may be visible.
        // On iPhone X there is the notch. In the two cases BoxModel.margin is
        // not enough.
        top: BoxModel.margin * 3
    },

    insecureRoomNameLabel: {
        backgroundColor: INSECURE_ROOM_NAME_LABEL_COLOR
    },

    participantsContainer: {
        // backgroundColor: 'white',
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        marginTop: 5,
        paddingTop: 5,
        paddingBottom: 5,
        borderRadius: 5,
        alignSelf: 'center',
        padding: 5
    },

    participantsTextContainer: {
        color: 'white',
        justifyContent: 'center',
        textAlign: 'center',
        paddingLeft: 5
    },

    adContainerCenter: {
        alignItems: 'center',
        backgroundColor: 'transparent'
    }
};

ColorSchemeRegistry.register('Conference', {
    lonelyButton: {
        backgroundColor: schemeColor('inviteButtonBackground')
    },

    lonelyMessage: {
        color: schemeColor('onVideoText')
    }
});
