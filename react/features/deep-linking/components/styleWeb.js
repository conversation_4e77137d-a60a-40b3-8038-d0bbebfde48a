// @flow
import React from 'react';

export type StyleProps = {
    [key: K]: React.CSSProperties
}


export const styleDeeplinking: StyleProps = {
    container: { maxWidth: '1120px!important',
        marginTop: '5%',
        margin: '153px auto 103px auto',
        backgroundColor: 'rgb(51, 54, 102)'
    },
    invalidMeeting: { width: '80%',
        maxWidth: '800px',
        borderRadius: '10px',
        margin: '51px auto auto',
        backgroundColor: 'rgb(246, 246, 246)',
        color: 'rgb(0, 0, 0)',
        padding: '20px',
        textAlign: 'center'
    },
    floatRight: { float: 'right' },
    joinMeeting: { marginTop: '10px',
        color: 'rgb(0, 0, 0)',
        fontFamily: 'Poppins, sans-serif',
        fontSize: '1.5rem',
        fontWeight: 500,
        lineHeight: '1.2' },
    launchMeeting: { width: '80%',
        maxWidth: '800px',
        borderRadius: '10px',
        marginTop: '45px',
        margin: 'auto',
        backgroundColor: '#f6f6f6',
        color: '#000',
        padding: '20px',
        textAlign: 'center' },
    marginTop10: { marginTop: '10px',
        fontSize: '15px' },
    marginTop20: { marginTop: '20px' },
    width100: {
        width: '100%' }

};
