/**
 * The namespace of the i18n/translation keys.
 *
 * @type {string}
 */
export const _TNS = 'deepLinking';


export const PlatformOS = {
    'windows': 'https://repo.shell.groupvi.systems/download/windows/latest/meet-hour.exe',
    'macos': 'https://repo.shell.groupvi.systems/download/mac/latest/meet-hour.dmg',
    'ios': 'https://apps.apple.com/in/app/meet-hour/id1527001689',
    'android': 'https://play.google.com/store/apps/details?id=meet.groupvi.systems',
    'linux': 'https://repo.shell.groupvi.systems/download/linux/latest/meet-hour-amd64.deb'
};

