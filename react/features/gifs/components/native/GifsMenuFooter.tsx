import React from "react";
import { useTranslation } from "react-i18next";
import { Image, Text, TextStyle, View, ViewStyle } from "react-native";

import styles from "./styles";

/**
 * Implements the gifs menu footer component.
 *
 * @returns { JSX.Element} - The gifs menu footer component.
 */
const GifsMenuFooter = (): JSX.Element => {
    const { t } = useTranslation();

    return (
        <View
            style={{
                alignItems: "center",
                backgroundColor: "#333666",
                display: "flex",
                flexDirection: "column",
                height: 60,
                justifyContent: "center",
            }}
        >
            <Text
                style={{
                    color: "#FFF",
                    fontWeight: "bold",
                }}
            >
                {t("poweredby")}
            </Text>
            <Image source={require("../../../../../images/GIPHY_logo.png")} />
        </View>
    );
};

export default GifsMenuFooter;
