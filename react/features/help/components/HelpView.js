// @flow

import React, { PureComponent } from 'react';
import { View } from 'react-native';
import WebView from 'react-native-webview';

import { MHModal } from '../../base/modal';
import { LoadingIndicator } from '../../base/react';
import { connect } from '../../base/redux';
import styles, { INDICATOR_COLOR } from '../../donorbox-modal/donorviewNative/styles';
import { HELP_VIEW_MODAL_ID } from '../constants';


const DEFAULT_HELP_CENTRE_URL = 'https://shell.groupvi.systems/FAQ.html?appview=1';

type Props = {

    /**
     * The URL to display in the Help Centre.
     */
    _url: string
}

/**
 * Implements a page that renders the help content for the app.
 */
class HelpView extends PureComponent<Props> {
    /**
     * Instantiates a new instance.
     *
     * @inheritdoc
     */
    constructor(props: Props) {
        super(props);

        this._renderLoading = this._renderLoading.bind(this);
    }

    /**
     * Implements {@code PureComponent#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        return (
            <MHModal
                headerProps = {{
                    headerLabelKey: 'helpView.header'
                }}
                modalId = { HELP_VIEW_MODAL_ID }>
                <WebView
                    renderLoading = { this._renderLoading }
                    source = {{ uri: this.props._url }}
                    startInLoadingState = { true } />
            </MHModal>
        );
    }

    _renderLoading: () => React$Component<any>;

    /**
     * Renders the loading indicator.
     *
     * @returns {React$Component<any>}
     */
    _renderLoading() {
        return (
            <View style = { styles.indicatorWrapper }>
                <LoadingIndicator
                    color = { INDICATOR_COLOR }
                    size = 'large' />
            </View>
        );
    }
}

/**
 * Maps part of the Redux state to the props of this component.
 *
 * @param {Object} state - The Redux state.
 * @returns {Props}
 */
function _mapStateToProps(state) {
    return {
        _url: state['features/base/config'].helpCentreURL || DEFAULT_HELP_CENTRE_URL
    };
}

export default connect(_mapStateToProps)(HelpView);
