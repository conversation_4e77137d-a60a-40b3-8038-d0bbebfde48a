/* eslint-disable react-native/no-inline-styles */
/* eslint-disable indent */
/* eslint-disable react/jsx-indent-props */
/* eslint-disable react/jsx-no-target-blank */
/* eslint-disable max-len */

/* eslint-disable camelcase */
// @flow

import { jitsiLocalStorage } from '@jitsi/js-utils';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import logger from '../../app/logger';
import { Avatar } from '../../base/avatar';
import { apiKEY } from '../../base/config/constants';
import { JWT_STORAGE } from '../../base/jwt/actionTypes';
import { fetchApi, setuserDetails } from '../../base/mtApi';
import { Watermark } from '../../base/react';
import { getDisplayName, updateSettings } from '../../base/settings';
import { isDomainWeborNative } from '../../base/util/checkOS';
import LanguagesDropdown from '../../languages-component/components/LanguagesDropdown';
import { styleWeb } from '../../welcome/components/stylesWeb';

declare var config: Object;
declare var interfaceConfig: Object;

type Props = {
  prejoin: ?boolean,
};

export const Header = (props: Props) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const locationUrl = window.location.href;
  const [ userd, setUserd ] = useState(undefined);
  const [ loginButton, setLoginButton ] = useState(false);

  const state1 = useSelector(state => state['features/base/jwt']);
  const name = useSelector(state => getDisplayName(state));
  const { userDetails, pregData } = useSelector(
    state => state['features/base/mtApi']
  );
  const userAccessToken = jitsiLocalStorage.getItem('accessToken');

  useEffect(async () => {
    let accesTokn = state1.accessToken ?? userAccessToken;

    if (
      accesTokn === null
      || accesTokn === 'null'
      || accesTokn === undefined
      || accesTokn === ''
    ) {
      if (locationUrl !== '') {
        const url = new URL(locationUrl);
        const location = url.searchParams.get('access_token');

        if (location !== null || location !== '' || location !== undefined) {
          accesTokn = location;
          await jitsiLocalStorage.setItem('accessToken', location);
        }
      }
    }

    const moement = moment().seconds(0)
.milliseconds(0)
.format('X');

    const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

    if (accesTokn) {
      const preRegObject = JSON.stringify({
        client_id: config.MH_CLIENT_ID,
        credentials: mesh256,
        ...isDomainWeborNative
      });

      fetchApi(
        'customer/user_details',
        'post',
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accesTokn}`
        },
        preRegObject
      )
        .then(response => {
          const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);

          const { jwt } = response.data;

          // Also Skipping the pre-registration page redirect for Recorder
          if (
            isTrueReg
            && !state1.jwt
            && _.isEmpty(jwt)
            && !config?.iAmRecorder === true
          ) {
            return window.location.replace(
              `${
                config?.brandedPreRegistrationURL
                  ? config?.brandedPreRegistrationURL
                  : pregData.registration_url
              }`
            );
          }
          setUserd(response.data);
          const namess = response?.data?.name;
          const emailss = response?.data?.email;

          dispatch(
            updateSettings({
              displayName: namess,
              email: emailss
            })
          );

          if (response.success) {
            setLoginButton(true);
            const getJwt = response.data?.jwt;

            if (!_.isEmpty(getJwt) && !state1?.jwt) {
              dispatch({
                type: JWT_STORAGE,
                jwtData: getJwt
              });
            }
          }
          const ele
            = document.getElementById('preloader')
            ?? document.getElementById('preloader1');

          if (ele) {
            setTimeout(() => {
              ele.style.opacity = '0';
              setTimeout(() => {
                ele.style.display = 'none';
              }, 2000);
            }, 1500);
          }

          dispatch(setuserDetails(response.data));
        })
        .catch(e => {
          logger.warn('errorResponse', e);

          const ele
            = document.getElementById('preloader')
            ?? document.getElementById('preloader1');

          if (ele) {
            ele.style.opacity = '0';
            ele.style.display = 'none';
          }
        });
    }

    if (!accesTokn && loginButton === false) {
      const ele
        = document.getElementById('preloader')
        ?? document.getElementById('preloader1');

      if (ele) {
        setTimeout(() => {
          ele.style.opacity = '0';
          setTimeout(() => {
            ele.style.display = 'none';
          }, 2500);
        }, 2000);
      }
    }

    if (userDetails) {
      setLoginButton(true);
    }
  }, []);


  const loginButtonJSx = (
    <>
      <li className = 'signin-li'>
        <a
          href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${window.location.href}&device_type=web&response_type=get` }>
          {t('prejoin.signinsignup')}
        </a>
      </li>
    </>
  );

  /**
   * Event used to logout the user from the app.
   *
   * @param {event} e - Event object..
   * @private
   * @returns {void}
   */
  function logOutClicked(e) {
    e.preventDefault();
    jitsiLocalStorage.clear();
    dispatch(setuserDetails(undefined));
    dispatch(
      updateSettings({
        displayName: undefined,
        email: undefined
      })
    );
    window.location.reload();
  }
  let logoutDocument;

  if (loginButton) {
    logoutDocument = (
      <li className = 'drop-down d-flex'>
        {userd && userd?.picture ? (
          <img
            className = 'avatar'
            src = { userd?.picture }
            style = { styleWeb.widthandHeight } />
        ) : (
          <Avatar
            className = 'premeeting-screen-avatar'
            displayName = { name }
            dynamicColor = { true }
            participantId = 'local'
            size = { 45 } />
        )}

        <a href = ''>{userd ? userd?.name : ''}</a>
        <ul className = 'drop-down1'>
          <li>
            <a
              href = { `${config.URL_PREFIX}serviceLogin?client_id=${
                config.MH_CLIENT_ID
              }&redirect_uri=${
                config.URL_PREFIX
              }customer/dashboard&device_type=web${
                state1.accessToken ?? userAccessToken
                  ? `&access_token=${state1.userAccessToken ?? userAccessToken}`
                  : ''
              }` }>
              <i className = 'bx bxs-dashboard' /> {t('prejoin.dashboard')}
            </a>
          </li>
          <li>
            <a
              href = { `${config.URL_PREFIX}serviceLogin?client_id=${
                config.MH_CLIENT_ID
              }&redirect_uri=${
                config.URL_PREFIX
              }customer/profile&device_type=web${
                state1.accessToken ?? userAccessToken
                  ? `&access_token=${state1.accessToken ?? userAccessToken}`
                  : ''
              }` }>
              <i className = 'bx bx-user' /> {t('prejoin.profile')}
            </a>{' '}
          </li>
          <li>
            <a
onClick = { logOutClicked }
style = { styleWeb.cusrorPointer }>
              <i className = 'bx bx-log-out' /> {t('prejoin.logout')}
            </a>
          </li>
        </ul>
      </li>
    );
  } else {
    logoutDocument = loginButtonJSx;
  }

  return (
    <React.Fragment>
      <header
        className = { 'fixed-top d-flex align-items-center' }
        id = 'header'
        style = { props.prejoin ? styleWeb.stickyPostion : null }>
        <div className = 'container d-flex align-items-center'>
          <div
className = 'logo mr-auto'
style = { styleWeb.width300 }>
            <Watermark />
          </div>

          {interfaceConfig
            && !interfaceConfig.disablePrejoinHeader
            && (config?.MEGAMENU_HEADER === true ? (
              <nav
                className = 'nav-menu d-none d-lg-flex desktop-width navbar navbar-expand-lg navbar-light nav-menu-nextjs'
                style = { styleWeb.width100 }>
                <div className = 'container-fluid'>
                  <button
                    aria-controls = 'navbarExampleOnHover'
                    aria-expanded = 'false'
                    aria-label = 'Toggle navigation'
                    className = 'navbar-toggler px-0'
                    data-mdb-target = '#navbarExampleOnHover'
                    data-mdb-toggle = 'collapse'
                    type = 'button'>
                    <i className = 'fas fa-bars' />
                  </button>

                  <div
                    className = 'collapse navbar-collapse justify-content-between'
                    id = 'navbarExampleOnHover'>


                    <ul className = 'float-right-desktop d-flex'>
                      <li style = { styleWeb.languageContainer }>
                        <LanguagesDropdown textColor = '#333666' />
                      </li>{' '}
                      <li style = { styleWeb.headerliRight }>
                        <a href = '/joinmeeting'>
                          {t('welcomepage.header.joinAMeeting')}
                        </a>
                      </li>
                      <li>{logoutDocument}</li>
                    </ul>
                  </div>
                </div>

                {/* <ul className="float-right-desktop">
                                    <li
                                        style={
                                            styleWeb.headerliRight
                                        }
                                    >
                                        <a href="/joinmeeting">
                                            Join a Meeting
                                        </a>
                                    </li>
                                    {logoutDocument}
                                </ul> */}
              </nav>
            ) : (
              <nav
                className = 'nav-menu d-none d-lg-block desktop-width '
                style = { styleWeb.width100 }>

                <ul className = 'float-right-desktop d-flex'>
                  <li style = { styleWeb.languageContainer }>
                    <LanguagesDropdown textColor = '#333666' />
                  </li>
                  <li
                    className = 'd-flex align-items-center joinM'
                    style = { styleWeb.headerliRight }>
                    <a href = '/joinmeeting'>
                      {t('welcomepage.header.joinAMeeting')}
                    </a>
                  </li>
                  {logoutDocument}
                </ul>
              </nav>
            ))}
        </div>
      </header>
    </React.Fragment>
  );
};
