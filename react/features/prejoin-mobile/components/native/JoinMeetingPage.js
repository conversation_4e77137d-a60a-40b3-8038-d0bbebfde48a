/* eslint-disable max-len */
/* eslint-disable react/no-multi-comp */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
// @flow
import moment from 'moment';
import React, { Component } from 'react';
import { Dimensions, Image, SafeAreaView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { connect } from 'react-redux';
import type { Dispatch } from 'redux';

import { appNavigate } from '../../../app/actions';
import { getDefaultURL } from '../../../app/functions.native';
import { translate } from '../../../base/i18n';
import { ColorPalette } from '../../../base/styles';


const screenHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({

    container: {
        flex: 1,
        backgroundColor: ColorPalette.darkblue,
        height: '100%',
        maxHeight: screenHeight,
        width: '100%'
    },
    input: {
        height: 50,
        margin: 12,
        marginTop: 20,
        borderRadius: 10,
        backgroundColor: '#fff',
        width: '80%',
        textAlign: 'center',
        fontSize: 16,
        fontWeight: '300'
    },
    buttonStyle: {
        height: 50,
        margin: 12,
        marginTop: 10,
        borderRadius: 10,
        width: '80%',
        justifyContent: 'center',
        alignItems: 'center'
    },
    button: {
        borderRadius: 10,
        height: 50,
        width: '100%',
        backgroundColor: '#047F3D',
        justifyContent: 'center',
        alignItems: 'center'
    }

});

const checkIfUrl = url => {
    if (!url) {
        return false;
    }
    let ifUrl;

    try {
        ifUrl = new URL(url);
    } catch {
        ifUrl = null;
    }
    if (ifUrl && typeof ifUrl === 'object') {
        return true;
    }

    return false;
};

type Props = {

    /** .........
     * Default url
     *
     */
     _url: ?string,

    /**
     * InterfaceConfig.
     */
     _interfaceConfig: ?object,

    /**
     * Recent list of conferences/meetings.
     */
    _recentList: Array,

        /**
     * The Redux dispatch Function.
     */
    dispatch: Dispatch<any>,

    /**
     * T function.
     */
    t: Function,
}

type State = {
    room: ?String,
    joining: boolean,
    list: ?Array
}

/**
 * The native container rendering the welcome page.
 *
 * @augments Component
 */
export class JoinMeetingPage extends Component<Props, State> {

    /**
     * 描述.
     *
     * @param {Object} props - Props of the class.
     * @returns {any}
     */
    constructor(props) {
        super(props);

        this.state = {
            room: null,
            joining: false,
            list: [],
            k: null
        };

        this._onJoin = this._onJoin.bind(this);
        this._onRoomChange = this._onRoomChange.bind(this);
    }

    /**
     * Implements React's {@link Component#componentWillUnmount()}. Invoked
     * immediately before this component is unmounted and destroyed.
     *
     * @inheritdoc
     */
    componentWillUnmount() {
        this._mounted = false;
    }

    /**
     * ComponentDidMount.
     *
     * @returns {any}
     */
    componentDidMount() {
        this._mounted = true;
        if (Array.isArray(this.props._recentList)) {
            const revv = this.props._recentList.reverse().slice(-3);

            // eslint-disable-next-line react/no-did-mount-set-state
            this.setState(prevState => {
                return {
                    ...prevState,
                    list: revv
                };
            });
        }
    }

    /**
     * 描述.
     *
     * @param {string} param - Parameter.
     * @returns {any}
     */
    _onJoin(param) {
        const room = typeof param === 'string' ? param : this.state.room;

        if (room) {
            this.setState({ joining: true });

            // By the time the Promise of appNavigate settles, this component
            // may have already been unmounted.
            const onAppNavigateSettled
                = () => this._mounted && this.setState({ joining: false });

            this.props.dispatch(appNavigate(room))
                .then(onAppNavigateSettled, onAppNavigateSettled);
        }
    }

    /**
     * Handles 'change' event for the room name text input field.
     *
     * @param {string} value - The text typed into the respective text input
     * field.
     * @protected
     * @returns {void}
     */
    _onRoomChange(value: string) {
        this.setState({
            room: value
        });
    }

    /**
     * Implements React's {@link Component#render()}. Renders a prompt for
     * entering a room name.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const roomnameAccLabel = 'welcomepage.accessibilityLabel.roomname';
        const { list } = this.state;
        const { t, _interfaceConfig } = this.props;
        const defaultpath = _interfaceConfig?.DEFAULT_LOGO_URL;
        const urll = typeof _interfaceConfig === 'object' && _interfaceConfig.hasOwnProperty('DEFAULT_LOGO_URL') && checkIfUrl(_interfaceConfig?.DEFAULT_LOGO_URL) ? { uri: defaultpath } : require('../../../../../images/watermark.png');


        return (
            <SafeAreaView style = { styles.container } >

                <View
                    style = {{ justifyContent: 'center',
                        alignItems: 'center',
                        marginTop: 40 }}>
                    <Image
                        source = { urll }
                        style = {{ height: 70,
                            width: 290,
                            backgroundColor: 'white',
                            borderRadius: 15 }} />
                    <Text
                        style = {{ color: '#fff',
                            marginTop: 60,
                            fontSize: 20,
                            fontWeight: '300' }}>{t('lobby.joinTitle')}</Text>
                    <TextInput
                        accessibilityLabel = { t(roomnameAccLabel) }
                        autoCapitalize = 'none'
                        autoComplete = 'off'
                        autoCorrect = { false }
                        autoFocus = { false }
                        onChangeText = { this._onRoomChange }
                        onSubmitEditing = { this._onJoin }
                        placeholder = { t(
                                    'welcomepage.roomname'
                        ) }
                        returnKeyType = { 'go' }
                        style = { styles.input }
                        underlineColorAndroid = 'transparent'
                        value = { this.state.room } />
                    <View style = { styles.buttonStyle }>
                        <TouchableOpacity
                            onPress = { this._onJoin }
                            style = { styles.button }>
                            <Text
                                style = {{
                                    fontSize: 16,
                                    fontWeight: '400',
                                    color: '#fff'
                                }}>{t('lobby.joinTitle')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                {Array.isArray(list) && list.length > 0 ? <View
                    style = {{
                        marginTop: 15
                    }}>
                    <Text
                        style = {{
                            textAlign: 'center',
                            fontSize: 20,
                            fontWeight: '300',
                            color: '#fff'
                        }}>
                        {t('prejoin.recentMeetings')}
                    </Text>
                    <View
                        style = {{
                            alignSelf: 'center',
                            width: '80%',
                            marginTop: 10,
                            backgroundColor: '#fff',
                            height: 200,
                            borderRadius: 10
                        }} >
                        {
                            Array.isArray(list) && list.length > 0 && list.map((item, i) => {
                                const urrl = item.conference ? new URL(item.conference)?.pathname : null;

                                return (
                                    <TouchableOpacity
                                        key = { i }
                                        // eslint-disable-next-line react/jsx-no-bind
                                        onPress = { () => this._onJoin(item.conference) }
                                        style = {{
                                            borderBottomColor: i === (list.length - 1) && list.length >= 3 ? null : '#D6D6D6',
                                            borderBottomWidth: i === (list.length - 1) && list.length >= 3 ? null : 1,
                                            paddingLeft: 15,
                                            paddingRight: 15,
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            width: '95%',
                                            height: '33%'
                                        }}>
                                        <View>
                                            <View
                                                style = {{
                                                    width: '100%',
                                                    display: 'flex',
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between'
                                                }}>
                                                <Text
                                                    style = {{ fontSize: 18,
                                                        fontWeight: '400' }}>
                                                    {urrl?.slice(1)}
                                                </Text>
                                                <Text
                                                    style = {{ fontSize: 12,
                                                        fontWeight: '300',
                                                        marginTop: 5 }}>
                                                    { item.date ? t('prejoin.daysAgo', {
                                                        daysCount: moment().diff(item.date, 'days')
                                                    }) : null}
                                                </Text>
                                            </View>
                                            <View
                                                style = {{
                                                    width: '100%',
                                                    display: 'flex',
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between'
                                                }}>
                                                <Text>
                                                    {moment(item.date).locale('en-gb')
.format('HH:mm a')}
                                                </Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity>);
                            })
                        }

                    </View>
                </View> : <View />}


            </SafeAreaView>
        );
    }
}

const mapStateToProps = state => {
    const { interfaceConfig } = state['features/base/config'];
    const url = getDefaultURL(state);

    return {
        _interfaceConfig: interfaceConfig,
        _recentList: state['features/recent-list'],
        _url: url
    };
};

/**
 * Maps (parts of) the redux function to the React {@code Component} props of
 * {@code AbstractWelcomePage}.
 *
 * @param {Function} dispatch - The redux state.
 * @protected
 * @returns {Props}
 */
function _mapDispatchToProps(dispatch: Function) {
    return {
        dispatch
    };
}

export default translate(connect(mapStateToProps, _mapDispatchToProps)(JoinMeetingPage));
