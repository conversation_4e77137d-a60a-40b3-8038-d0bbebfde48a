/* eslint-disable */
import { jitsiLocalStorage } from '@jitsi/js-utils';
import $ from 'jquery';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MediaQuery from 'react-responsive';

import { Avatar } from '../../base/avatar';
import { apiKEY } from '../../base/config/constants';
import { removeUrlState } from '../../base/jwt';
import { JWT_STORAGE, SET_ACCESS_TOKEN } from '../../base/jwt/actionTypes';
import { fetchApi, fetchpreRegApi, fetchUserApi, setInvalidMeeting, setJWTforGuest, setuserDetails } from '../../base/mtApi';
import { Watermark } from '../../base/react';
import { getDisplayName, updateSettings } from '../../base/settings';
import { getBackendSafeRoomName, parseURLParams } from '../../base/util';
import { isDomainWeborNative } from '../../base/util/checkOS';
import Conference from '../../conference/components/web/Conference';
import { styleWeb } from '../../welcome/components/stylesWeb';


declare var config: object;
// declare var interfaceConfig: object;

export default function ApiCalls() {
    const dispatch = useDispatch();
    const [ loginButton, setLoginButton ] = useState(false);
    const [ openLoader,setOpenLoader ] = useState(true)
    const [ hideConference,setHideConference]= useState(true);
    const state1 = useSelector(state => state['features/base/jwt']);
    const {interfaceConfig} =  useSelector(state => state['features/base/config']);
    const name = useSelector(state => getDisplayName(state));
    const { userDetails, pregData, desktopapp } = useSelector(state => state['features/base/mtApi']);

    const iAmRecorder = useSelector(state => state['features/base/config'].iAmRecorder);

    const userAccessToken = jitsiLocalStorage.getItem('accessToken');
    const roomName = getBackendSafeRoomName(useSelector(state => state['features/base/conference'].room));
    const { locationURL } = useSelector(state => state['features/base/connection']);
    const urlPathName = locationURL?.pathname?.length;

    React.useLayoutEffect(() => {
        dispatch(fetchpreRegApi());
    },[])

    useEffect(() => {

        // console.log('userAccessToken ==>', userAccessToken, 'state1 ==>', state1.accessToken, 'userDetails ==>', userDetails);
        let accesTokn = state1.accessToken ?? userAccessToken === 'null' ? null : userAccessToken;
        const urlTest = locationURL?.pathname?.length;
        const urrr = parseURLParams(locationURL, true, 'search');
        if(urrr?.logout && urrr?.client_id && urrr?.client_id === config?.MH_CLIENT_ID){
            dispatch(setuserDetails(undefined));
            accesTokn = undefined;
            jitsiLocalStorage.clear();
            const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');
            
            setTimeout(() => {
                setOpenLoader(false)
                         }, 2000);
            // if (ele) {
            //     setTimeout(() => {
            //         ele.style.opacity = '0';
            //         setTimeout(() => {
            //             ele.style.display = 'none';
            //         }, 1000);
            //     }, 200);
            // }
        }

        if ((_.isNil(userDetails) && pregData && accesTokn) || Boolean(_.isNil(userDetails) && urlTest <= 1 && accesTokn)) {

            const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');
    
            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

            const preRegObject = JSON.stringify({
                'client_id': config.MH_CLIENT_ID,
                'credentials': mesh256,
                ...isDomainWeborNative,
                'meeting_id': roomName
            });
            fetchApi('customer/user_details', 'post', {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${state1.accessToken ?? userAccessToken}`
            },
            preRegObject
            ).then((response) => {
                const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);
                // eslint-disable-next-line camelcase
                // const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1);
                const { jwt = '' } = response?.data ?? {};

                // Also Skipping the pre-registration page redirect for Recorder
                if (isTrueReg && !state1.jwt && !state1.jwtData && _.isEmpty(jwt) && !iAmRecorder === true) {
                    return window.location.replace(`${config?.brandedPreRegistrationURL ? config?.brandedPreRegistrationURL : pregData.registration_url}`);
                }
                // setUserd(response.data);
                if (response.success) {
                    const namess = response?.data?.name;
                    const emailss = response?.data?.email;
                    const pict = response?.data?.picture;
    
                    dispatch(updateSettings({
                        avatarURL: pict,
                        displayName: namess,
                        email: emailss
                    }));
                    dispatch({ type: SET_ACCESS_TOKEN,
                        accessToken: state1.accessToken ?? userAccessToken })
                        const getJwt = response.data?.jwt;

                    if (!_.isEmpty(getJwt) && !state1?.jwt) {
                        dispatch({
                            type: JWT_STORAGE,
                            jwtData: getJwt
                        });
                    } else {
                        setTimeout(() => {
                            setHideConference(false)
                                     }, 2000);
                                    }
                    if(_.isEmpty(getJwt) && !state1?.jwt && !state1.jwtData && interfaceConfig?.applyMeetingSettings === true) {
                        dispatch(setJWTforGuest())

                    }
                } else {
                    
                    jitsiLocalStorage.removeItem('accessToken')
                }

                const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');
                setTimeout(() => {
                    setOpenLoader(false)
                             }, 2000);
                        //  setOpenLoader(false)
                // if (ele) {
                //     setTimeout(() => {
                //          ele.style.opacity = '0';
                //          setTimeout(() => {
                //              ele.style.display = 'none';
                //          }, 1000);
                //      }, 200);
                //  }



                response?.code === 401 ? setHideConference(false) :  dispatch(setuserDetails(response.data));
            })
            .catch(e => {
                console.error('errorResponse1234', e);
                const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');
                setTimeout(() => {
                    console.error('errorResponse1234',)
                    setOpenLoader(false)
                            }, 2000);
                // if (ele) {
                //     setTimeout(() => {
                //         ele.style.opacity = '0';
                //         setTimeout(() => {
                //             ele.style.display = 'none';
                //         }, 1000);
                //     }, 200);
                // }
            });

        }

        const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);

        // // const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1);

        // Also Skipping the pre-registration page redirect for Recorder
        if (isTrueReg && !state1.jwt && !state1.jwtData && !accesTokn && parseURLParams(locationURL)?.mt === undefined && !iAmRecorder === true) {
            return window.location.replace(`${config?.brandedPreRegistrationURL ? config?.brandedPreRegistrationURL : pregData.registration_url}`);
        }

        if (!accesTokn) {
            const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');
            // if (ele) {
                setTimeout(() => {
                    setOpenLoader(false)
                             }, 2000);
            //     setTimeout(() => {
            //          setOpenLoader(false)
            //          ele.style.opacity = '0';
            //          setTimeout(() => {
            //              ele.style.display = 'none';
            //             }, 1000);
            //      }, 200);
            //  }
        }

        if (userDetails) {
            setLoginButton(true);
        }


    }, [ pregData?.success ]);

    React.useEffect(() => {
        if(!openLoader && (state1.isRendered || state1?.jwt || state1.jwtData)){
            setHideConference(false)
        }
    },[openLoader,state1?.isRendered, state1?.jwt ])

    React.useEffect(() => {
        if(!hideConference){
            setTimeout(() => {
                dispatch(removeUrlState());
            },500)
        }
    },[hideConference])



  return (
    <>
       {
            hideConference ? <div id='preloader'  style={{ backgroundColor: interfaceConfig?.applyMeetingSettings === true ? interfaceConfig.DEFAULT_BACKGROUND : '#333666'}}>
                </div> : <Conference />
        }
    </>
  )
}
