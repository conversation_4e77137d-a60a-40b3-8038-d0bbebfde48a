// @flow

import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { hideDialog } from '../../../../base/dialog';
import { CustomDialog } from '../../../../base/dialog/components/native';
import { translate } from '../../../../base/i18n';
import { connect } from '../../../../base/redux';
import AbstractRecordingConsentDialog, {
    mapStateToProps
} from '../AbstractRecordingConsentDialog';

const styles = StyleSheet.create({
    container: {
        padding: 16
    },
    header: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
        marginTop: -40
    },
    // eslint-disable-next-line react-native/no-color-literals
    message: {
        marginBottom: 16,
        fontSize: 14,
        lineHeight: 20,
        color: '#333'
    },
    // eslint-disable-next-line react-native/no-color-literals
    disclaimer: {
        marginBottom: 0,
        fontSize: 12,
        lineHeight: 16,
        color: '#666'
    },
    buttonsParentContainer: {
        flexDirection: 'row', // arrange children horizontally
        justifyContent: 'flex-end', // optional, to center the buttons
        alignItems: 'center', // optional, to align vertically
        marginBottom: 20 // optional, spacing around
    },
    // eslint-disable-next-line react-native/no-color-literals
    acceptButton: {
        backgroundColor: '#333666',
        padding: 10, // space between buttons
        color: 'gray',
        marginRight: 10,
        borderRadius: 10
    },
    // eslint-disable-next-line react-native/no-color-literals
    acceptButtonText: {
        color: '#ffffff'
    },
    // eslint-disable-next-line react-native/no-color-literals
    leaveButton: {
        backgroundColor: 'gray',
        padding: 10, // space between buttons
        color: 'gray',
        marginRight: 10,
        borderRadius: 10
    },
    // eslint-disable-next-line react-native/no-color-literals
    leaveButtonText: {
        color: '#ffffff'
    }
});

/**
 * React Component for getting consent from participants before recording starts.
 *
 * @augments Component
 */
class RecordingConsentDialog extends AbstractRecordingConsentDialog {
    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const { t, dispatch } = this.props;

        return (
            <CustomDialog>
                <View style = { styles.container }>
                    <Text style = { styles.header }>{t('recording.consentDialog.title')}</Text>
                    <Text style = { styles.message }>
                        {t('recording.consentDialog.message')}
                    </Text>
                    <Text style = { styles.disclaimer }>
                        {t('recording.consentDialog.disclaimer')}
                    </Text>
                </View>
                <View style = { styles.buttonsParentContainer }>
                    <View style = { styles.acceptButton }>
                        <TouchableOpacity
                            onPress = { () => dispatch(hideDialog()) }>
                            <Text
                                style = { styles.acceptButtonText }>
                                {t('recording.consentDialog.accept')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                    <View style = { styles.leaveButton }>
                        <TouchableOpacity
                            onPress = { this._onDecline }>
                            <Text
                                style = { styles.leaveButtonText }>
                                {t('recording.consentDialog.leaveMeeting')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </CustomDialog>
        );
    }
    _onDecline: () => boolean;
}


export default translate(connect(mapStateToProps)(RecordingConsentDialog));
