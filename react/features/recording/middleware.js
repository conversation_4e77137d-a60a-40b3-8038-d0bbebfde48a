/* @flow */


import { sha256 } from 'js-sha256';
import moment from 'moment';

import {
    createRecordingEvent,
    sendAnalytics
} from '../analytics';
import logger from '../app/logger';
import { APP_WILL_MOUNT, APP_WILL_UNMOUNT } from '../base/app';
import { CONFERENCE_WILL_JOIN, getCurrentConference } from '../base/conference';
import { apiKEY } from '../base/config/constants';
import MeetHourJS, {
    MHConferenceEvents,
    MHRecordingConstants
} from '../base/lib-meet-hour';
import { IS_RECORDING_AND_LIVESTREAMING_ON, fetchAxiosApi, recordingandLivestreamingOn } from '../base/mtApi';
import { ULR_PATHS } from '../base/mtApi/constants';
import {
    getParticipantDisplayName,
    isLocalParticipantModerator,
    showRecordingConsentModal
} from '../base/participants';
import { USER_WAITING_REGISTER, USER_WAITING_SOUND } from '../base/premeeting/components/web/constants';
import { MiddlewareRegistry, StateListenerRegistry } from '../base/redux';
import {
    playSound,
    registerSound,
    stopSound,
    unregisterSound
} from '../base/sounds';
import { getBackendSafeRoomName } from '../base/util';
import { checkIfNotReactNative, isDomainWeborNative } from '../base/util/checkOS';
import { showErrorNotification } from '../notifications';
import { setRequestingSubtitles } from '../subtitles/actions';

import { RECORDING_SESSION_UPDATED } from './actionTypes';
import {
    clearRecordingSessions,
    hidePendingRecordingNotification,
    showPendingRecordingNotification,
    showRecordingError,
    showRecordingLimitNotification,
    showStartedRecordingNotification,
    showStoppedRecordingNotification,
    updateRecordingSessionData
} from './actions';
import {
    LIVE_STREAMING_OFF_SOUND_ID,
    LIVE_STREAMING_ON_SOUND_ID,
    RECORDING_OFF_SOUND_ID,
    RECORDING_ON_SOUND_ID
} from './constants';
import { getResourceId, getSessionById } from './functions';
import {
    LIVE_STREAMING_OFF_SOUND_FILE,
    LIVE_STREAMING_ON_SOUND_FILE,
    RECORDING_OFF_SOUND_FILE,
    RECORDING_ON_SOUND_FILE
} from './sounds';


/**
 * StateListenerRegistry provides a reliable way to detect the leaving of a
 * conference, where we need to clean up the recording sessions.
 */
StateListenerRegistry.register(
    /* selector */ state => getCurrentConference(state),
    /* listener */ (conference, { dispatch }) => {
        if (!conference) {
            dispatch(clearRecordingSessions());
        }
    }
);

/**
 * The redux middleware to handle the recorder updates in a React way.
 *
 * @param {Store} store - The redux store.
 * @returns {Function}
 */
MiddlewareRegistry.register(({ dispatch, getState }) => next => action => {
    let oldSessionData;

    if (action.type === RECORDING_SESSION_UPDATED) {
        oldSessionData
            = getSessionById(getState(), action.sessionData.id);
    }

    const result = next(action);

    switch (action.type) {
    case APP_WILL_MOUNT:
        if (checkIfNotReactNative()) {
            dispatch(registerSound(
                LIVE_STREAMING_OFF_SOUND_ID,
                LIVE_STREAMING_OFF_SOUND_FILE));

            dispatch(registerSound(
                LIVE_STREAMING_ON_SOUND_ID,
                LIVE_STREAMING_ON_SOUND_FILE));

            dispatch(registerSound(
                    RECORDING_ON_SOUND_ID,
                    RECORDING_ON_SOUND_FILE));

            dispatch(registerSound(
                RECORDING_OFF_SOUND_ID,
                RECORDING_OFF_SOUND_FILE));

            dispatch(registerSound(USER_WAITING_REGISTER, USER_WAITING_SOUND, { loop: true }));
        }


        break;

    case APP_WILL_UNMOUNT:
        dispatch(unregisterSound(LIVE_STREAMING_OFF_SOUND_ID));
        dispatch(unregisterSound(LIVE_STREAMING_ON_SOUND_ID));
        dispatch(unregisterSound(RECORDING_OFF_SOUND_ID));
        dispatch(unregisterSound(RECORDING_ON_SOUND_ID));

        break;

    case CONFERENCE_WILL_JOIN: {
        const { conference } = action;

        conference.on(
            MHConferenceEvents.RECORDER_STATE_CHANGED,
            recorderSession => {

                if (recorderSession) {
                    recorderSession.getID()
                        && dispatch(
                            updateRecordingSessionData(recorderSession));

                    recorderSession.getError()
                        && _showRecordingErrorNotification(
                            recorderSession, dispatch);
                }

                return;
            });


        break;
    }

    case RECORDING_SESSION_UPDATED: {
        // When in recorder mode no notifications are shown
        // or extra sounds are also not desired
        // but we want to indicate those in case of sip gateway
        const {
            iAmRecorder,
            iAmSipGateway,
            disableRecordAudioNotification,
            transcription,
            recordingLimit
        } = getState()['features/base/config'];

        const conference = getCurrentConference(getState());

        const { isRecordingandLivestreamingOn = false } = getState()['features/base/mtApi'];

        const isTranscribing = getState()['features/transcribing']?.isTranscribing;

        let isRecordAndSIPGATEWAY = false;
        let isRecordAndTRANSCRIBER = false;

        if (iAmRecorder && isTranscribing) {
            isRecordAndTRANSCRIBER = true;
        }

        if (iAmRecorder && !iAmSipGateway) {
            isRecordAndSIPGATEWAY = true;
        }

        const updatedSessionData
            = getSessionById(getState(), action.sessionData.id);
        const { initiator, mode, terminator } = updatedSessionData;
        const { PENDING, OFF, ON } = MHRecordingConstants.status;

        if (!isRecordAndSIPGATEWAY) {
            if (updatedSessionData.status === PENDING
                    && (!oldSessionData || oldSessionData.status !== PENDING)) {
                dispatch(showPendingRecordingNotification(mode));
            } else if (updatedSessionData.status !== PENDING) {
                dispatch(hidePendingRecordingNotification(mode));
            }
        }

        if (isRecordAndTRANSCRIBER) {
            const { confSettings } = getState()['features/base/jwt'];
            const _autoCaptionOnRecord = transcription?.autoCaptionOnRecord ?? false;
            const language = getState()['features/subtitles']._language;

            if (transcription?.enabled && (confSettings?.AUTO_CAPTION_ON_RECORDING ?? _autoCaptionOnRecord) && Boolean(language) === false) {

                if (confSettings?.TRANSCRIPTION_QUOTA_EXCEEDED === 1 ?? false) {
                    if (isLocalParticipantModerator(getState())) {
                        dispatch(showErrorNotification(
                    {
                        descriptionKey: 'transcribing.transcriptionQuotaExceeded',
                        titleKey: 'transcribing.transcriptionQuotaExceededTitle'
                    }));
                    } else {
                        return;
                    }
                } else {
                    dispatch(setRequestingSubtitles(true, Boolean(language), getState()['features/subtitles']._language));
                }
            }
        }

        if (updatedSessionData.status === ON
                && (!oldSessionData || oldSessionData.status !== ON) && !isRecordAndSIPGATEWAY) {
            if (typeof recordingLimit === 'object') {
                // Show notification with additional information to the initiator.
                dispatch(showRecordingLimitNotification(mode));
            } else {
                dispatch(showStartedRecordingNotification(
                        mode, initiator && getParticipantDisplayName(getState, action.sessionData.id)));

                // Show recording consent modal to guest participants (non-moderators)
                // Use a small delay to ensure participant roles are properly set
                const state = getState();
                const participants = state['features/base/participants'];

                participants.forEach(participant => {

                    const isLocalModeratorAlsoStartedRecording = participant?.isRecordingStartedByUser;

                    if (!iAmRecorder && isLocalModeratorAlsoStartedRecording === false) {
                        dispatch(showRecordingConsentModal(participant.id));
                    }
                });
            }

            sendAnalytics(createRecordingEvent('start', mode));

            if (disableRecordAudioNotification) {
                break;
            }

            let soundID;
            let soundID1;

            if (mode === MHRecordingConstants.mode.FILE && !isRecordingandLivestreamingOn) {
                soundID = RECORDING_ON_SOUND_ID;
            } else if (mode === MHRecordingConstants.mode.STREAM) {
                soundID = LIVE_STREAMING_ON_SOUND_ID;
            } else if (mode === MHRecordingConstants.mode.FILE && isRecordingandLivestreamingOn) {
                soundID = RECORDING_ON_SOUND_ID;
                soundID1 = LIVE_STREAMING_ON_SOUND_ID;
            }

            if (soundID) {
                dispatch(playSound(soundID));
                if (soundID1) {
                    setTimeout(() => {
                        dispatch(playSound(soundID1));
                    }, 4000);
                }
            }


        } else if (updatedSessionData.status === OFF
                && (!oldSessionData || oldSessionData.status !== OFF)) {
            if (!isRecordAndSIPGATEWAY) {
                dispatch(
                        showStoppedRecordingNotification(
                            mode, getParticipantDisplayName(getState, getResourceId(terminator))));
            }
            let duration = 0, soundOff, soundOff1, soundOn, soundon1;

            if (oldSessionData && oldSessionData.timestamp) {
                duration
                        = (Date.now() / 1000) - oldSessionData.timestamp;
            }
            sendAnalytics(createRecordingEvent('stop', mode, duration));

            if (disableRecordAudioNotification) {
                break;
            }

            let liveStreamDurationApiCall = false; // Use to send api call to portal for duration of livestreaming.

            if (mode === MHRecordingConstants.mode.FILE && !isRecordingandLivestreamingOn) {
                soundOff = RECORDING_OFF_SOUND_ID;
                soundOn = RECORDING_ON_SOUND_ID;
            } else if (mode === MHRecordingConstants.mode.STREAM) {
                soundOff = LIVE_STREAMING_OFF_SOUND_ID;
                soundOn = LIVE_STREAMING_ON_SOUND_ID;
                liveStreamDurationApiCall = true;
            } else if (mode === MHRecordingConstants.mode.FILE && isRecordingandLivestreamingOn) {
                soundOff = RECORDING_OFF_SOUND_ID;
                soundOn = RECORDING_ON_SOUND_ID;
                soundOff1 = LIVE_STREAMING_OFF_SOUND_ID;
                soundon1 = LIVE_STREAMING_ON_SOUND_ID;
                liveStreamDurationApiCall = true;
            }
            dispatch(recordingandLivestreamingOn(false));
            conference.sendCommand(IS_RECORDING_AND_LIVESTREAMING_ON, {
                value: false
            });

            if (soundOff && soundOn) {
                if (!isRecordAndSIPGATEWAY) {
                    dispatch(stopSound(soundOn));
                    dispatch(playSound(soundOff));
                }
                if (soundOff1 && soundon1) {

                    setTimeout(() => {
                        if (!isRecordAndSIPGATEWAY) {
                            dispatch(stopSound(soundon1));
                            dispatch(playSound(soundOff1));
                        }
                    }, 4000);
                }
                if (liveStreamDurationApiCall === true) {
                    const config = getState()['features/base/config'];
                    const MH_CLIENT_ID = config?.MH_CLIENT_ID;
                    const roomName = getBackendSafeRoomName(getState()['features/base/conference'].room);

                    _liveStreamingApiCall(MH_CLIENT_ID, roomName, duration, config).then(response => {
                        if (response.success === undefined || response?.success !== true) {
                            logger.error('Error in LiveStreamAPI for Duration failed');
                        }
                    });
                }
            }
        }
    }

        break;
    }

    return result;
});

/**
 * API CAll for LiveStreaming Duration update for Portal.
 *
 * @private
 * @param {string} clientid - Portal Client ID.
 * @param {string} roomName - Room Name.
 * @param {string} meetingDuration - LiveStreaming Duration.
 * @param {Object} config - Config from state.
 * @returns {bool | Object}
 */
async function _liveStreamingApiCall(clientid, roomName, meetingDuration, config) {
    const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

    const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

    const x = meetingDuration;
    const int_part = Math.trunc(x);
    const float_part = Number((x - int_part).toFixed(2));

    const hours = Math.round((Math.floor(meetingDuration) / 60) / 60);
    const minutes = Math.round(meetingDuration / 60);
    const seconds = Math.round(float_part * 60);
    const milliseconds = Math.round(Math.floor(meetingDuration) / 1000);

    const finalDuration = `${hours}:${minutes}:${seconds}.${milliseconds}`;

    const referenceParam = {
        meeting_id: roomName,
        client_id: clientid,
        credentials: mesh256,
        duration: finalDuration,
        options: [ 'Livestream' ],
        ...isDomainWeborNative
    };

    const liveStreamAPIUpdate = await fetchAxiosApi(ULR_PATHS.LIVESTREAM_USAGE_UPDATE,
    'POST',
    {
        'Content-Type': 'application/json',
        'Accept': '*'
    }, referenceParam, config).catch(
        err => logger.error('Error in hitting LiveStreamingDurationAPI in api', err)
    );

    return liveStreamAPIUpdate ? liveStreamAPIUpdate : false;
}

/**
 * Shows a notification about an error in the recording session. A
 * default notification will display if no error is specified in the passed
 * in recording session.
 *
 * @private
 * @param {Object} recorderSession - The recorder session model from the
 * lib.
 * @param {Dispatch} dispatch - The Redux Dispatch function.
 * @returns {void}
 */
function _showRecordingErrorNotification(recorderSession, dispatch) {
    const isStreamMode
        = recorderSession.getMode()
            === MeetHourJS.constants.recording.mode.STREAM;

    switch (recorderSession.getError()) {
    case MeetHourJS.constants.recording.error.SERVICE_UNAVAILABLE:
        dispatch(showRecordingError({
            descriptionKey: 'recording.unavailable',
            descriptionArguments: {
                serviceName: isStreamMode
                    ? '$t(liveStreaming.serviceName)'
                    : '$t(recording.serviceName)'
            },
            titleKey: isStreamMode
                ? 'liveStreaming.unavailableTitle'
                : 'recording.unavailableTitle'
        }));
        break;
    case MeetHourJS.constants.recording.error.RESOURCE_CONSTRAINT:
        dispatch(showRecordingError({
            descriptionKey: isStreamMode
                ? 'liveStreaming.busy'
                : 'recording.busy',
            titleKey: isStreamMode
                ? 'liveStreaming.busyTitle'
                : 'recording.busyTitle'
        }));
        break;
    default:
        dispatch(showRecordingError({
            descriptionKey: isStreamMode
                ? 'liveStreaming.error'
                : 'recording.error',
            titleKey: isStreamMode
                ? 'liveStreaming.failedToStart'
                : 'recording.failedToStart'
        }));
        break;
    }
}
