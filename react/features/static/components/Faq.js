import { jitsiLocalStorage } from '@jitsi/js-utils';
import moment from 'moment';
import React, { useEffect, Component } from 'react';



import { getURLWithoutParams } from '../../base/connection';
import { connect } from '../../base/redux';
import { fetchCustomBrandingData } from '../../dynamic-branding';
import { Header } from '../../joinmeeting/components/Header';
import { Footer } from '../../welcome/components/Footer';

/**
 * The Web container rendering the welcome page.
 *
 * @extends WhoweAre
 */
export class Faq extends Component {
    /**
     * Implements React's {@link Component#componentDidMount()}. Invoked
     * immediately after this component is mounted.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        this.props._fetchCustomBrandingData();
        const mainCss = document.getElementById('mainCss')
        if(mainCss){
            mainCss.remove()
        }
        if (document.body) {
            document.body.style.overflowY = 'scroll';
        }
        if (document && document.body) {
            const link2 = document.createElement('link');

            link2.href = 'static/assets/vendor/bootstrap/css/bootstrap.min.css';
            link2.rel = 'stylesheet';
            link2.id = 'link2Style1';
            document.body.appendChild(link2);
            const link = document.createElement('link');

            link.href = '/static/assets/css/style1.css';
            link.rel = 'stylesheet';
            link.id = 'linkStyle1';
            document.body.appendChild(link);

            const script = document.createElement('script');

            script.src = '/static/assets/js/main.js';
            script.id = 'mainJS';
            script.async = true;
            document.body.appendChild(script);
        }
        const ele = document.getElementById('preloader');

        if (ele) {

            setTimeout(() => {
                ele.style.opacity = '0';
                setTimeout(() => {
                    ele.style.display = 'none';
                }, 1000);
            }, 1000);
        }
        const { locationURL } = this.props;

        if (locationURL) {
            const urlwithout = getURLWithoutParams(locationURL);

            window.history.replaceState(
                            history.state,
                            (document && document.title) || '',
                            urlwithout);
        }

    }

    /**
         * Implements React's {@link Component#render()}.
         *
         * @inheritdoc
         * @returns {ReactElement|null}
         */
    render() {
        return (
            <div>
                            <div id = 'preloader' />
                <Header />
                <main id = 'main'>


                    <section
                        style = {{
                            marginTop: '5%'
                        }}
                        className = 'faq section-bg'>
                        <div className = 'container'>

                            <div
                                className = 'section-title aos-init aos-animate'
                                data-aos = 'fade-up'>
                                <h2>F.A.Q</h2>
                                <p>Frequently Asked Questions</p>
                            </div>

                            <div className = 'faq-list'>
                                <ul>
                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init aos-animate'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            href = '#faq-list-1'>
      How does Meet Hour work?
                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-1'
                                            data-parent = '.faq-list'>
                                            <p>
             Once you installed the app and allowed all permissions to access, you simply enter a meeting room name into the room field. You have now just created your first conference. Then once in the meeting, you can open the menu using the 3 dots button at the bottom, then select "Invite others" to share the meeting details with others.

                                            </p>
                                        </div>
                                    </li>

                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init aos-animate'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
     I want to join class/meeting XY

                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-2'
                                            data-parent = '.faq-list'>
                                            <p>
              You should reach out to the organizer of that conference and get the room name to join.

                                            </p>
                                        </div>
                                    </li>

                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init aos-animate'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
     Can't activate my camera, or microphone.

                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-3'
                                            data-parent = '.faq-list'>
                                            <p>It sounds like a permission issue on the device. Can you please double check if you gave all the permissions to the app that it needs? The easiest way is to go to the device settings section of the phone, find the Meet Hour application, and turn all permissions on. On Android you may also need to go into "Apps and notifications" after opening Settings.

                                            </p>
                                        </div>
                                    </li>

                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init aos-animate'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
     How do I invite others to my meeting?


                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-4'
                                            data-parent = '.faq-list'>
                                            <p>Once in the meeting, you can open the menu using the 3 dots button at the bottom, then select ?Invite others? to share the meeting details with others.

                                            </p>
                                        </div>
                                    </li>

                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init aos-animate'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
      How do I use Meet Hour on Windows/Mac?


                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-5'
                                            data-parent = '.faq-list'>
                                            <p>Meet Hour is mainly a web based app, so on any other desktop operating systems, you can just navigate to https://shell.groupvi.systems using a Chrome / Safari / Edge browser and you're good to go. No need to download anything.

                                            </p>
                                        </div>
                                    </li>


                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init aos-animate'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
    What is the password?
                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-6'
                                            data-parent = '.faq-list'>
                                            <p>If you are prompted with a password when joining a conference, then you should reach out either to other participants in the conference, because they might have locked the conference, or to the IT administrator of your employer/school, because they must have set up a company/school-wide password for all conferences.
                                            </p>
                                        </div>
                                    </li>


                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init aos-animate'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
      How can I delete a room?
                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-7'
                                            data-parent = '.faq-list'>
                                            <p>You don't need to delete a room, as Meet Hour meeting rooms get deleted as soon as everyone leaves them. If you want to remove it from your app home screen too, you can just swipe left on the name and it will offer you to delete the entry.

                                            </p>
                                        </div>
                                    </li>


                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
      How can I change my profile picture?

                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-8'
                                            data-parent = '.faq-list'>
                                            <p>Meet Hour uses the free Gravatar service (https://en.gravatar.com/). If you sign up for that service with the email address you set up in the Meet Hour application, and set an avatar there, Meet Hour will show that profile photo in your next conference.

                                            </p>
                                        </div>
                                    </li>


                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>
             Someone entered my meeting I didn't know.
                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-9'
                                            data-parent = '.faq-list'>
                                            <p>Next time you can try using the "Room lock" feature. Using Room lock, you can set a simple password that you share with others in the conference (for the unlikely case they got dropped from the conference), so the only people who know that password can join your conference.
                                            </p>
                                        </div>
                                    </li>


                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>

   I cannot join the meeting / it randomly disconnects


                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-10'
                                            data-parent = '.faq-list'>
                                            <p> We occasionally experience some exceptionally heavy load and intermittent issues may happen. We suggest, you try again a bit later, if possible. If the problem persists, please reach out to us via email: <EMAIL> </p>
                                        </div>
                                    </li>


                                    <li
                                        data-aos = 'fade-up'
                                        className = 'aos-init'>
                                        <i className = 'bx bx-help-circle icon-help' /> <a
                                            data-toggle = 'collapse'
                                            className = 'collapsed'>


    I have other problems with the app.


                                            <i className = 'bx bx-chevron-down icon-show' /><i className = 'bx bx-chevron-up icon-close' /></a>
                                        <div
                                            id = 'faq-list-11'
                                            data-parent = '.faq-list'>
                                            <p>In this case please reach out to us via email: <EMAIL> </p>
                                        </div>
                                    </li>


                                </ul>
                            </div>

                        </div>
                    </section>

                </main>
                <Footer  prejoin={true}/>
            </div>
        );
    }
}

/**
 * Maps (parts of) the redux state to the React {@code Component} props of
 * {@code AbstractWelcomePage}.
 *
 * @param {Object} state - The redux state.
 * @protected
 * @returns {Props}
 */
export function mapStateToProps(state: Object) {
    const participants = state['features/base/participants'];
    const { userDetails } = state['features/base/mtApi'];
    const participant = participants.filter(i => i.id === 'local');
    const { locationURL } = state['features/base/connection'];


    return {
        _enableInsecureRoomNameWarning: state['features/base/config'].enableInsecureRoomNameWarning || false,
        _moderatedRoomServiceUrl: state['features/base/config'].moderatedRoomServiceUrl,
        _room: state['features/base/conference'].room,
        _settings: state['features/base/settings'],
        _localParticipant: participant,
        userDetails,
        locationURL
    };
}

const mapDispatchToProps = {
    _fetchCustomBrandingData: fetchCustomBrandingData
};

export default connect(mapStateToProps, mapDispatchToProps)(Faq);
