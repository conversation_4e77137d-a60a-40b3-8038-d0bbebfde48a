import { jitsiLocalStorage } from '@jitsi/js-utils';
import moment from 'moment';
import React, { useEffect, Component } from 'react';



import { getURLWithoutParams } from '../../base/connection';
import { connect } from '../../base/redux';
import { fetchCustomBrandingData } from '../../dynamic-branding';
import { Header } from '../../joinmeeting/components/Header';
import { Footer } from '../../welcome/components/Footer';

declare var config: Object;

/**
 * The Web container rendering the welcome page.
 *
 * @extends WhoweAre
 */
export class Hospitals extends Component {

    /**
     * Implements React's {@link Component#componentDidMount()}. Invoked
     * immediately after this component is mounted.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        this.props._fetchCustomBrandingData();

        if (document.body) {
            document.body.style.overflowY = 'scroll';
        }
        const mainCss = document.getElementById('mainCss')
        if(mainCss){
            mainCss.remove()
        }
        if (document && document.body) {
            const link2 = document.createElement('link');

            link2.href = 'static/assets/vendor/bootstrap/css/bootstrap.min.css';
            link2.rel = 'stylesheet';
            link2.id = 'link2Style1';
            document.body.appendChild(link2);
            const link = document.createElement('link');

            link.href = '/static/assets/css/style1.css';
            link.rel = 'stylesheet';
            link.id = 'linkStyle1';
            document.body.appendChild(link);

            const script = document.createElement('script');

            script.src = '/static/assets/js/main.js';
            script.id = 'mainJS';
            script.async = true;
            document.body.appendChild(script);
        }
        const ele = document.getElementById('preloader');

        if (ele) {

            setTimeout(() => {
                ele.style.opacity = '0';
                setTimeout(() => {
                    ele.style.display = 'none';
                }, 1000);
            }, 1000);
        }
        const { locationURL } = this.props;

        if (locationURL) {
            const urlwithout = getURLWithoutParams(locationURL);

            window.history.replaceState(
                                history.state,
                                (document && document.title) || '',
                                urlwithout);
        }

    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement|null}
    */
    render() {
        return (
<>
<div id = 'preloader' />
    <Header />
    <main id="main">
  {/* ======= Breadcrumbs Section ======= */}
  <section className="breadcrumbs">
    <div className="container">
      <div className="d-flex justify-content-between align-items-center">
        <h2>Hospitals</h2>
        <ol>
          <li>
            <a href="index.html">Home</a>
          </li>
          <li>Hospitals </li>
        </ol>
      </div>
    </div>
  </section>
  {/* End Breadcrumbs Section */}
  <section className="inner-page">
    <div className="container">
      <div className="row">
        <div className="col-md-5 col-sm-12">
          <h2>Meet Hour Solutions for Health Care</h2>
          <p>
            Meet Hour allows healthcare providers to extend the reach of their
            practice by providing secure, easy-to-join telehealth visits to
            patients from any device or location. During the pandemic, almost
            50% of all patient interactions have been virtual. This trend may
            continue into 2022.
          </p>
          <button 
          type="button" 
          className="btn btn-primary btn-lg"
          onClick = {() => {
                               const urll =  window.location.origin
                               return window.location.replace(`${urll}/contact`)
                        }}
          >
            Contact Sales
          </button>
        </div>
        <div className="col-md-7 col-sm-12">
          <div className="embed-responsive embed-responsive-16by9">
            <iframe
              className="embed-responsive-item"
              src="https://www.youtube.com/embed/9e3XR2fwzCE"
              allowFullScreen
            />
          </div>
        </div>
      </div>
      <br />
      <div className="row">
        <div className="col-md-4 col-sm-12">
          <div className="card">
            <img
                    className = 'img-fluid'
                    src = 'static/assets/solutions/hospitals/doctor-consulting.jpg' />
            <div className="card-body">
              <h5 className="card-title">Consult Doctor Online </h5>
            </div>
          </div>
        </div>
        <div className="col-md-4 col-sm-12">
          <div className="card">
            <img className="img-fluid" src='static/assets/solutions/hospitals/hospitals2.jpg' />
            <div className="card-body">
              <h5 className="card-title">Get Advice Anywhere</h5>
            </div>
          </div>
        </div>{" "}
        <div className="col-md-4 col-sm-12">
          <div className="card">
            <img className="img-fluid" src='static/assets/solutions/hospitals/hospitals3.jpg' />
            <div className="card-body">
              <h5 className="card-title">Connect Anywhere</h5>
            </div>
          </div>
        </div>
        <div
          className="col-md-12 col-sm-12"
          style={{ marginTop: "3%", marginBottom: "3%" }}
        >
          <h2 className="text-center">Flexible plans for hospitals</h2>
        </div>
        <div className="col-md-6 col-sm-12">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">Hospital Branded Conference</h5>
              <p className="card-text-big">
                Pro Plan: $6.49
                <span style={{ fontSize: 12 }}>/license/month</span>
              </p>
              <button type="button" className="btn btn-primary btn-lg" onClick={
                () => {
                  const urll =  `https://meet.groupvi.systems/register`
                  return window.location.replace(`${urll}`)
                }
              }>
                Buy Now
              </button>
            </div>
          </div>
        </div>
        <div className="col-md-6 col-sm-12">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">Integration with Hospitals</h5>
              <p className="card-text-big">
                Developer Plan: $13.29
                <span style={{ fontSize: 12 }}>/license/month</span>
              </p>
              <button type="button" className="btn btn-primary btn-lg" onClick={
                () => {
                  const urll =  `https://meet.groupvi.systems/register`
                  return window.location.replace(`${urll}`)
                }
              }>
                Buy Now
              </button>
            </div>
          </div>
        </div>
      </div>
      <br />
      <div className="row">
        <div className="col-md-7 col-sm-12">
          <img className="img-fluid" src='static/assets/solutions/hospitals/online-hospitals.jpg' />
        </div>
        <div className="col-md-5 col-sm-12">
          <h3>Why Meet Hour for Hospitals?</h3>
          <p>
            Meet Hour allows healthcare providers to extend the reach of their
            practice by providing secure, easy-to-join telehealth visits to
            patients from any device or location. During the pandemic, almost
            50% of all patient interactions have been virtual. This trend may
            continue into 2022.
          </p>
          <ul>
            <li>Conducts secure online session between patient and doctor </li>
            <li>
              Can use from any device (mobile app or desktop) without
              downloading via browser{" "}
            </li>
            <li>
              Extremely simple and easy to use for both clinicians and patients.{" "}
            </li>
            <li>HIPAA-compliant environment </li>
            <li>
              Connect from anywhere, at any time, eliminating the need for
              travel.{" "}
            </li>
          </ul>
        </div>
        <div className="col-md-12 col-sm-12">
          <h2>Meet Hour Features</h2>
          <ul>
            <li>Appointment Scheduling</li>
            <li>Patient Session Notes (Live pad)</li>
            <li>Consultation Fee Payment</li>
            <li>Secure Messaging (Chat Box)</li>
            <li>File Sharing</li>
            <li>Session Recording (Drop Box)</li>
            <li>Booking appointment is safe from home.</li>
            <li>Fastest way to access acute illnesses and injuries</li>
            <li>Specialist access will available.</li>
          </ul>
        </div>
      </div>
    </div>
  </section>
</main>
    <Footer prejoin = {true} />
</>
        );
    }
}

/**
 * Maps (parts of) the redux state to the React {@code Component} props of
 * {@code AbstractWelcomePage}.
 *
 * @param {Object} state - The redux state.
 * @protected
 * @returns {Props}
 */
export function mapStateToProps(state: Object) {
    const participants = state['features/base/participants'];
    const { userDetails } = state['features/base/mtApi'];
    const participant = participants.filter(i => i.id === 'local');
    const { locationURL } = state['features/base/connection'];


    return {
        _enableInsecureRoomNameWarning: state['features/base/config'].enableInsecureRoomNameWarning || false,
        _moderatedRoomServiceUrl: state['features/base/config'].moderatedRoomServiceUrl,
        _room: state['features/base/conference'].room,
        _settings: state['features/base/settings'],
        _localParticipant: participant,
        userDetails,
        locationURL
    };
}

const mapDispatchToProps = {
    _fetchCustomBrandingData: fetchCustomBrandingData
};

export default connect(mapStateToProps, mapDispatchToProps)(Hospitals);
