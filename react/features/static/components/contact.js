

import React, { Component } from 'react';

import { getURLWithoutParams } from '../../base/connection';
import { connect } from '../../base/redux';
import { fetchCustomBrandingData } from '../../dynamic-branding';
import { Header } from '../../joinmeeting/components/Header';
import { Footer } from '../../welcome/components/Footer';


/**
 * The Web container rendering the welcome page.
 *
 * @extends WhoweAre
 */
class contact extends Component {

    /**
     * Implements React's {@link Component#componentDidMount()}. Invoked
     * immediately after this component is mounted.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        this.props._fetchCustomBrandingData();
        const mainCss = document.getElementById('mainCss')
        if(mainCss){
            mainCss.remove()
        }
        if (document.body) {
            document.body.style.overflowY = 'scroll';
        }
        if (document && document.body) {
            const link2 = document.createElement('link');

            link2.href = 'static/assets/vendor/bootstrap/css/bootstrap.min.css';
            link2.rel = 'stylesheet';
            link2.id = 'link2Style1';
            document.body.appendChild(link2);
            const link = document.createElement('link');

            link.href = '/static/assets/css/style1.css';
            link.rel = 'stylesheet';
            link.id = 'linkStyle1';
            document.body.appendChild(link);

            const script = document.createElement('script');

            script.src = '/static/assets/js/main.js';
            script.id = 'mainJS';
            script.async = true;
            document.body.appendChild(script);
        }
        const ele = document.getElementById('preloader');

        if (ele) {

            setTimeout(() => {
                ele.style.opacity = '0';
                setTimeout(() => {
                    ele.style.display = 'none';
                }, 1000);
            }, 1000);
        }
        const { locationURL } = this.props;

        if (locationURL) {
            const urlwithout = getURLWithoutParams(locationURL);

            window.history.replaceState(
                            history.state,
                            (document && document.title) || '',
                            urlwithout);
        }

    }

    UNSAFE_componentWillMount(){

    }

    /**
         * Implements React's {@link Component#render()}.
         *
         * @inheritdoc
         * @returns {ReactElement|null}
         */
    render() {
        return (
            <div>
                            <div id = 'preloader' />
                <Header />
                <main id = 'main'>


                    <section className = 'breadcrumbs'>
                        <div className = 'container'>

                            <div className = 'd-flex justify-content-between align-items-center'>
                                <h2 style = {{ color: '#333'}}>Contact</h2>

                            </div>

                        </div>
                    </section>
                    <section
                        id = 'contact'
                        className = 'contact'>
                        <div className = 'container'>
                            <div className = 'row'>

                                <div
                                    className = 'col-lg-12 aos-init aos-animate'
                                    data-aos = 'fade-right'
                                    data-aos-delay = '100'>
                                    <div className = 'info'>
                                        <div className = 'address'>
                                            <i className = 'icofont-google-map' />
                                            <h4>Location:</h4>
                                            <p>8825 Stanford , Suite 205
            Columbia, MD 21045</p>
                                        </div>

                                        <div className = 'email'>
                                            <i className = 'icofont-envelope' />
                                            <h4>Email:</h4>
                                            <p><a href = 'mailto:<EMAIL>'><EMAIL></a></p>
                                        </div>

                                        <div className = 'phone'>
                                            <i className = 'icofont-phone' />
                                            <h4>Call:</h4>
                                            <p>

                                                <a href = 'tel:+13128009410'>+**************** </a>

                                            </p>
                                        </div>

                                    </div>

                                </div>

                                <div
                                    className = 'col-lg-12 col-md-12'
                                    style = {{ marginTop: '5%' }}>
                                    <div className = 'embed-responsive embed-responsive-16by9'>

                                    <iframe className="embed-responsive-item"  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3092.37098518033!2d-76.82062898464099!3d39.18901827952676!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89b7e07d8a2f9d15%3A0xdc420419a0773fbd!2s8825%20Stanford%20Blvd%2C%20Columbia%2C%20MD%2021045%2C%20USA!5e0!3m2!1sen!2sin!4v1602010095004!5m2!1sen!2sin"  frameborder="0" style={{border:0}} allowfullscreen="" aria-hidden="false" tabindex="0" />

                                    </div>
                                </div>


                            </div>

                        </div>
                    </section>

                </main>
                <Footer prejoin = {true} />
            </div>
        );
    }
}

/**
 * Maps (parts of) the redux state to the React {@code Component} props of
 * {@code AbstractWelcomePage}.
 *
 * @param {Object} state - The redux state.
 * @protected
 * @returns {Props}
 */
export function mapStateToProps(state: Object) {
    const participants = state['features/base/participants'];
    const { userDetails } = state['features/base/mtApi'];
    const participant = participants.filter(i => i.id === 'local');
    const { locationURL } = state['features/base/connection'];


    return {
        _enableInsecureRoomNameWarning: state['features/base/config'].enableInsecureRoomNameWarning || false,
        _moderatedRoomServiceUrl: state['features/base/config'].moderatedRoomServiceUrl,
        _room: state['features/base/conference'].room,
        _settings: state['features/base/settings'],
        _localParticipant: participant,
        userDetails,
        locationURL
    };
}

const mapDispatchToProps = {
    _fetchCustomBrandingData: fetchCustomBrandingData
};

export default connect(mapStateToProps, mapDispatchToProps)(contact);
