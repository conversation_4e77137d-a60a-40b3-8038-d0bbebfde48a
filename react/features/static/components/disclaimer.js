import { jitsiLocalStorage } from '@jitsi/js-utils';
import moment from 'moment';
import React, { useEffect, Component } from 'react';

import { getURLWithoutParams } from '../../base/connection';
import { connect } from '../../base/redux';
import { fetchCustomBrandingData } from '../../dynamic-branding';
import { Header } from '../../joinmeeting/components/Header';
import { Footer } from '../../welcome/components/Footer';


/**
 * The Web container rendering the welcome page.
 *
 * @extends WhoweAre
 */
class disclaimer extends Component {

    /**
     * Implements React's {@link Component#componentDidMount()}. Invoked
     * immediately after this component is mounted.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        this.props._fetchCustomBrandingData();
        const mainCss = document.getElementById('mainCss')
        if(mainCss){
            mainCss.remove()
        }
        if (document.body) {
            document.body.style.overflowY = 'scroll';
        }
        if (document && document.body) {
            const link2 = document.createElement('link');

            link2.href = 'static/assets/vendor/bootstrap/css/bootstrap.min.css';
            link2.rel = 'stylesheet';
            link2.id = 'link2Style1';
            document.body.appendChild(link2);
            const link = document.createElement('link');

            link.href = '/static/assets/css/style1.css';
            link.rel = 'stylesheet';
            link.id = 'linkStyle1';
            document.body.appendChild(link);

            const script = document.createElement('script');

            script.src = '/static/assets/js/main.js';
            script.id = 'mainJS';
            script.async = true;
            document.body.appendChild(script);
        }
        const ele = document.getElementById('preloader');

        if (ele) {

            setTimeout(() => {
                ele.style.opacity = '0';
                setTimeout(() => {
                    ele.style.display = 'none';
                }, 1000);
            }, 1000);
        }
        const { locationURL } = this.props;

        if (locationURL) {
            const urlwithout = getURLWithoutParams(locationURL);

            window.history.replaceState(
                                history.state,
                                (document && document.title) || '',
                                urlwithout);
        }

    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement|null}
    */
    render() {
        return (
<>
<div id = 'preloader' />
    <Header />
    <main id = 'main'>

        <section className = 'breadcrumbs'>
            <div className = 'container'>

                <div className = 'd-flex justify-content-between align-items-center'>
                    <h2>Disclaimer</h2>

                </div>

            </div>
        </section>

        <section className = 'inner-page'>
            <div className = 'container'>

                <div className = 'col-lg-12 col-md-12 col-sm-12 col-xs-12'>
                               While Meet Hour agrees to maintain certain security measures to protect User information, we do not take responsibility for the release of User information caused by hacking or other User's tampering with the www.shell.groupvi.systems Website.
                    <br />
                    <br />

Users assume the risk that any information submitted, however secure, could be illegally misappropriated by others.
                    <br />
                    <br />

Meet Hour agree to fully cooperate with law enforcement to protect and retrieve any information that may be illegally misappropriated by others.
                    <p
                        style = {{ float: 'left',
                            marginBottom: '24%' }}>&nbsp;</p>
                </div>


            </div>
        </section>

    </main>
    <Footer prejoin = {true}/>
</>
        );
    }
}

/**
 * Maps (parts of) the redux state to the React {@code Component} props of
 * {@code AbstractWelcomePage}.
 *
 * @param {Object} state - The redux state.
 * @protected
 * @returns {Props}
 */
export function mapStateToProps(state: Object) {
    const participants = state['features/base/participants'];
    const { userDetails } = state['features/base/mtApi'];
    const participant = participants.filter(i => i.id === 'local');
    const { locationURL } = state['features/base/connection'];


    return {
        _enableInsecureRoomNameWarning: state['features/base/config'].enableInsecureRoomNameWarning || false,
        _moderatedRoomServiceUrl: state['features/base/config'].moderatedRoomServiceUrl,
        _room: state['features/base/conference'].room,
        _settings: state['features/base/settings'],
        _localParticipant: participant,
        userDetails,
        locationURL
    };
}

const mapDispatchToProps = {
    _fetchCustomBrandingData: fetchCustomBrandingData
};

export default connect(mapStateToProps, mapDispatchToProps)(disclaimer);
