// @flow
/* eslint-disable */

import React, { PureComponent } from "react";
import {
    TouchableOpacity,
    View,
    Platform,
    Dimensions,
    Animated,
    ScrollView,
    Text,
} from "react-native";
import { ColorSchemeRegistry } from "../../../base/color-scheme";
import { hideDialog, isDialogOpen } from "../../../base/dialog";
import {
    Icon,
    IconDragHandle,
    Icon_Slide_Active,
    Icon_Slide_Inactive,
} from "../../../base/icons";
import { isLocalParticipantModerator } from "../../../base/participants";
import { connect } from "../../../base/redux";
import { StyleType } from "../../../base/styles";
import { isLocalVideoTrackDesktop } from "../../../base/tracks";
import {
    ClickandPledgeShareButton,
    ClickandPledgeRemoveButton,
} from "../../../clickandpledge-connect";
import { DonateShareButton, DonateRemoveButton } from "../../../donorbox";
import { SharedDocumentButton } from "../../../etherpad";
import { GenericIFrameButton } from "../../../genericiframe";
import { InviteButton, InviteViaCalendar } from "../../../invite";
import { AudioRouteButton } from "../../../mobile/audio-mode";
import { LiveStreamButton, RecordButton } from "../../../recording";
import { SharedVideoButton } from "../../../shared-video/components";
import ClosedCaptionButton from "../../../subtitles/components/native/ClosedCaptionButton.native";
import VoiceCommandButton from "./VoiceCommandButton";
import { TileViewButton } from "../../../video-layout";
import HelpButton from "../HelpButton";
import MuteEveryoneButton from "../MuteEveryoneButton";
import { BottomSheet } from "../../../recording";
import ReactionMenu from "../../../reactions/components/native/ReactionMenu";
import AudioOnlyButton from "./AudioOnlyButton";
import ReactionsButton from "./ReactionsButton";
import ParticipantPaneButton from "./ParticipantPaneButton";
import EtherpadIosButton from "./EtherpadIosButton";
import MoreOptionsButton from "./MoreOptionsButton";
import RaiseHandButton from "./RaiseHandButton";
import ScreenSharingButton from "./ScreenSharingButton.js";
import ToggleCameraButton from "./ToggleCameraButton";
import WhiteBoardIosButton from "./WhiteBoardIosButton";
import styles from "./styles";
import RNAnimatedScrollIndicators from "react-native-animated-scroll-indicators";
import SwipeGesture from "./SwipeGesture";
import { PictureInPictureButton } from "../../../mobile/picture-in-picture";
import _ from "lodash";
import { shouldDisplayReactionsButtons } from "../../../reactions/functions.any.js";
import OpenCarmodeButton from "./OpenCarmodeButton";
import OpenLanguageModalButton from "./OpenLanguageModalButton";

/**
 * The type of the React {@code Component} props of {@link OverflowMenu}.
 */
type Props = {
    /**
     * The color-schemed stylesheet of the dialog feature.
     */
    _bottomSheetStyles: StyleType,

    /**
     * True if the overflow menu is currently visible, false otherwise.
     */
    _isOpen: boolean,

    /**
     * Whether the recoding button should be enabled or not.
     */
    _recordingEnabled: boolean,

    /**
     * Used for hiding the dialog when the selection was completed.
     */
    dispatch: Function,

    /**
     * Url Recieved or not.
     */
    _getUrl: string,

    /**
     * Clickandpledge received url
     */
    _getCUrl: string,

    /*
     *Is the person Moderator or not.
     */
    _isModerator: boolean,
    donorBoxVisible: boolean,
    clicknPledgeVisible: boolean,
    _recStreamVisible: boolean,
    _liveStreamVisible: boolean,

    /**
     * Share Status
     */
    _sharingWhiteBoard: boolean,
    _shareEtherpad: boolean,
    _shareVideoState: boolean,
    _partId: ?boolean,
    _etherpadPart: ?boolean,
    _isWhiteBoardID: ?string,
    _isetherPadId: ?string,
    _whiteBoardvisible: ?boolean,
    _localId: ?string,
    _isOpenP: ?boolean,
    _livePadVisible: ?boolean,
    _screensharing: ?boolean,
    _screenShareVisible: ?boolean,
    _showReactions?: boolean,
};

type State = {
    /**
     * True if the bottom scheet is scrolled to the top.
     */
    scrolledToTop: boolean,

    /**
     * True if the 'more' button set needas to be rendered.
     */
    showMore: boolean,
};

/**
 * The exported React {@code Component}. We need it to execute
 * {@link hideDialog}.
 *
 * XXX It does not break our coding style rule to not utilize globals for state,
 * because it is merely another name for {@code export}'s {@code default}.
 */
let OverflowMenu_; // eslint-disable-line prefer-const
const { width } = Dimensions.get("window");
/**
 * Implements a React {@code Component} with some extra actions in addition to
 * those in the toolbar.
 */
class OverflowMenu extends PureComponent<Props, State> {
    scrollX = new Animated.Value(0);
    /**
     * Initializes a new {@code OverflowMenu} instance.
     *
     * @inheritdoc
     */
    constructor(props: Props) {
        super(props);

        this.state = {
            scrolledToTop: true,
            showMore: false,
            swipType: null,
            onPage: 0,
        };

        // Bind event handlers so they are only bound once per instance.
        this._onCancel = this._onCancel.bind(this);
        this._onSwipe = this._onSwipe.bind(this);
        this._onToggleMenu = this._onToggleMenu.bind(this);
        this._renderMenuExpandToggle = this._renderMenuExpandToggle.bind(this);
        this._onSwipePerformed = this._onSwipePerformed.bind(this);
        this._renderReactionMenu = this._renderReactionMenu.bind(this);
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const {
            _bottomSheetStyles,
            _getUrl,
            _getCUrl,
            _isModerator,
            donorBoxVisible,
            clicknPledgeVisible,
            _screensharing,
            _partId,
            _etherpadPart,
            _isWhiteBoardID,
            _isetherPadId,
            _shareVideoState,
            _screenShareVisible,
            _shouldDisplayReactionsButtons,
            _showReactions
        } = this.props;
        const { showMore } = this.state;
        const styleLabel = {
            ..._bottomSheetStyles.buttons.labelStyle,
            color: "rgb(227, 227, 227)",
        };
        const buttonProps = {
            overflowLabel: true,
            afterClick: this._onCancel,
            showLabel: true,
            styles: {
                ..._bottomSheetStyles.buttons,
                labelStyle: { ...styleLabel },
                underlayColor: "#0000",
            },
        };

        // const topButtonProps = {
        //     afterClick: this._onCancel,
        //     dispatch: this.props.dispatch,
        //     showLabel: true,
        //     styles: {
        //         ..._bottomSheetStyles.buttons,
        //         style: {
        //             ..._bottomSheetStyles.buttons.style,
        //             borderTopLeftRadius: 16,
        //             borderTopRightRadius: 16
        //         }
        //     }
        // };

        const reactionButtonProps = {
            overflowLabel: true,
            showLabel: true,
            styles: {
                ..._bottomSheetStyles.buttons,
                labelStyle: { ...styleLabel },
                underlayColor: "#0000",
            },
        };

        const moreOptionsButtonProps = {
            ...buttonProps,
            afterClick: this._onToggleMenu,
            visible: !showMore,
        };

        let donate;
        let Cdonate;

        if (donorBoxVisible && _getUrl === undefined && _isModerator) {
            donate = <DonateShareButton {...buttonProps} />;
        }
        if (donorBoxVisible && _getUrl && _isModerator) {
            donate = <DonateRemoveButton {...buttonProps} />;
        }

        if (clicknPledgeVisible && _getCUrl === undefined && _isModerator) {
            Cdonate = <ClickandPledgeShareButton {...buttonProps} />;
        }
        if (clicknPledgeVisible && _getCUrl && _isModerator) {
            Cdonate = <ClickandPledgeRemoveButton {...buttonProps} />;
        }
        let whiteBrd;
        let etherPad;

        if (Platform.OS === "ios") {
            whiteBrd = <WhiteBoardIosButton {...buttonProps} />;
        } else {
            whiteBrd = <GenericIFrameButton {...buttonProps} />;
        }

        if (Platform.OS === "ios") {
            etherPad = <EtherpadIosButton {...buttonProps} />;
        } else {
            etherPad = <SharedDocumentButton {...buttonProps} />;
        }

        return (
            <BottomSheet
                height={350}
                sheetBackgroundColor="rgba(28, 43, 65,1)"
                onCancel={this._onCancel}
                onSwipe={this._onSwipe}
                renderHeader={this._renderMenuExpandToggle}
                // renderFooter = { _shouldDisplayReactionsButtons
                // ? this._renderReactionMenu
                // : undefined }
                // renderFooter={this._renderReactionMenu}
            >
                <SwipeGesture onSwipePerformed={this._onSwipePerformed}>
                    <ScrollView
                        style={{
                            height:_showReactions ? 230 : 'auto',
                            marginBottom: 15,
                        }}
                    >
                        <View style={styles.footerLogoApp}>
                            <Text style={styles.footerLogoApp}>
                                © Meet Hour LLC
                            </Text>
                        </View>

                        <View
                            style={{
                                width: "100%",
                                paddingLeft: 20,
                                paddingRight: 20,
                                marginBottom: 20,
                                display: "flex",
                                flex: 1,
                                flexWrap: "wrap",
                                flexDirection: "row",
                                justifyContent: "space-between",
                            }}
                        >
                            {Boolean(!_isWhiteBoardID && !_isetherPadId) &&
                                !_shareVideoState &&
                                _screenShareVisible && (
                                    <ScreenSharingButton {...buttonProps} />
                                )}
                            <InviteButton {...buttonProps} />
                            <InviteViaCalendar {...buttonProps} />
                            <RaiseHandButton {...buttonProps} />
                            <ClosedCaptionButton {...buttonProps} />
                            {this.props._recStreamVisible && (
                                <RecordButton {...buttonProps} />
                            )}
                            {this.props._liveStreamVisible && (
                                <LiveStreamButton {...buttonProps} />
                            )}
                            {Boolean(!_isWhiteBoardID && !_isetherPadId) &&
                                !_screensharing &&
                                _isModerator && (
                                    <SharedVideoButton {...buttonProps} />
                                )}
                            {/* <ToggleCameraButton { ...buttonProps } /> */}
                            {_isModerator &&
                                Boolean(
                                    (!_isWhiteBoardID && !_isetherPadId) ||
                                        _etherpadPart ||
                                        _partId
                                ) &&
                                whiteBrd}

                            {_isModerator &&
                                Boolean(
                                    (!_isWhiteBoardID && !_isetherPadId) ||
                                        _etherpadPart ||
                                        _partId
                                ) &&
                                etherPad}
                            <TileViewButton {...buttonProps} />
                            <OpenCarmodeButton { ...buttonProps } />
                            <AudioOnlyButton {...buttonProps} />

                            {Cdonate}
                            {donate}
                            <AudioRouteButton {...buttonProps} />
                            <PictureInPictureButton {...buttonProps} />
                            <ReactionsButton {...reactionButtonProps}  />
                            <OpenLanguageModalButton {...buttonProps} />
                            <ParticipantPaneButton {...buttonProps} />

                        </View>
                    </ScrollView>
                </SwipeGesture>
                <View style={{marginBottom: 10}}>
                    {_showReactions && this._renderReactionMenu()}
                </View>
            </BottomSheet>
        );
    }

    _renderMenuExpandToggle: () => React$Element<any>;

    /**
     * Function to render the menu toggle in the bottom sheet header area.
     *
     * @returns {React$Element}
     */
    _renderMenuExpandToggle() {
        return (
            <View
                style={[
                    this.props._bottomSheetStyles.sheet,
                    styles.expandMenuContainer,
                ]}
            >
                <TouchableOpacity onPress={this._onToggleMenu}>
                    {/* $FlowFixMe */}
                    <IconDragHandle
                        fill={
                            this.props._bottomSheetStyles.buttons.iconStyle
                                .color
                        }
                    />
                </TouchableOpacity>
            </View>
        );
    }

    _onCancel: () => boolean;

    /**
     * Hides this {@code OverflowMenu}.
     *
     * @private
     * @returns {boolean}
     */
    _onCancel() {
        if (this.props._isOpen) {
            this.props.dispatch(hideDialog(OverflowMenu_));

            return true;
        }

        return false;
    }

    /**
     * Function to render the reaction menu as the footer of the bottom sheet.
     *
     * @returns {React$Element}
     */
    _renderReactionMenu() {
        return <ReactionMenu onCancel={this._onCancel} overflowMenu={true} />;
    }

    /**
     * Hides this {@code OverflowMenu}.
     *
     * @private
     * @returns {string}
     */
    _onSwipePerformed(action) {
        /// action : 'left' for left swipe
        /// action : 'right' for right swipe
        /// action : 'up' for up swipe
        /// action : 'down' for down swipe

        switch (action) {
            case "left": {
                this.setState((prevState) => ({
                    ...prevState,
                    swipType: "left",
                    onPage:
                        prevState.onPage >= 1
                            ? prevState.onPage
                            : prevState.onPage + 1,
                }));
                return "left";
                break;
            }
            case "right": {
                this.setState((prevState) => ({
                    ...prevState,
                    swipType: "right",
                    onPage:
                        prevState.onPage >= 1
                            ? prevState.onPage - 1
                            : prevState.onPage,
                }));
                return "right";
                break;
            }
            case "up": {
                this.setState((prevState) => ({
                    ...prevState,
                    swipType: "up",
                }));
                break;
            }
            case "down": {
                this.setState(
                    (prevState) => ({
                        ...prevState,
                        swipType: "down",
                    }),
                    () => {
                        if (this.state.swipType === "down") {
                            this._onCancel();
                        }
                    }
                );
                return "down";
                break;
            }
            default: {
                console.log("Undeteceted action");
            }
        }
    }

    _onSwipe: (string) => void;

    /**
     * Callback to be invoked when swipe gesture is detected on the menu. Returns true
     * if the swipe gesture is handled by the menu, false otherwise.
     *
     * @param {string} direction - Direction of 'up' or 'down'.
     * @returns {boolean}
     */
    _onSwipe(direction) {
        const { showMore } = this.state;

        switch (direction) {
            case "up":
                !showMore &&
                    this.setState({
                        showMore: true,
                    });

                return !showMore;
            case "down":
                showMore &&
                    this.setState({
                        showMore: false,
                    });

                return showMore;
        }
    }

    _onToggleMenu: () => void;

    /**
     * Callback to be invoked when the expand menu button is pressed.
     *
     * @returns {void}
     */
    _onToggleMenu() {
        this._onCancel();
    }
}

/**
 * Function that maps parts of Redux state tree into component props.
 *
 * @param {Object} state - Redux state.
 * @private
 * @returns {Props}
 */
function _mapStateToProps(state) {
    const { getUrl } = state["features/donorbox"];
    const { getCUrl } = state["features/clickandpledge"];
    const isPersonModerator = isLocalParticipantModerator(state);
    const { confSettings } = state["features/base/jwt"];
    const donorBoxVisible = confSettings
        ? confSettings?.DONOR_BOX === 1 && isPersonModerator
        : false;
    const clicknPledgeVisible = confSettings
        ? confSettings?.CP_CONNECT === 1 && isPersonModerator
        : false;
    const sharingWhiteBoard = state["features/genericiframe"].visible;
    const { editing: shareEtherpad, partId: participantID } =
        state["features/etherpad"];
    const sharedVideoStatus = state["features/shared-video"].status;
    const shareVideoState = ["playing", "pause", "start"].includes(
        sharedVideoStatus
    );
    const participants = state["features/base/participants"];
    const partTrue =
        participants.filter((i) => i.local === true)[0]?.id === participantID;
    const { partId } = state["features/genericiframe"];
    const participantLocal =
        participants.filter((i) => i.local === true)[0]?.id === partId;
    const localId = participants.filter((i) => i.local === true)[0];
    const livePadVisible = confSettings
        ? confSettings?.LIVEPAD === 1 && isPersonModerator
        : false;
    const whiteBoardvisible = confSettings
        ? confSettings?.WHITE_BOARD === 1 && isPersonModerator
        : false;
    const recSettings = confSettings
        ? isPersonModerator && confSettings?.ENABLE_RECORDING === 1
        : false;
    const livSettings = confSettings
        ? confSettings?.ENABLE_LIVESTREAM === 1
        : false;
    const screenShareVisible =
        !_.isNil(confSettings) &&
        Boolean(
            isPersonModerator ||
                !(confSettings?.DISABLE_SCREEN_SHARING_FOR_GUEST === 1)
        );
    const { liveStreamingEnabled } = state["features/base/config"];
    const { fileRecordingsEnabled } = state["features/base/config"];
    const { showReactions } = state["features/toolbox"];

    const liveStreamVisible =
        liveStreamingEnabled && livSettings && isPersonModerator;
    const recStreamVisible =
        fileRecordingsEnabled && recSettings && isPersonModerator;
    return {
        _bottomSheetStyles: ColorSchemeRegistry.get(state, "BottomSheet"),
        _isOpen: isDialogOpen(state, OverflowMenu_),
        _shouldDisplayReactionsButtons: shouldDisplayReactionsButtons(state),
        _getUrl: getUrl,
        _getCUrl: getCUrl,
        _isModerator: isPersonModerator,
        donorBoxVisible,
        clicknPledgeVisible,
        _sharingWhiteBoard: sharingWhiteBoard,
        _shareEtherpad: shareEtherpad,
        _shareVideoState: shareVideoState,
        _etherpadPart: partTrue,
        _isWhiteBoardID: partId,
        _isetherPadId: participantID,
        _whiteBoardvisible: whiteBoardvisible,
        _livePadVisible: livePadVisible,
        _localId: localId,
        _partId: participantLocal,
        _liveStreamVisible: liveStreamVisible,
        _recStreamVisible: recStreamVisible,
        _screensharing: isLocalVideoTrackDesktop(state),
        _screenShareVisible: screenShareVisible,
        _showReactions : showReactions
    };
}

OverflowMenu_ = connect(_mapStateToProps)(OverflowMenu);

export default OverflowMenu_;
