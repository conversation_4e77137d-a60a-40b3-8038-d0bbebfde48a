/* eslint-disable react/jsx-handler-names */
// @flow

import { BrowserDetection } from '@jitsi/js-utils';
import _ from 'lodash';
import React, { Component } from 'react';
import { batch } from 'react-redux';

import { SAFARI } from '../../../../../browser';
import {
    ACTION_SHORTCUT_TRIGGERED,
    createShortcutEvent,
    createToolbarEvent,
    sendAnalytics
} from '../../../analytics';
import { getToolbarButtons } from '../../../base/config';
import { openDialog, toggleDialog } from '../../../base/dialog';
import { isMobileBrowser } from '../../../base/environment/utils';
import { translate } from '../../../base/i18n';
import {
    IconArrowUp,
    IconChat,
    IconClickPledge,
    IconCodeBlock,
    IconDonate,
    IconExitFullScreen,
    IconFeedback,
    IconFullScreen,
    IconInviteMore,
    IconOpenInNew,
    IconParticipants,
    IconPresentation,
    IconRaisedHand,
    IconRec,
    IconShareDesktop,
    IconStartScreenShare,
    IconVoiceCommand,
    IconZoomIn,
    IconZoomOut
} from '../../../base/icons';
import MeetHourJS, { MHRecordingConstants } from '../../../base/lib-meet-hour';
import { MEDIA_TYPE } from '../../../base/media';
import {
    getLocalParticipant,
    getParticipants,
    isLocalParticipantModerator,
    participantUpdated
} from '../../../base/participants';
import { connect, equals } from '../../../base/redux';
import {
    OverflowMenuItem,
    ToolboxButtonWithIcon
} from '../../../base/toolbox/components';
import {
    getLocalVideoTrack,
    isLocalCameraTrackMuted,
    isLocalTrackMuted,
    toggleScreensharing
} from '../../../base/tracks';
import { getBackendSafeRoomName } from '../../../base/util';
import { isVpaasMeeting } from '../../../billing-counter/functions';
import { CHAT_SIZE, ChatCounter, toggleChat } from '../../../chat';
import {
    addClickandPledgeurl,
    clickAndPledge,
    clickPledgeDialog
} from '../../../clickandpledge-connect';
import { addDonorurl, donorboxDialog, toggleDonorbox } from '../../../donorbox';
import { EmbedMeetingDialog } from '../../../embed-meeting';
import { SharedDocumentButton, toggleDocumentedit } from '../../../etherpad';
import { openFeedbackDialog } from '../../../feedback';
import { GenericIFrameButton } from '../../../genericiframe';
import { setGifMenuVisibility } from '../../../gifs/actions';
import { isGifEnabled } from '../../../gifs/function.any';
import { beginAddPeople } from '../../../invite';
import { openKeyboardShortcutsDialog } from '../../../keyboard-shortcuts';
import {
    LocalRecordingButton,
    LocalRecordingInfoDialog
} from '../../../local-recording';
import {
    close as closeParticipantsPane,
    open as openParticipantsPane
} from '../../../participants-pane/actions';
import { getParticipantsPaneOpen } from '../../../participants-pane/functions';
import { addReactionToBuffer } from '../../../reactions/actions.any';
import { toggleReactionsMenuVisibility } from '../../../reactions/actions.web';
import ReactionsContainerButton from '../../../reactions/components/web/ReactionsContainerButtons';
import { REACTIONS } from '../../../reactions/constants';
import {
    isReactionsEnabled,
    shouldDisplayReactionsButtons
} from '../../../reactions/functions.any';
import {
    LiveStreamButton,
    RecordButton,
    StartLiveStreamDialog,
    StartRecordingDialog,
    StopLiveStreamDialog,
    StopRecordingDialog,
    getActiveSession
} from '../../../recording';
import { SecurityDialogButton } from '../../../security';
import {
    SETTINGS_TABS,
    SettingsButton,
    openSettingsDialog
} from '../../../settings';
import {
    showSharedVideoDialog,
    toggleSharedVideo
} from '../../../shared-video/actions.web';
import SharedVideoButton from '../../../shared-video/components/web/SharedVideoButton';
import { SpeakerStats } from '../../../speaker-stats';
import { closeCaptionsModal } from '../../../subtitles/actions.web';
import ClosedCaptionButton from '../../../subtitles/components/web/ClosedCaptionButton.web';
import {
    TileViewButton,
    setTileView,
    shouldDisplayTileView,
    toggleTileView
} from '../../../video-layout';
import {
    OverflowMenuVideoQualityItem,
    VideoQualityDialog
} from '../../../video-quality';
import { VideoBackgroundButton } from '../../../virtual-background';
import { VirtualBackgroundDialog } from '../../../virtual-background/components/web';
import { checkBlurSupport } from '../../../virtual-background/functions';
import { VoiceCommandDialog } from '../../../voice-command/components/web/VoiceCommandDialog';
import {
    setFullScreen,
    setOverflowMenuVisible,
    setToolbarHovered
} from '../../actions';
import {
    sendWhiteBoardInstance,
    setShareMenuVisible,
    toogleEtherPad
} from '../../actions.web';
import {
    getToolbarAdditionalButtons,
    isToolboxVisible,
    togglezoom,
    togglezoomout
} from '../../functions';
import DownloadButton from '../DownloadButton';
import HelpButton from '../HelpButton';

import AudioSettingsButton from './AudioSettingsButton';
import HangupSettings from './HangupSettings';
import MuteEveryoneButton from './MuteEveryoneButton';
import OverflowMenuButton from './OverflowMenuButton';
import OverflowMenuProfileItem from './OverflowMenuProfileItem';
import ScreenOverflowButton from './ScreenOverflowButton';
import ToolbarButton from './ToolbarButton';
import VideoSettingsButton from './VideoSettingsButton';

/**
 * The type of the React {@code Component} props of {@link Toolbox}.
 */
type Props = {

  /**
   * Whether or not the chat feature is currently displayed.
   */
  _chatOpen: boolean,

  /**
   * Click and pledge token.
   */
  _clicknPledgeVisible: boolean,

  /**
   * The {@code MHConference} for the current conference.
   */
  _conference: Object,

  /**
   * The tooltip key to use when screensharing is disabled. Or undefined
   * if non to be shown and the button to be hidden.
   */
  _desktopSharingDisabledTooltipKey: boolean,

  /**
   * Whether or not screensharing is initialized.
   */
  _desktopSharingEnabled: boolean,

  /**
   * Whether or not a dialog is displayed.
   */
  _dialog: boolean,

  /**
   * Donor box visibility in toolbox through jwt token.
   */
  _donorBoxVisible: boolean,

  /**
   * Display embed Meeting url button.
   */
  _embedMeeting: boolean,

  /**
   * Whether or not call feedback can be sent.
   */
  _feedbackConfigured: boolean,

  /**
   * Whether or not the app is currently in full screen.
   */
  _fullScreen: boolean,

  /**
   * Whether or not the tile view is enabled.
   */
  _tileViewEnabled: boolean,

  /**
   * Whether or not the current user is logged in through a JWT.
   */
  _isGuest: boolean,

  /**
   * Whether or not the current meeting belongs to a JaaS user.
   */
  _isVpaasMeeting: boolean,

  /**
   * The ID of the local participant.
   */
  _localParticipantID: String,

  /**
   * The subsection of Redux state for local recording.
   */
  _localRecState: Object,

  /**
   * The value for how the conference is locked (or undefined if not locked)
   * as defined by room-lock constants.
   */
  _locked: boolean,

  /**
   * Use the Button to Zoom in the video.
   */
  _clickbutton: number,

  /**
   * ConfSettings in the conference settings.
   */
  confSettings: Object,

  /**
   * Whether or not the overflow menu is visible.
   */
  _overflowMenuVisible: boolean,

  /**
   * Whether or not the participants pane is open.
   */
  _participantsPaneOpen: boolean,

  /**
   * Whether or not the local participant's hand is raised.
   */
  _raisedHand: boolean,

  /**
   * Whether or not the local participant is screensharing.
   */
  _screensharing: boolean,

  /**
   * _ShareMenuVisibility.
   */
  _ShareMenuVisibility: boolean,

  /**
   * Whether or not the participants pane is open.
   */
  _participantsPaneOpen: boolean,

  /**
   * LiveStream Visible or not.
   */
  liveStreamVisible: boolean,

  /**
   * Room name.
   */
  _roomName: string,

  /**
   * Whether or not the local participant is sharing a YouTube video.
   */
  _sharingVideo: boolean,

  /**
   * Flag showing whether toolbar is visible.
   */
  _visible: boolean,

  /**
   * Set with the buttons which this Toolbox should display.
   */
  _visibleButtons: Set<string>,

  /**
   * Invoked to active other features of the app.
   */
  dispatch: Function,

  /**
   * Invoked to obtain translated strings.
   */
  t: Function,

  /**
   * Get Url for Donorbox.
   */
  _geturl: string,

  /**
   * Get Url for click and pledge .
   */
  _getCUrl: string,

  /**
   * Add Url for click and Pledge.
   */
  _addCUrl: string,

  /**
   * Add Url for Donorbox.
   */
  _addurl: string,

  /**
   * Recording is Visible.
   */
  RecordingVisible: boolean,

  /**
   * Is Moderator.
   */
  _isModerator: Boolean,

  /**
   * Detects Mobile Browser.
   */
  _isMobile: boolean,

  /**
   * Width of the Clients Browser.
   */
  _clientWidth: number,

  _videoMuted: boolean,

  /**
   * Share Status.
   */
  _sharingWhiteBoard: boolean,
  _shareEtherpad: boolean,
  _shareVideoState: boolean,
  _partId: ?boolean,
  _etherpadPart: ?boolean,
  _isWhiteBoardID: ?string,
  _isetherPadId: ?string,
  _whiteBoardvisible: ?boolean,
  _localId: ?string,
  _isOpenP: ?boolean,
  _livePadVisible: ?boolean,
  _vcString: ?string,
  _isLiveStreamRunning: ?boolean,
  iframeVisible: ?boolean,
  onEdit: ?boolean,
  _isRecordingRunning: ?boolean,
  vcVisible: ?boolean,
  _screenShareVisible: ?boolean,

  /**
   * Voice command hide.
   */
  _isVoiceCommandDisabled: ?boolean,

  /*
   * isGIF Enabled
   */
  _isGifEnabled: boolean,

  /**
   * ShouldDisplayReactionsButtons.
   */
  _shouldDisplayReactionsButtons: boolean,

  /**
   * Is Reactions Enabled.
   */
  _reactionsEnabled: boolean,
};

/**
 * The type of the React {@code Component} state of {@link Toolbox}.
 */
type State = {

  /**
   * The width of the browser's window.
   */
  windowWidth: number,

  /**
   * The url of the donorbox.
   */
  url: any,
  tileViewEnable: boolean,
};

declare var APP: Object;
declare var interfaceConfig: Object;

// XXX: We are not currently using state here, but in the future, when
// interfaceConfig is part of redux we will. This will have to be retrieved from the store.
const visibleButtons = new Set(interfaceConfig.TOOLBAR_BUTTONS);

/**
 * Implements the conference toolbox on React/Web.
 *
 * @augments Component
 * @param {string} e - The first number.
 * @returns {Function} For function.
 */
class Toolbox extends Component<Props, State> {
    /**
   * Initializes a new {@code Toolbox} instance.
   *
   * @param {Props} props - The read-only React {@code Component} props with
   * which the new instance is to be initialized.
   */
    constructor(props: Props) {
        super(props);

        // Bind event handlers so they are only bound once per instance.
        this._onMouseOut = this._onMouseOut.bind(this);
        this._onMouseOver = this._onMouseOver.bind(this);
        this._onResize = this._onResize.bind(this);
        this._onSetOverflowVisible = this._onSetOverflowVisible.bind(this);
        this._onSetShareScreenOverflowVisible
      = this._onSetShareScreenOverflowVisible.bind(this);
        this._onToggleWhiteBoard = this._onToggleWhiteBoard.bind(this);

        this._onShortcutToggleChat = this._onShortcutToggleChat.bind(this);
        this._onShortcutToggleFullScreen
      = this._onShortcutToggleFullScreen.bind(this);
        this._onShortcutToggleParticipantsPane
      = this._onShortcutToggleParticipantsPane.bind(this);
        this._onShortcutToggleRaiseHand
      = this._onShortcutToggleRaiseHand.bind(this);
        this._onShortcutToggleScreenshare
      = this._onShortcutToggleScreenshare.bind(this);
        this._onShortcutToggleVideoQuality
      = this._onShortcutToggleVideoQuality.bind(this);
        this._onToolbarOpenFeedback = this._onToolbarOpenFeedback.bind(this);
        this._onToolbarOpenInvite = this._onToolbarOpenInvite.bind(this);
        this._onToolbarOpenKeyboardShortcuts
      = this._onToolbarOpenKeyboardShortcuts.bind(this);
        this._onToolbarToggleParticipantsPane
      = this._onToolbarToggleParticipantsPane.bind(this);
        this._onToolbarOpenSpeakerStats
      = this._onToolbarOpenSpeakerStats.bind(this);
        this._onToolbarOpenEmbedMeeting
      = this._onToolbarOpenEmbedMeeting.bind(this);
        this._onToolbarOpenVideoQuality
      = this._onToolbarOpenVideoQuality.bind(this);
        this._onToolbarToggleChat = this._onToolbarToggleChat.bind(this);
        this._onToolbarToggleFullScreen
      = this._onToolbarToggleFullScreen.bind(this);
        this._onToolbarToggleProfile = this._onToolbarToggleProfile.bind(this);
        this._onToolbarToggleRaiseHand = this._onToolbarToggleRaiseHand.bind(this);
        this._onToolbarToggleScreenshare
      = this._onToolbarToggleScreenshare.bind(this);
        this._onToolbarToggleDonorbox = this._onToolbarToggleDonorbox.bind(this);
        this._onToolbarToggleclickandPledge
      = this._onToolbarToggleclickandPledge.bind(this);
        this._onToolbarToggleRemoveDonorbox
      = this._onToolbarToggleRemoveDonorbox.bind(this);
        this._onToolbarToggleVoiceCOmmandsDialog
      = this._onToolbarToggleVoiceCOmmandsDialog.bind(this);
        this._onToolbarToggleRemoveclickandPledge
      = this._onToolbarToggleRemoveclickandPledge.bind(this);
        this._onToolbarToggleSharedVideo
      = this._onToolbarToggleSharedVideo.bind(this);
        this._onToolbarOpenLocalRecordingInfoDialog
      = this._onToolbarOpenLocalRecordingInfoDialog.bind(this);
        this._onShortcutToggleTileView = this._onShortcutToggleTileView.bind(this);
        this._onZoomIn = this._onZoomIn.bind(this);
        this._onZoomOut = this._onZoomOut.bind(this);

        this.state = {
            windowWidth: (window.innerWidth * 70) / 100,
            url: this.props._geturl,
            tileViewEnable: false
        };
    }

    /**
   * Sets keyboard shortcuts for to trigger ToolbarButtons actions.
   *
   * @inheritdoc
   * @returns {void}
   */
    componentDidMount() {
        const KEYBOARD_SHORTCUTS = [
            this._shouldShowButton('videoquality') && {
                character: 'A',
                exec: this._onShortcutToggleVideoQuality,
                helpDescription: 'keyboardShortcuts.videoQuality'
            },
            this._shouldShowButton('chat') && {
                character: 'C',
                exec: this._onShortcutToggleChat,
                helpDescription: 'keyboardShortcuts.toggleChat'
            },
            this._shouldShowButton('desktop')
        && this.props._screenShareVisible && {
                character: 'D',
                exec: this._onShortcutToggleScreenshare,
                helpDescription: 'keyboardShortcuts.toggleScreensharing'
            },
            this._shouldShowButton('participants-pane') && {
                character: 'P',
                exec: this._onShortcutToggleParticipantsPane,
                helpDescription: 'keyboardShortcuts.toggleParticipantsPane'
            },
            this._shouldShowButton('raisehand') && {
                character: 'R',
                exec: this._onShortcutToggleRaiseHand,
                helpDescription: 'keyboardShortcuts.raiseHand'
            },
            this._shouldShowButton('fullscreen') && {
                character: 'S',
                exec: this._onShortcutToggleFullScreen,
                helpDescription: 'keyboardShortcuts.fullScreen'
            },
            this._shouldShowButton('tileview') && {
                character: 'W',
                exec: this._onShortcutToggleTileView,
                helpDescription: 'toolbar.tileViewToggle'
            }
        ];

        KEYBOARD_SHORTCUTS.forEach(shortcut => {
            if (typeof shortcut === 'object') {
                APP.keyboardshortcut.registerShortcut(
          shortcut.character,
          null,
          shortcut.exec,
          shortcut.helpDescription
                );
            }
        });

        if (
            this.props._reactionsEnabled
      && this.props._shouldDisplayReactionsButtons
        ) {
            const REACTION_SHORTCUTS = Object.keys(REACTIONS).map(key => {
                const onShortcutSendReaction = () => {
                    this.props.dispatch(addReactionToBuffer(key));
                    sendAnalytics(createShortcutEvent(`reaction.${key}`));
                };

                return {
                    character: REACTIONS[key].shortcutChar,
                    exec: onShortcutSendReaction,
                    helpDescription: `toolbar.reaction${key
            .charAt(0)
            .toUpperCase()}${key.slice(1)}`,
                    altKey: true
                };
            });

            REACTION_SHORTCUTS.forEach(shortcut => {
                APP.keyboardshortcut.registerShortcut(
          `:${shortcut.character}`,
          null,
          shortcut.exec,
          shortcut.helpDescription
                );
            });

            if (this.props._isGifEnabled) {
                const onGifShortcut = () => {
                    batch(() => {
                        this.props.dispatch(toggleReactionsMenuVisibility());
                        this.props.dispatch(setGifMenuVisibility(true));
                    });
                };

                APP.keyboardshortcut.registerShortcut({
                    character: 'G',
                    handler: onGifShortcut,
                    helpDescription: 'keyboardShortcuts.giphyMenu'
                });
            }
        }

        window.addEventListener('resize', this._onResize);

        this.props.dispatch(toggleDonorbox());

        if (document.body) {
            document.body.style.overflow = 'hidden';
        }
    }

    /**
   * Update the visibility of the {@code OverflowMenuButton}.
   *
   * @inheritdoc
   */
    componentDidUpdate(prevProps) {
    // Ensure the dialog is closed when the toolbox becomes hidden.
    // if (prevProps._overflowMenuVisible && !this.props._visible) {
    //     this._onSetOverflowVisible(false);
    // }

        // if (prevProps._overflowMenuVisible
        //     && !prevProps._dialog
        //     && this.props._dialog) {
        //     this._onSetOverflowVisible(false);
        //     this.props.dispatch(setToolbarHovered(false));
        // }

        if (this.props._chatOpen !== prevProps._chatOpen) {
            this._onResize();
        }

        if (prevProps._sharingWhiteBoard !== this.props._sharingWhiteBoard) {
            if (this.props._sharingWhiteBoard && this.props._tileViewEnabled) {
                // eslint-disable-next-line react/no-did-update-set-state
                this.setState({
                    tileViewEnable: true
                });
                this.props.dispatch(setTileView(false));
            } else if (
                this.state.tileViewEnable
        && !this.props._sharingWhiteBoard
        && !this.props._tileViewEnabled
            ) {
                this.props.dispatch(setTileView(!this.props._tileViewEnabled));
            }
        }

        if (this.props._isModerator !== prevProps._isModerator) {
            if (this.props.confSettings !== null) {
                if (
                    this.props.confSettings.ENABLE_LOBBY === 1
          && this.props._conference
                ) {
                    // this.props.dispatch(toggleLobbyMode(true));
                }
            }
        }

        if (
            prevProps._vcString !== this.props._vcString
      && this.props._vcString !== null
        ) {
            if (
                this.props._vcString === 'DonorBox'
        || this.props._vcString === 'RDonorBox'
            ) {
                if (this.props._geturl === undefined) {
                    this._doToggleDonorboxDialog();
                } else {
                    this._doRemoveDonorDialog();
                }
            }
            if (
                this.props._vcString === 'ScreenShare'
        || this.props._vcString === 'StopScreen'
            ) {
                this._doToggleScreenshare(true);
            }
            if (this.props._vcString === 'Livestream') {
                this.props.dispatch(
          openDialog(
            this.props._isLiveStreamRunning
                ? StopLiveStreamDialog
                : StartLiveStreamDialog
          )
                );
            }
            if (
                (this.props._vcString === 'WhiteBoard'
          || this.props._vcString === 'CWhiteBoard')
        && this.props._whiteBoardvisible
            ) {
                this.props.dispatch(sendWhiteBoardInstance(!this.props.iframeVisible));
                this.props.dispatch(setShareMenuVisible(false));
            }
            if (
                (this.props._vcString === 'LivePad'
          || this.props._vcString === 'CLivePad')
        && this.props._livePadVisible
            ) {
                this.props.dispatch(toogleEtherPad(!this.props._shareEtherpad));
                this.props.dispatch(toggleDocumentedit(!this.props.onEdit));
            }
            if (
                this.props._vcString === 'Youtube'
        || this.props._vcString === 'CYoutube'
            ) {
                if (this.props._shareVideoState === false) {
                    this.props.dispatch(
            showSharedVideoDialog(id => APP.UI.startSharedVideoEmitter(id))
                    );
                } else {
                    APP.UI.stopSharedVideoEmitter();
                }
            }
            if (
                this.props._vcString === 'clicknPledge'
        || this.props._vcString === 'RclicknPledge'
            ) {
                if (this.props._getCUrl === undefined) {
                    this._doToggleCLPDialog();
                } else {
                    this._doRemoveClpDialog();
                }
            }
            if (this.props._vcString === 'Recording') {
                this.props.dispatch(
          openDialog(
            this.props._isRecordingRunning
                ? StopRecordingDialog
                : StartRecordingDialog
          )
                );
            }
            if (this.props._vcString === 'ParticipantP') {
                this._onToolbarToggleParticipantsPane();
            }
            if (this.props._vcString === 'VcDialog') {
                this.props.dispatch(openDialog(VirtualBackgroundDialog));
            }
            if (this.props._vcString === 'ChatBox') {
                this._onToolbarToggleChat();
            }
            if (this.props._vcString === 'VideoQ') {
                this._onToolbarOpenVideoQuality();
            }
            if (this.props._vcString === 'SpeckerS') {
                this._onToolbarOpenSpeakerStats();
            }
            if (this.props._vcString === 'Settings') {
                const defaultTab = SETTINGS_TABS.DEVICES;

                this.props.dispatch(openSettingsDialog(defaultTab));
            }
            if (this.props._vcString === 'Fullscreen') {
                this._onToolbarToggleFullScreen();
            }
            if (this.props._vcString === 'InviteP') {
                this._onToolbarOpenInvite();
            }
            if (
                this.props._vcString === 'RaiseHand'
        || this.props._vcString === 'LowerHand'
            ) {
                this._doToggleRaiseHand();
            }
        }

    // if (this.props._geturl !== prevProps._geturl && this.props._geturl !== undefined) {
    //     this.props.dispatch(toggleDonorbox());
    // }
    }

    /**
   * Removes keyboard shortcuts registered by this component.
   *
   * @inheritdoc
   * @returns {void}
   */
    componentWillUnmount() {
        [ 'A', 'C', 'D', 'R', 'S' ].forEach(letter =>
            APP.keyboardshortcut.unregisterShortcut(letter)
        );

        window.removeEventListener('resize', this._onResize);
    }

    _onCaptionsArrowClick = () => {
        this.props.dispatch(closeCaptionsModal(false));
    };

    /**
   * Callback invoked to display Donorbox Modal {@code DonorBox}.
   *
   * @param {number} e - The first number.
   *
   * @private
   * @returns {void}
   */
    _onDonate = e => {
        e.preventDefault();
    // eslint-disable-next-line no-invalid-this
    // this.props.dispatch(getMeethourDonorboxEnabled());
    };

    _onOpenDialog = e => {
        e.preventDefault();
        // eslint-disable-next-line no-invalid-this
        this.props.dispatch(toggleDialog(clickPledgeDialog));

    // this.props.dispatch(toggleDialog())
    };

    /**
   * Implements React's {@link Component#render()}.
   *
   * @inheritdoc
   * @returns {ReactElement}
   */
    render() {
        const {
            _chatOpen,
            _visible,
            _visibleButtons,
            _geturl,
            _getCUrl,
            _ShareMenuVisibility,
            _overflowMenuVisible
        } = this.props;

        const rootClassNames = `new-toolbox ${
            _visible || Boolean(_ShareMenuVisibility || _overflowMenuVisible)
                ? 'visible'
                : ''
        } ${_visibleButtons.size ? '' : 'no-buttons'} ${
            _chatOpen ? 'shift-right' : ''
        }`;

        const buttonClassNames = `dbox-donation-button donor-box ${
            _visible && _geturl ? '' : 'not-visible'
        }`;

        const buttonClassNames1 = ` click-and-pledge-connect
        ${_visible && _getCUrl ? '' : 'not-visible-click'}`;

        return (
            <div
                className = { rootClassNames }
                id = 'new-toolbox'
                onMouseOut = { this._onMouseOut }
                onMouseOver = { this._onMouseOver }>
                <div className = 'toolbox-background' />
                <a
                    className = { buttonClassNames }
                    href = { _geturl }
                    onClick = { this._onDonate }>
          Donate
                </a>
                { _visible && _getCUrl && (
                    <a
                        className = { buttonClassNames1 }
                        onClick = { this._onOpenDialog }>
            Donate
                    </a>
                ) }
                { this._renderToolboxContent() }
            </div>
        );
    }

    /**
   * Callback invoked to display {@code FeedbackDialog}.
   *
   * @private
   * @returns {void}
   */
    _doOpenFeedback() {
        const { _conference } = this.props;

        this.props.dispatch(openFeedbackDialog(_conference));
    }

    /**
   * Callback invoked to display {@code FeedbackDialog}.
   *
   * @private
   * @returns {void}
   */
    _doOpenEmbedMeeting() {
        this.props.dispatch(openDialog(EmbedMeetingDialog));
    }

    /**
   * Dispatches an action to display {@code KeyboardShortcuts}.
   *
   * @private
   * @returns {void}
   */
    _doOpenKeyboardShorcuts() {
        this.props.dispatch(openKeyboardShortcutsDialog());
    }

    /**
   * Callback invoked to display {@code SpeakerStats}.
   *
   * @private
   * @returns {void}
   */
    _doOpenSpeakerStats() {
        this.props.dispatch(
      openDialog(SpeakerStats, {
          conference: this.props._conference
      })
        );
    }

    /**
   * Dispatches an action to open the video quality dialog.
   *
   * @private
   * @returns {void}
   */
    _doOpenVideoQuality() {
        this.props.dispatch(openDialog(VideoQualityDialog));
    }

    /**
   * Dispatches an action to toggle the display of chat.
   *
   * @private
   * @returns {void}
   */
    _doToggleChat() {
        this.props.dispatch(toggleChat());
    }

    /**
   * Dispatches an action to toggle screensharing.
   *
   * @private
   * @returns {void}
   */
    _doToggleFullScreen() {
        const fullScreen = !this.props._fullScreen;

        this.props.dispatch(setFullScreen(fullScreen));
    }

    /**
   * Dispatches an action to show or hide the profile edit panel.
   *
   * @private
   * @returns {void}
   */
    _doToggleProfile() {
        this.props.dispatch(openSettingsDialog(SETTINGS_TABS.PROFILE));
    }

    /**
   * Dispatches an action to toggle the local participant's raised hand state.
   *
   * @private
   * @returns {void}
   */
    _doToggleRaiseHand() {
        const { _localParticipantID, _raisedHand } = this.props;

        this.props.dispatch(
      participantUpdated({
          // XXX Only the local participant is allowed to update without
          // stating the MHConference instance (i.e. participant property
          // `conference` for a remote participant) because the local
          // participant is uniquely identified by the very fact that there is
          // only one local participant.

          id: _localParticipantID,
          local: true,
          raisedHand: !_raisedHand
      })
        );
    }

    _onShortcutToggleParticipantsPane: () => void;

    /**
   * Creates an analytics keyboard shortcut event and dispatches an action for
   * toggling the display of the participants pane.
   *
   * @private
   * @returns {void}
   */
    _onShortcutToggleParticipantsPane() {
        sendAnalytics(
      createShortcutEvent('toggle.participants-pane', {
          enable: !this.props._participantsPaneOpen
      })
        );

        this._onToolbarToggleParticipantsPane();
    }

    /**
   * Dispatches an action to toggle screensharing.
   *
   * @param {boolean} enabled - Desktop Sharing Enabled bool.
   * @private
   * @returns {void}
   */
    _doToggleScreenshare(enabled) {
        if (this.props._desktopSharingEnabled) {
            this.props.dispatch(toggleScreensharing(enabled));
        }
    }

    /**
   * Dispatches an action to toggle YouTube video sharing.
   *
   * @private
   * @returns {void}
   */
    _doToggleSharedVideo() {
        this.props.dispatch(toggleSharedVideo());
    }

    /**
   * Dispatches an action to toggle Donorbox Dialog box.
   *
   * @private
   * @returns {void}
   */
    _doToggleDonorboxDialog() {
        this.props.dispatch(toggleDialog(donorboxDialog));
    }

    /**
   * Dispatches an action to toggle Donorbox Dialog box.
   *
   * @private
   * @returns {void}
   */
    _doToggleCLPDialog() {
        this.props.dispatch(toggleDialog(clickAndPledge));
    }

    /**
   * Dispatches an action to toggle Donorbox Dialog box.
   *
   * @private
   * @returns {void}
   */
    _doRemoveDonorDialog() {
        this.props.dispatch(addDonorurl('remove'));
    }

    /**
   * Dispatches an action to toggle Voice Command Dialog box.
   *
   * @private
   * @returns {void}
   */
    _doOpenVoiceCommand() {
        this.props.dispatch(toggleDialog(VoiceCommandDialog));
    }

    /**
   * Dispatches an action to toggle Donorbox Dialog box.
   *
   * @private
   * @returns {void}
   */
    _doRemoveClpDialog() {
        this.props.dispatch(addClickandPledgeurl('remove'));
    }

    /**
   * Dispatches an action to toggle the video quality dialog.
   *
   * @private
   * @returns {void}
   */
    _doToggleVideoQuality() {
        this.props.dispatch(toggleDialog(VideoQualityDialog));
    }

    /**
   * Dispaches an action to toggle tile view.
   *
   * @private
   * @returns {void}
   */
    _doToggleTileView() {
        this.props.dispatch(toggleTileView());
    }

    _onMouseOut: () => void;

    /**
   * Dispatches an action signaling the toolbar is not being hovered.
   *
   * @private
   * @returns {void}
   */
    _onMouseOut() {
        this.props.dispatch(setToolbarHovered(false));
    }

    _onMouseOver: () => void;

    /**
   * Dispatches an action signaling the toolbar is being hovered.
   *
   * @private
   * @returns {void}
   */
    _onMouseOver() {
        this.props.dispatch(setToolbarHovered(true));
    }

    _onResize: () => void;

    /**
   * A window resize handler used to calculate the number of buttons we can
   * fit in the toolbar.
   *
   * @private
   * @returns {void}
   */
    _onResize() {
        let widthToUse = window.innerWidth;

        // Take chat size into account when resizing toolbox.
        if (this.props._chatOpen) {
            widthToUse -= CHAT_SIZE;
        }

        if (this.state.windowWidth !== widthToUse) {
            this.setState({ windowWidth: widthToUse });
        }
    }

    _onSetOverflowVisible: (boolean) => void;

    /**
   * Sets the visibility of the overflow menu.
   *
   * @param {boolean} visible - Whether or not the overflow menu should be
   * displayed.
   * @private
   * @returns {void}
   */
    _onSetOverflowVisible(visible) {
        this.props.dispatch(setOverflowMenuVisible(visible));
    }

    _onShortcutToggleChat: () => void;

    /**
   * Creates an analytics keyboard shortcut event and dispatches an action for
   * toggling the display of chat.
   *
   * @private
   * @returns {void}
   */
    _onShortcutToggleChat() {
        sendAnalytics(
      createShortcutEvent('toggle.chat', {
          enable: !this.props._chatOpen
      })
        );

        this._doToggleChat();
    }

    _onShortcutToggleVideoQuality: () => void;

    /**
   * Creates an analytics keyboard shortcut event and dispatches an action for
   * toggling the display of Video Quality.
   *
   * @private
   * @returns {void}
   */
    _onShortcutToggleVideoQuality() {
        sendAnalytics(createShortcutEvent('video.quality'));

        this._doToggleVideoQuality();
    }

    _onShortcutToggleTileView: () => void;

    /**
   * Dispatches an action for toggling the tile view.
   *
   * @private
   * @returns {void}
   */
    _onShortcutToggleTileView() {
        sendAnalytics(
      createShortcutEvent('toggle.tileview', {
          enable: !this.props._tileViewEnabled
      })
        );

        this._doToggleTileView();
    }

    _onShortcutToggleFullScreen: () => void;

    /**
   * Creates an analytics keyboard shortcut event and dispatches an action for
   * toggling full screen mode.
   *
   * @private
   * @returns {void}
   */
    _onShortcutToggleFullScreen() {
        sendAnalytics(
      createShortcutEvent('toggle.fullscreen', {
          enable: !this.props._fullScreen
      })
        );

        this._doToggleFullScreen();
    }

    _onSetShareScreenOverflowVisible: (boolean) => void;

    /**
   * Sets the visibility of the overflow menu.
   *
   * @param {boolean} visible - Whether or not the overflow menu should be
   * displayed.
   * @private
   * @returns {void}
   */
    _onSetShareScreenOverflowVisible(visible) {
        this.props.dispatch(setShareMenuVisible(visible));
    }

    _onShortcutToggleRaiseHand: () => void;

    /**
   * Creates an analytics keyboard shortcut event and dispatches an action for
   * toggling raise hand.
   *
   * @private
   * @returns {void}
   */
    _onShortcutToggleRaiseHand() {
        sendAnalytics(
      createShortcutEvent('toggle.raise.hand', ACTION_SHORTCUT_TRIGGERED, {
          enable: !this.props._raisedHand
      })
        );

        this._doToggleRaiseHand();
    }

    _onShortcutToggleScreenshare: () => void;

    /**
   * Creates an analytics keyboard shortcut event and dispatches an action for
   * toggling screensharing.
   *
   * @private
   * @returns {void}
   */
    _onShortcutToggleScreenshare() {
        const enable = !this.props._screensharing;

        sendAnalytics(
      createToolbarEvent('screen.sharing', {
          enable
      })
        );

        this._doToggleScreenshare(enable);
    }

    _onToolbarOpenFeedback: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for toggling
   * display of feedback.
   *
   * @private
   * @returns {void}
   */
    _onToolbarOpenFeedback() {
        sendAnalytics(createToolbarEvent('feedback'));

        this._doOpenFeedback();
    }

    _onToolbarOpenInvite: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for opening
   * the modal for inviting people directly into the conference.
   *
   * @private
   * @returns {void}
   */
    _onToolbarOpenInvite() {
        sendAnalytics(createToolbarEvent('invite'));
        this.props.dispatch(beginAddPeople());
    }

    _onToolbarOpenKeyboardShortcuts: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for opening
   * the modal for showing available keyboard shortcuts.
   *
   * @private
   * @returns {void}
   */
    _onToolbarOpenKeyboardShortcuts() {
        sendAnalytics(createToolbarEvent('shortcuts'));

        this._doOpenKeyboardShorcuts();
    }

    _onToolbarToggleParticipantsPane: () => void;

    /**
   * Dispatches an action for toggling the participants pane.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleParticipantsPane() {
        const { dispatch, _participantsPaneOpen } = this.props;

        if (_participantsPaneOpen) {
            dispatch(closeParticipantsPane());
        } else {
            dispatch(openParticipantsPane());
        }
    }

    _onToolbarOpenEmbedMeeting: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for opening
   * the embed meeting modal.
   *
   * @private
   * @returns {void}
   */
    _onToolbarOpenEmbedMeeting() {
        sendAnalytics(createToolbarEvent('embed.meeting'));

        this._doOpenEmbedMeeting();
    }

    _onToolbarOpenSpeakerStats: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for opening
   * the speaker stats modal.
   *
   * @private
   * @returns {void}
   */
    _onToolbarOpenSpeakerStats() {
        sendAnalytics(createToolbarEvent('speaker.stats'));

        this._doOpenSpeakerStats();
    }

    _onToolbarOpenVideoQuality: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for toggling
   * open the video quality dialog.
   *
   * @private
   * @returns {void}
   */
    _onToolbarOpenVideoQuality() {
        sendAnalytics(createToolbarEvent('video.quality'));

        this._doOpenVideoQuality();
    }

    _onToolbarToggleChat: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for toggling
   * the display of chat.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleChat() {
        sendAnalytics(
      createToolbarEvent('toggle.chat', {
          enable: !this.props._chatOpen
      })
        );

        this._doToggleChat();
    }

    _onToolbarToggleFullScreen: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for toggling
   * full screen mode.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleFullScreen() {
        sendAnalytics(
      createToolbarEvent('toggle.fullscreen', {
          enable: !this.props._fullScreen
      })
        );

        this._doToggleFullScreen();
    }

    _onToggleWhiteBoard: () => void;

    /**
   * Toggle White Board.
   *
   * @private
   * @returns {void}
   */
    _onToggleWhiteBoard() {
        this.props.dispatch(sendWhiteBoardInstance(true));

    //  window.open(
    //     `https://wbo.ophir.dev/boards/meethour-${_roomName}`, '_blank');
    }

    _onToolbarToggleProfile: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for showing
   * or hiding the profile edit panel.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleProfile() {
        sendAnalytics(createToolbarEvent('profile'));

        this._doToggleProfile();
    }

    _onToolbarToggleRaiseHand: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for toggling
   * raise hand.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleRaiseHand() {
        sendAnalytics(
      createToolbarEvent('raise.hand', { enable: !this.props._raisedHand })
        );

        this._doToggleRaiseHand();
    }

    _onToolbarToggleScreenshare: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for toggling
   * screensharing.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleScreenshare() {
        if (!this.props._desktopSharingEnabled) {
            return;
        }

        const enable = !this.props._screensharing;

        sendAnalytics(
      createShortcutEvent('toggle.screen.sharing', ACTION_SHORTCUT_TRIGGERED, {
          enable
      })
        );

        this._doToggleScreenshare(enable);
    }
    _onZoomIn: () => void;

    /**
   * Opens the {@code VideoZoomInOnClick}.
   *
   * @private
   * @returns {void}
   */
    _onZoomIn() {
        sendAnalytics(createToolbarEvent('video-zoom.zoomin'));

        this.props.dispatch(togglezoom());
    }

    _onZoomOut: () => void;

    /**
   * Opens the {@code VideoZoomoutOnClick}.
   *
   * @private
   * @returns {void}
   */
    _onZoomOut() {
        sendAnalytics(createToolbarEvent('video-zoom.zoomout'));
        this.props.dispatch(togglezoomout());
    }
    _onToolbarToggleSharedVideo: () => void;

    /**
   * Creates an analytics toolbar event and dispatches an action for toggling
   * the sharing of a YouTube video.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleSharedVideo() {
        sendAnalytics(
      createToolbarEvent('shared.video.toggled', {
          enable: !this.props._sharingVideo
      })
        );

        this._doToggleSharedVideo();
    }
    _onToolbarToggleDonorbox: () => void;

    /**
   * Creates dispatches an action for toggling
   * the sharing of a donate button across the conference.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleDonorbox() {
        this._doToggleDonorboxDialog();
    }

    _onToolbarToggleclickandPledge: () => void;

    /**
   * Creates dispatches an action for toggling
   * the sharing of a donate button across the conference.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleclickandPledge() {
        this._doToggleCLPDialog();
    }

    _onToolbarToggleRemoveDonorbox: () => void;

    /**
   * Creates dispatches an action for toggling
   * the removing the donorbox url.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleRemoveDonorbox() {
        this._doRemoveDonorDialog();
    }

    _onToolbarToggleVoiceCOmmandsDialog: () => void;

    /**
   * Creates dispatches an action for toggling
   * the dialog for Voice Commands.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleVoiceCOmmandsDialog() {
        this._doOpenVoiceCommand();
    }

    _onToolbarToggleRemoveclickandPledge: () => void;

    /**
   * Creates dispatches an action for toggling
   * the removing the donorbox url.
   *
   * @private
   * @returns {void}
   */
    _onToolbarToggleRemoveclickandPledge() {
        this._doRemoveClpDialog();
    }

    _onToolbarOpenLocalRecordingInfoDialog: () => void;

    /**
   * Opens the {@code LocalRecordingInfoDialog}.
   *
   * @private
   * @returns {void}
   */
    _onToolbarOpenLocalRecordingInfoDialog() {
        sendAnalytics(createToolbarEvent('local.recording'));

        this.props.dispatch(openDialog(LocalRecordingInfoDialog));
    }

    /**
   * Returns true if the the desktop sharing button should be visible and
   * false otherwise.
   *
   * @returns {boolean}
   */
    _isDesktopSharingButtonVisible() {
        const { _desktopSharingEnabled, _desktopSharingDisabledTooltipKey }
      = this.props;

        return _desktopSharingEnabled || _desktopSharingDisabledTooltipKey;
    }

    /**
   * Renders a button for toggleing screen sharing.
   *
   * @private
   * @param {boolean} isInOverflowMenu - True if the button is moved to the
   * overflow menu.
   * @returns {ReactElement|null}
   */
    _renderDesktopSharingButton(isInOverflowMenu = false) {
        const {
            _desktopSharingEnabled,
            _desktopSharingDisabledTooltipKey,
            _screensharing,
            t
        } = this.props;

        if (!this._isDesktopSharingButtonVisible()) {
            return null;
        }

        if (isInOverflowMenu) {
            return (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.shareYourScreen') }
                    disabled = { !_desktopSharingEnabled }
                    icon = { IconStartScreenShare }
                    iconId = 'share-desktop'
                    key = 'desktop'
                    onClick = { this._onToolbarToggleScreenshare }
                    text = { t(
            `toolbar.${
                _screensharing ? 'stopScreenSharing' : 'startScreenSharing'
            }`
                    ) } />
            );
        }

        const tooltip = t(
      _desktopSharingEnabled
          ? 'dialog.shareYourScreen'
          : _desktopSharingDisabledTooltipKey
        );

        return (
            <ToolbarButton
                accessibilityLabel = { t('toolbar.accessibilityLabel.shareYourScreen') }
                disabled = { !_desktopSharingEnabled }
                icon = { IconStartScreenShare }
                key = 'desktop'
                onClick = { this._onToolbarToggleScreenshare }
                toggled = { _screensharing }
                tooltip = { tooltip } />
        );
    }

    /**
   * Returns true if the profile button is visible and false otherwise.
   *
   * @returns {boolean}
   */
    _isEmbedMeetingVisible() {
        return (
            !this.props._isVpaasMeeting
      && (this._shouldShowButton('embedmeeting') || this.props._embedMeeting)
        );
    }

    /**
   * Returns true if the profile button is visible and false otherwise.
   *
   * @returns {boolean}
   */
    _isProfileVisible() {
        return this.props._isGuest && this._shouldShowButton('profile');
    }

    /**
   * Returns true if the desktop sharing button should be visible and
   * false otherwise.
   *
   * @returns {boolean}
   */
    _showDesktopSharingButton() {
        const { _desktopSharingEnabled, _desktopSharingDisabledTooltipKey }
      = this.props;

        return (
            (_desktopSharingEnabled || _desktopSharingDisabledTooltipKey)
      && this._shouldShowButton('desktop')
        );
    }

    /**
   * Returns the buttons to be displayed in main or the overflow menu.
   *
   * @param {Set} buttons - A set containing the buttons to be displayed
   * in the toolbar beside the main audio/video & hanugup.
   * @returns {Object}
   */
    _getAdditionalButtons(buttons) {
        const {
            _chatOpen,
            _desktopSharingEnabled,
            _desktopSharingDisabledTooltipKey,
            _raisedHand,
            _screensharing,
            _screenShareVisible,
            t
        } = this.props;

        const overflowMenuAdditionalButtons = [];
        const mainMenuAdditionalButtons = [];

        if (this._showDesktopSharingButton()) {
            buttons.has('desktop') && _screenShareVisible
                ? mainMenuAdditionalButtons.push(
            <ToolbarButton
                accessibilityLabel = { t(
                'toolbar.accessibilityLabel.shareYourScreen'
                ) }
                disabled = { !_desktopSharingEnabled }
                icon = { IconShareDesktop }
                key = 'desktop'
                onClick = { this._onToolbarToggleScreenshare }
                toggled = { _screensharing }
                tooltip = { t(
                _desktopSharingEnabled
                    ? 'dialog.shareYourScreen'
                    : _desktopSharingDisabledTooltipKey
                ) } />
                )
                : overflowMenuAdditionalButtons.push(
            <OverflowMenuItem
                accessibilityLabel = { t(
                'toolbar.accessibilityLabel.shareYourScreen'
                ) }
                icon = { IconShareDesktop }
                iconId = 'share-desktop'
                key = 'desktop'
                onClick = { this._onToolbarToggleScreenshare }
                text = { t(
                `toolbar.${
                    _screensharing ? 'stopScreenSharing' : 'startScreenSharing'
                }`
                ) } />
                );
        }

        if (this._shouldShowButton('chat')) {
            buttons.has('chat')
                ? mainMenuAdditionalButtons.push(
            <div
                className = 'toolbar-button-with-badge'
                key = 'chatcontainer'>
                <ToolbarButton
                    accessibilityLabel = { t('toolbar.accessibilityLabel.chat') }
                    icon = { IconChat }
                    key = 'chat'
                    onClick = { this._onToolbarToggleChat }
                    toggled = { _chatOpen }
                    tooltip = { t('toolbar.chat') } />
                <ChatCounter />
            </div>
                )
                : overflowMenuAdditionalButtons.push(
            <OverflowMenuItem
                accessibilityLabel = { t('toolbar.accessibilityLabel.chat') }
                icon = { IconChat }
                key = 'chat'
                onClick = { this._onToolbarToggleChat }
                text = { t(`toolbar.${_chatOpen ? 'closeChat' : 'openChat'}`) } />
                );
        }

        if (this._shouldShowButton('raisehand')) {
            buttons.has('raisehand')
                ? mainMenuAdditionalButtons.push(
            <ToolbarButton
                accessibilityLabel = { t('toolbar.accessibilityLabel.raiseHand') }
                icon = { IconRaisedHand }
                key = 'raisehand'
                onClick = { this._onToolbarToggleRaiseHand }
                toggled = { _raisedHand }
                tooltip = { t(
                `toolbar.${_raisedHand ? 'lowerYourHand' : 'raiseYourHand'}`
                ) } />
                )
                : overflowMenuAdditionalButtons.push(
            <OverflowMenuItem
                accessibilityLabel = { t('toolbar.accessibilityLabel.raiseHand') }
                icon = { IconRaisedHand }
                key = 'raisehand'
                onClick = { this._onToolbarToggleRaiseHand }
                text = { t(
                `toolbar.${_raisedHand ? 'lowerYourHand' : 'raiseYourHand'}`
                ) } />
                );
        }

        if (this._shouldShowButton('raisehand')) {
            buttons.has('raisehand')
                ? mainMenuAdditionalButtons.push(
            <ToolbarButton
                accessibilityLabel = { t('toolbar.accessibilityLabel.participants') }
                icon = { IconParticipants }
                onClick = { this._onToolbarToggleParticipantsPane }
                toggled = { this.props._participantsPaneOpen }
                tooltip = { t('toolbar.participants') } />
                )
                : overflowMenuAdditionalButtons.push(
            <OverflowMenuItem
                accessibilityLabel = { t('toolbar.accessibilityLabel.participants') }
                icon = { IconParticipants }
                key = 'participants-pane'
                onClick = { this._onToolbarToggleParticipantsPane }
                text = { t('toolbar.participants') }
                toggled = { this.props._participantsPaneOpen } />
                );
        }

        if (this._shouldShowButton('tileview')) {
            buttons.has('tileview')
                ? mainMenuAdditionalButtons.push(
            <TileViewButton
                key = 'tileview'
                showLabel = { false } />
                )
                : overflowMenuAdditionalButtons.push(
            <TileViewButton
                key = 'tileview'
                showLabel = { true } />
                );
        }

        return {
            mainMenuAdditionalButtons,
            overflowMenuAdditionalButtons
        };
    }

    /**
   * Renders the list elements of the overflow menu.
   *
   * @private
   * @returns {Array<ReactElement>}
   */
    _renderShareFlowContent() {
        const {
            _shareVideoState,
            _isModerator,
            _screensharing,
            _partId,
            _etherpadPart,
            _isWhiteBoardID,
            _isetherPadId,
            _whiteBoardvisible,
            _livePadVisible,
            _screenShareVisible
        } = this.props;

        return [
            this._shouldShowButton('desktop')
        && _screenShareVisible
        && Boolean(!_isWhiteBoardID && !_isetherPadId)
        && !_shareVideoState
        && this._renderDesktopSharingButton(true),
            (this._shouldShowButton('livepad') || _livePadVisible)
        && Boolean(
          (!_isWhiteBoardID && !_isetherPadId) || _etherpadPart || _partId
        )
        && _isModerator
        && !_shareVideoState
          && <SharedDocumentButton
              key = 'etherpad'
              showLabel = { true } />,
            (this._shouldShowButton('whiteboard') || _whiteBoardvisible)
        && Boolean(
          (!_isWhiteBoardID && !_isetherPadId) || _etherpadPart || _partId
        )
        && _isModerator
        && !_shareVideoState
          && <GenericIFrameButton
              key = 'genericiframe'
              showLabel = { true } />,
            this._shouldShowButton('sharedvideo')
        && Boolean(!_isWhiteBoardID && !_isetherPadId)
        && !_screensharing
        && _isModerator
          && <SharedVideoButton
              key = 'sharedvideo'
              showLabel = { true } />

        ];
    }

    /**
   * Renders the list elements of the overflow menu.
   *
   * @private
   * @returns {Array<ReactElement>}
   */
    _renderOverflowMenuContent() {
        const {
            _clicknPledgeVisible,
            _donorBoxVisible,
            _feedbackConfigured,
            _fullScreen,
            _screensharing,
            _geturl,
            _getCUrl,
            liveStreamVisible,
            RecordingVisible,
            _isModerator,
            vcVisible,
            _isVoiceCommandDisabled,
            t
        } = this.props;

        const bowser = new BrowserDetection();
        const browserName = bowser.getName();

        return [
            this._isProfileVisible() && (
                <div className = 'col-m-4'>
                    <OverflowMenuProfileItem
                        key = 'profile'
                        onClick = { this._onToolbarToggleProfile } />
                </div>
            ),
            this._shouldShowButton('videoquality') && (
                <div className = 'col-m-4'>
                    <OverflowMenuVideoQualityItem
                        key = 'videoquality'
                        onClick = { this._onToolbarOpenVideoQuality } />
                </div>
            ),
            this._shouldShowButton('fullscreen') && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.fullScreen') }
                        icon = { _fullScreen ? IconExitFullScreen : IconFullScreen }
                        key = 'fullscreen'
                        onClick = { this._onToolbarToggleFullScreen }
                        text = {
                            _fullScreen
                                ? t('toolbar.exitFullScreen')
                                : t('toolbar.enterFullScreen')
                        } />
                </div>
            ),
            liveStreamVisible && (
                <div
                    className = 'col-m-4'
                    id = 'liveStream-div'
                    key = 'livestreaming'>
                    <LiveStreamButton showLabel = { true } />
                </div>
            ),
            RecordingVisible && (
                <div
                    className = 'col-m-4'
                    id = 'recordIng-div'
                    key = 'record'>
                    <RecordButton showLabel = { true } />
                </div>
            ),

            // this._shouldShowButton('sharedvideo')
            //     && <div className = 'col-m-4'><SharedVideoButton
            //         key = 'sharedvideo'
            //         showLabel = { true } /></div>,
            (this._shouldShowButton('addclickNpledge') || _clicknPledgeVisible)
        && _getCUrl === undefined
        && _isModerator && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.donationLink') }
                        disabled = { !(_geturl === undefined) }
                        icon = { IconClickPledge }
                        key = 'clickandpledge'
                        onClick = { this._onToolbarToggleclickandPledge }
                        text = { t('toolbar.donationCLP') } />
                </div>
            ),
            (this._shouldShowButton('removeclickNpledge') || _clicknPledgeVisible)
        && _getCUrl
        && _isModerator && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t(
                'toolbar.accessibilityLabel.removeDonation'
                        ) }
                        icon = { IconClickPledge }
                        key = 'donorboxremove'
                        onClick = { this._onToolbarToggleRemoveclickandPledge }
                        text = { t('toolbar.rmoveCDonation') } />
                </div>
            ),
            (this._shouldShowButton('adddonorbox') || _donorBoxVisible)
        && _geturl === undefined
        && _isModerator && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.donationLink') }
                        disabled = { !(_getCUrl === undefined) }
                        icon = { IconDonate }
                        key = 'donorbox'
                        onClick = { this._onToolbarToggleDonorbox }
                        text = { t('toolbar.donationLink') } />
                </div>
            ),
            this._shouldShowButton('voicecommand') && !_isVoiceCommandDisabled && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.rmoveCDonation') }
                        icon = { IconVoiceCommand }
                        key = 'voicecommand'
                        onClick = { this._onToolbarToggleVoiceCOmmandsDialog }
                        text = { t('toolbar.voiceCommand') } />
                </div>
            ),
            (this._shouldShowButton('removedonorbox') || _donorBoxVisible)
        && _geturl
        && _isModerator && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t(
                'toolbar.accessibilityLabel.rmoveCDonation'
                        ) }
                        icon = { IconDonate }
                        key = 'donorboxremove'
                        onClick = { this._onToolbarToggleRemoveDonorbox }
                        text = { t('toolbar.removeDonation') } />
                </div>
            ),

            // this._shouldShowButton('etherpad')
            //     && <div className = 'col-m-4'><SharedDocumentButton
            //         key = 'etherpad'
            //         showLabel = { true } /></div>,
            (this._shouldShowButton('select-background')
        || this._shouldShowButton('videobackgroundblur'))
        && vcVisible
        && browserName !== SAFARI && (
                <div className = 'col-m-4'>
                    <VideoBackgroundButton
                        key = { 'select-background' }
                        showLabel = { true }
                        visible = { !_screensharing && checkBlurSupport() } />
                </div>
            ),
            <div
                className = 'col-m-4'
                key = 'settings'>
                <SettingsButton
                    showLabel = { true }
                    visible = { this._shouldShowButton('settings') } />
            </div>,
            this._shouldShowButton('stats') && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.speakerStats') }
                        icon = { IconPresentation }
                        key = 'stats'
                        onClick = { this._onToolbarOpenSpeakerStats }
                        text = { t('toolbar.speakerStats') } />
                </div>
            ),
            this._isEmbedMeetingVisible() && _isModerator && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.embedMeeting') }
                        icon = { IconCodeBlock }
                        key = 'embed'
                        onClick = { this._onToolbarOpenEmbedMeeting }
                        text = { t('toolbar.embedMeeting') } />
                </div>
            ),
            this._shouldShowButton('feedback') && _feedbackConfigured && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.feedback') }
                        icon = { IconFeedback }
                        key = 'feedback'
                        onClick = { this._onToolbarOpenFeedback }
                        text = { t('toolbar.feedback') } />
                </div>
            ),
            this._shouldShowButton('shortcuts') && (
                <div className = 'col-m-4'>
                    <OverflowMenuItem
                        accessibilityLabel = { t('toolbar.accessibilityLabel.shortcuts') }
                        icon = { IconOpenInNew }
                        key = 'shortcuts'
                        onClick = { this._onToolbarOpenKeyboardShortcuts }
                        text = { t('toolbar.shortcuts') } />
                </div>
            ),
            this._shouldShowButton('download') && (
                <div className = 'col-m-4'>
                    <DownloadButton
                        key = 'download'
                        showLabel = { true } />
                </div>
            ),
            this._shouldShowButton('help') && (
                <div className = 'col-m-4'>
                    <HelpButton
                        key = 'help'
                        showLabel = { true } />
                </div>
            )
        ];
    }

    /**
   * Renders the list elements of the overflow menu.
   *
   * @private
   * @param {Array<React$Element>} additionalButtons - Additional buttons to be displayed.
   * @returns {Array<React$Element>}
   */
    _renderOverflowMenuContentMobile(
            additionalButtons: Array<React$Element<any>>
    ) {
        const {
            _donorBoxVisible,
            _feedbackConfigured,
            _fullScreen,
            _isMobile,
            _screensharing,
            _geturl,
            _getCUrl,
            _isModerator,
            t
        } = this.props;

        const bowser = new BrowserDetection();
        const browserName = bowser.getName();

        const group1 = [
            ...additionalButtons,

            this._shouldShowButton('videoquality') && (
                <OverflowMenuVideoQualityItem
                    key = 'videoquality'
                    onClick = { this._onToolbarOpenVideoQuality } />
            ),
            this._shouldShowButton('fullscreen') && !_isMobile && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.fullScreen') }
                    icon = { _fullScreen ? IconExitFullScreen : IconFullScreen }
                    key = 'fullscreen'
                    onClick = { this._onToolbarToggleFullScreen }
                    text = {
                        _fullScreen
                            ? t('toolbar.exitFullScreen')
                            : t('toolbar.enterFullScreen')
                    } />
            ),
            (this._shouldShowButton('security')
        || this._shouldShowButton('info'))
        && <SecurityDialogButton
            key = 'security'
            showLabel = { true } />,
            this._shouldShowButton('adddonorbox')
        && _getCUrl === undefined
        && _isModerator
        && _donorBoxVisible && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.donationLink') }
                    disabled = { !(_geturl === undefined) }
                    icon = { IconClickPledge }
                    key = 'clickandpledge'
                    onClick = { this._onToolbarToggleclickandPledge }
                    text = { t('toolbar.donationCLP') } />
            ),
            this._shouldShowButton('removedonorbox') && _getCUrl && _isModerator && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.removeDonation') }
                    icon = { IconClickPledge }
                    key = 'donorboxremove'
                    onClick = { this._onToolbarToggleRemoveclickandPledge }
                    text = { t('toolbar.rmoveCDonation') } />
            ),
            this._shouldShowButton('adddonorbox')
        && _geturl === undefined
        && _isModerator && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.donationLink') }
                    disabled = { !(_getCUrl === undefined) }
                    icon = { IconDonate }
                    key = 'donorbox'
                    onClick = { this._onToolbarToggleDonorbox }
                    text = { t('toolbar.donationLink') } />
            ),
            this._shouldShowButton('removedonorbox') && _geturl && _isModerator && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.rmoveCDonation') }
                    icon = { IconDonate }
                    key = 'donorboxremove'
                    onClick = { this._onToolbarToggleRemoveDonorbox }
                    text = { t('toolbar.removeDonation') } />
            ),
            this._shouldShowButton('closedcaptions')
        && <ClosedCaptionButton
            key = 'closed-captions'
            showLabel = { true } />,
            this._shouldShowButton('recording')
        && <RecordButton
            key = 'record'
            showLabel = { true } />,
            this._shouldShowButton('localrecording') && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.localRecording') }
                    icon = { IconRec }
                    key = 'localrecording'
                    onClick = { this._onToolbarOpenLocalRecordingInfoDialog }
                    text = { t('localRecording.dialogTitle') } />
            ),
            this._shouldShowButton('mute-everyone')
        && <MuteEveryoneButton
            key = 'mute-everyone'
            showLabel = { true } />,
            this._shouldShowButton('livestreaming')
        && <LiveStreamButton
            key = 'livestreaming'
            showLabel = { true } />

        ];

        const group2 = [
            this._shouldShowButton('sharedvideo')
        && <SharedVideoButton
            key = 'sharedvideo'
            showLabel = { true } />,
            this._shouldShowButton('etherpad')
        && <SharedDocumentButton
            key = 'etherpad'
            showLabel = { true } />,
            (this._shouldShowButton('select-background')
        || this._shouldShowButton('videobackgroundblur'))
        && browserName !== SAFARI && (
                <VideoBackgroundButton
                    key = { 'select-background' }
                    showLabel = { true }
                    visible = { !_screensharing && checkBlurSupport() } />
            ),
            this._shouldShowButton('stats') && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.speakerStats') }
                    icon = { IconPresentation }
                    key = 'stats'
                    onClick = { this._onToolbarOpenSpeakerStats }
                    text = { t('toolbar.speakerStats') } />
            )
        ];

        return [
            this._isProfileVisible() && (
                <OverflowMenuProfileItem
                    key = 'profile'
                    onClick = { this._onToolbarToggleProfile } />
            ),
            this._isProfileVisible() && <hr
                className = 'overflow-menu-hr'
                key = 'hr1' />,

            ...group1,
            group1.some(Boolean) && <hr
                className = 'overflow-menu-hr'
                key = 'hr2' />,

            ...group2,
            group2.some(Boolean) && <hr
                className = 'overflow-menu-hr'
                key = 'hr3' />,

            this._shouldShowButton('settings')
        && <SettingsButton
            key = 'settings'
            showLabel = { true } />,

            this._isEmbedMeetingVisible() && _isModerator && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.embedMeeting') }
                    icon = { IconCodeBlock }
                    key = 'embed'
                    onClick = { this._onToolbarOpenEmbedMeeting }
                    text = { t('toolbar.embedMeeting') } />
            ),
            this._shouldShowButton('feedback') && _feedbackConfigured && (
                <OverflowMenuItem
                    accessibilityLabel = { t('toolbar.accessibilityLabel.feedback') }
                    icon = { IconFeedback }
                    key = 'feedback'
                    onClick = { this._onToolbarOpenFeedback }
                    text = { t('toolbar.feedback') } />
            ),
            this._shouldShowButton('download')
        && <DownloadButton
            key = 'download'
            showLabel = { true } />,
            this._shouldShowButton('help')
        && <HelpButton
            key = 'help'
            showLabel = { true } />

        ];
    }

    /**
   * Renders a list of buttons that are moved to the overflow menu.
   *
   * @private
   * @param {Array<string>} movedButtons - The names of the buttons to be
   * moved.
   * @returns {Array<ReactElement>}
   */
    _renderMovedButtons(movedButtons) {
        const { _chatOpen, _raisedHand, t } = this.props;

        return movedButtons.map(buttonName => {
            switch (buttonName) {
            case 'desktop':
                return this._renderDesktopSharingButton(true);
            case 'raisehand':
                return (
                    <div className = 'col-m-4'>
                        <OverflowMenuItem
                            accessibilityLabel = { t('toolbar.accessibilityLabel.raiseHand') }
                            icon = { IconRaisedHand }
                            key = 'raisedHand'
                            onClick = { this._onToolbarToggleRaiseHand }
                            text = { t(
                  `toolbar.${_raisedHand ? 'lowerYourHand' : 'raiseYourHand'}`
                            ) } />
                    </div>
                );
            case 'chat':
                return (
                    <div className = 'col-m-4'>
                        <OverflowMenuItem
                            accessibilityLabel = { t('toolbar.accessibilityLabel.chat') }
                            icon = { IconChat }
                            key = 'chat'
                            onClick = { this._onToolbarToggleChat }
                            text = { t(`toolbar.${_chatOpen ? 'closeChat' : 'openChat'}`) } />
                    </div>
                );
            case 'closedcaptions':
                return (
                    <div className = 'col-m-4'>
                        <ClosedCaptionButton
                            key = 'closed-captions'
                            showLabel = { true } />
                    </div>
                );
            case 'security':
                return (
                    <div className = 'col-m-4'>
                        <SecurityDialogButton
                            key = 'security'
                            showLabel = { true } />
                    </div>
                );
            case 'invite':
                return (
                    <div className = 'col-m-4'>
                        <OverflowMenuItem
                            accessibilityLabel = { t('toolbar.accessibilityLabel.invite') }
                            icon = { IconInviteMore }
                            key = 'invite'
                            onClick = { this._onToolbarOpenInvite }
                            text = { t('toolbar.invite') } />
                    </div>
                );
            case 'tileview':
                return <TileViewButton showLabel = { true } />;

            case 'zoomin':
                return (
                    <div className = 'col-m-4'>
                        <OverflowMenuItem
                            accessibilityLabel = 'ZoomIn'
                            icon = { IconZoomIn }
                            key = 'Zoomin'
                            onClick = { this._onZoomIn }
                            text = { t('toolbar.zoomin') } />
                    </div>
                );
            case 'zoomout':
                return (
                    <div className = 'col-m-4'>
                        <OverflowMenuItem
                            accessibilityLabel = 'Zoom Out'
                            icon = { IconZoomOut }
                            key = 'Zoomout'
                            onClick = { this._onZoomOut }
                            text = { t('toolbar.zoomout') } />
                    </div>
                );
            case 'participants-pane':
                return (
                    <div className = 'col-m-4'>
                        <OverflowMenuItem
                            accessibilityLabel = { t(
                  'toolbar.accessibilityLabel.participants'
                            ) }
                            icon = { IconParticipants }
                            key = 'participants-pane'
                            onClick = { this._onToolbarToggleParticipantsPane }
                            text = { t('toolbar.participants') } />
                    </div>
                );
            case 'localrecording':
                return (
                    <div className = 'col-m-4'>
                        <OverflowMenuItem
                            accessibilityLabel = { t(
                  'toolbar.accessibilityLabel.localRecording'
                            ) }
                            icon = { IconRec }
                            key = 'localrecording'
                            onClick = { this._onToolbarOpenLocalRecordingInfoDialog }
                            text = { t('localRecording.dialogTitle') } />
                    </div>
                );
            default:
                return null;
            }
        });
    }

    /**
   * Renders the Audio controlling button.
   *
   * @returns {ReactElement}
   */
    _renderAudioButton() {
        return this._shouldShowButton('microphone')
            ? <AudioSettingsButton
                key = 'asb'
                visible = { true } />
            : null;
    }

    /**
   * Renders the Video controlling button.
   *
   * @returns {ReactElement}
   */
    _renderVideoButton() {
        return this._shouldShowButton('camera')
            ? <VideoSettingsButton
                key = 'vsb'
                visible = { true } />
            : null;
    }

    /**
   * Renders the toolbox content.
   *
   * @returns {Array<ReactElement>}
   */
    _renderToolboxContent() {
        const {
            _chatOpen,
            _clientWidth,
            _overflowMenuVisible,
            _isMobile,
            _raisedHand,
            _isOpenP,
            _ShareMenuVisibility,
            t,
            _requestingCaptionsModal
        } = this.props;

        if (_isMobile) {
            const buttonSet = getToolbarAdditionalButtons(_clientWidth, _isMobile);
            const toolbarAccLabel = 'toolbar.accessibilityLabel.moreActionsMenu';
            const showOverflowMenuButton = buttonSet.has('overflow');
            const containerClassName = `toolbox-content${
                _isMobile ? ' toolbox-content-mobile' : ''
            }`;

            let overflowMenuAdditionalButtons = [];
            let mainMenuAdditionalButtons = [];

            if (showOverflowMenuButton) {
                ({ overflowMenuAdditionalButtons, mainMenuAdditionalButtons }
          = this._getAdditionalButtons(buttonSet));
            }

            return (
                <div className = { containerClassName }>
                    <div
                        className = 'toolbox-content-wrapper'
                        onMouseOut = { this._onMouseOut }
                        onMouseOver = { this._onMouseOver }>
                        <div className = 'toolbox-content-items'>
                            { this._renderAudioButton() }
                            { this._renderVideoButton() }
                            { mainMenuAdditionalButtons }
                            { showOverflowMenuButton && (
                                <OverflowMenuButton
                                    isOpen = { _overflowMenuVisible }
                                    onVisibilityChange = { this._onSetOverflowVisible }>
                                    <ul
                                        aria-label = { t(toolbarAccLabel) }
                                        className = 'overflow-menu'>
                                        { this._renderOverflowMenuContentMobile(
                      overflowMenuAdditionalButtons
                                        ) }
                                    </ul>
                                </OverflowMenuButton>
                            ) }
                            <HangupSettings
                                customClass = 'hangup-button'
                                visible = { this._shouldShowButton('hangup') } />
                        </div>
                    </div>
                </div>
            );
        }
        const overflowMenuContent = this._renderOverflowMenuContent();
        const overflowHasItems = Boolean(
      overflowMenuContent.filter(child => child).length
        );
        const toolbarAccLabel = 'toolbar.accessibilityLabel.moreActionsMenu';
        const buttonsLeft = [];
        const buttonsRight = [];

        const smallThreshold = 700;
        const verySmallThreshold = 500;

        let minSpaceBetweenButtons = 54;
        let widthPlusPaddingOfButton = 62;

        if (this.state.windowWidth <= verySmallThreshold) {
            minSpaceBetweenButtons = 16;
            widthPlusPaddingOfButton = 18;
        } else if (this.state.windowWidth <= smallThreshold) {
            minSpaceBetweenButtons = 23;
            widthPlusPaddingOfButton = 30;
        }
        let widthOpen = _isOpenP ? 315 : 0;

        widthOpen = _chatOpen ? widthOpen + 375 : widthOpen;

        // widthOpen = _chatOpen ?
        const maxNumberOfButtonsPerGroup = Math.floor(
      (this.state.windowWidth
        - widthOpen
        - 168 // the width of the central group by design
        - minSpaceBetweenButtons) // the minimum space between the button groups
        / widthPlusPaddingOfButton // the width + padding of a button
        / 2 // divide by the number of groups(left and right group)
        );

        const showOverflowMenu
      = this.state.windowWidth >= verySmallThreshold || isMobileBrowser();

        if (this._shouldShowButton('chat')) {
            buttonsLeft.push('chat');
        }
        if (
            this._shouldShowButton('desktop')
      && this._isDesktopSharingButtonVisible()
        ) {
            buttonsLeft.push('desktop');
        }
        if (this._shouldShowButton('raisehand')) {
            buttonsLeft.push('raisehand');
        }
        if (this._shouldShowButton('closedcaptions')) {
            buttonsLeft.push('closedcaptions');
        }
        if (overflowHasItems && showOverflowMenu) {
            buttonsRight.push('overflowmenu');
        }
        if (this._shouldShowButton('invite')) {
            buttonsRight.push('invite');
        }
        if (this._shouldShowButton('participants-pane')) {
            buttonsRight.push('participants-pane');
        }
        if (this._shouldShowButton('security') || this._shouldShowButton('info')) {
            buttonsRight.push('security');
        }

        if (this._shouldShowButton('tileview')) {
            buttonsRight.push('tileview');
        }
        if (this._shouldShowButton('zoomin')) {
            buttonsRight.push('zoomin');
        }
        if (this._shouldShowButton('zoomout')) {
            buttonsRight.push('zoomout');
        }
        if (this._shouldShowButton('localrecording')) {
            buttonsRight.push('localrecording');
        }

        const movedButtons = [];

        if (buttonsLeft.length > maxNumberOfButtonsPerGroup) {
            movedButtons.push(
        ...buttonsLeft.splice(
          maxNumberOfButtonsPerGroup,
          buttonsLeft.length - maxNumberOfButtonsPerGroup
        )
            );
            if (buttonsRight.indexOf('overflowmenu') === -1 && showOverflowMenu) {
                buttonsRight.unshift('overflowmenu');
            }
        }

        if (buttonsRight.length > maxNumberOfButtonsPerGroup) {
            if (buttonsRight.indexOf('overflowmenu') === -1 && showOverflowMenu) {
                buttonsRight.unshift('overflowmenu');
            }

            let numberOfButtons = maxNumberOfButtonsPerGroup;

            // make sure the more button will be displayed when we move buttons.
            if (numberOfButtons === 0) {
                numberOfButtons++;
            }

            movedButtons.push(
        ...buttonsRight.splice(
          numberOfButtons,
          buttonsRight.length - numberOfButtons
        )
            );
        }

        const shareFlowMenu = this._renderShareFlowContent();

        overflowMenuContent.splice(1, 0, ...this._renderMovedButtons(movedButtons));

        overflowMenuContent.filter(i => i !== undefined && i !== false);

        return (
            <div className = 'toolbox-content'>
                <div className = 'button-group-left'>
                    { /* {buttonsLeft.indexOf('desktop') !== -1
                        && this._renderDesktopSharingButton()} */ }
                    <ScreenOverflowButton
                        isOpen = { _ShareMenuVisibility }
                        onVisibilityChange = { this._onSetShareScreenOverflowVisible }>
                        <ul
                            aria-label = { t(toolbarAccLabel) }
                            className = 'overflow-menu'>
                            { shareFlowMenu }
                        </ul>
                    </ScreenOverflowButton>
                    { buttonsLeft.indexOf('raisehand') !== -1 && (
                        <ToolbarButton
                            accessibilityLabel = { t('toolbar.accessibilityLabel.raiseHand') }
                            icon = { IconRaisedHand }
                            onClick = { this._onToolbarToggleRaiseHand }
                            toggled = { _raisedHand }
                            tooltip = { t('toolbar.raiseHand') } />
                    ) }
                    { buttonsLeft.indexOf('chat') !== -1 && (
                        <div className = 'toolbar-button-with-badge'>
                            <ToolbarButton
                                accessibilityLabel = { t('toolbar.accessibilityLabel.chat') }
                                icon = { IconChat }
                                onClick = { this._onToolbarToggleChat }
                                toggled = { _chatOpen }
                                tooltip = { t('toolbar.chat') } />
                            <ChatCounter />
                        </div>
                    ) }
                    { buttonsRight.indexOf('invite') !== -1 && (
                        <ToolbarButton
                            accessibilityLabel = { t('toolbar.accessibilityLabel.invite') }
                            icon = { IconInviteMore }
                            onClick = { this._onToolbarOpenInvite }
                            tooltip = { t('toolbar.invite') } />
                    ) }
                    { buttonsLeft.indexOf('closedcaptions') !== -1
          && _requestingCaptionsModal
                        ? <ClosedCaptionButton />
                        : (
                            <ToolboxButtonWithIcon
                                icon = { IconArrowUp }
                                iconDisabled = { false }
                                iconTooltip = { t('transcribing.OpenCloseCaptions') }
                                onIconClick = { this._onCaptionsArrowClick }>
                                <ClosedCaptionButton />
                            </ToolboxButtonWithIcon>
                        ) }
                    <ReactionsContainerButton
                        handleClick = { this._onToolbarToggleRaiseHand } />
                </div>
                <div className = 'button-group-center'>
                    { this._renderAudioButton() }
                    <HangupSettings
                        customClass = 'hangup-button '
                        visible = { this._shouldShowButton('hangup') } />
                    { this._renderVideoButton() }
                </div>
                <div className = 'button-group-right'>
                    { buttonsRight.indexOf('localrecording') !== -1 && (
                        <LocalRecordingButton
                            onClick = { this._onToolbarOpenLocalRecordingInfoDialog } />
                    ) }
                    { buttonsRight.indexOf('tileview') !== -1 && <TileViewButton /> }
                    { buttonsRight.indexOf('participants-pane') !== -1 && (
                        <ToolbarButton
                            accessibilityLabel = { t('toolbar.accessibilityLabel.participants') }
                            icon = { IconParticipants }
                            key = 'participants-pane'
                            onClick = { this._onToolbarToggleParticipantsPane }
                            toggled = { this.props._participantsPaneOpen }
                            tooltip = { t('toolbar.participants') } />
                    ) }
                    { buttonsRight.indexOf('zoomin') !== -1 && (
                        <ToolbarButton
                            accessibilityLabel = 'ZoomIn'
                            icon = { IconZoomIn }
                            key = 'ZoomIn'
                            onClick = { this._onZoomIn }
                            toggled = { false }
                            tooltip = { t('toolbar.zoomin') } />
                    ) }
                    { buttonsRight.indexOf('zoomout') !== -1 && (
                        <ToolbarButton
                            accessibilityLabel = 'Zoomout'
                            icon = { IconZoomOut }
                            key = 'Zoomout'
                            onClick = { this._onZoomOut }
                            tooltip = { t('toolbar.zoomout') } />
                    ) }
                    { buttonsRight.indexOf('security') !== -1
            && <SecurityDialogButton customClass = 'security-toolbar-button' />
                    }
                    { buttonsRight.indexOf('overflowmenu') !== -1 && (
                        <OverflowMenuButton
                            isOpen = { _overflowMenuVisible }
                            onVisibilityChange = { this._onSetOverflowVisible }>
                            <ul
                                aria-label = { t(toolbarAccLabel) }
                                className = 'overflow-menu'>
                                <div className = 'rows'>{ overflowMenuContent }</div>
                            </ul>
                        </OverflowMenuButton>
                    ) }
                </div>
            </div>
        );
    }

    _shouldShowButton: (string) => boolean;

    /**
   * Returns if a button name has been explicitly configured to be displayed.
   *
   * @param {string} buttonName - The name of the button, as expected in
   * {@link interfaceConfig}.
   * @private
   * @returns {boolean} True if the button should be displayed.
   */
    _shouldShowButton(buttonName) {
        return this.props._visibleButtons.has(buttonName);
    }
}

/**
 * Maps (parts of) the redux state to {@link Toolbox}'s React {@code Component}
 * props.
 *
 * @param {Object} state - The redux store/state.
 * @private
 * @returns {{}}
 */
function _mapStateToProps(state) {
    const { conference, locked } = state['features/base/conference'];

    const { _requestingCaptionsModal } = state['features/subtitles'];

    let desktopSharingEnabled = MeetHourJS.isDesktopSharingEnabled();
    const { callStatsID, enableFeaturesBasedOnToken, liveStreamingEnabled }
    = state['features/base/config'];
    const sharedVideoStatus = state['features/shared-video'].status;
    const { fullScreen, overflowMenuVisible, visibility, iframeVisible }
    = state['features/toolbox'];
    const roomName = getBackendSafeRoomName(
    state['features/base/conference'].room
    );
    const { isOpen } = state['features/participants-pane'];
    const { getUrl, addUrl } = state['features/donorbox'];
    const { getCUrl, addCUrl } = state['features/clickandpledge'];
    const localParticipant = getLocalParticipant(state);
    const localRecordingStates = state['features/local-recording'];
    const localVideo = getLocalVideoTrack(state['features/base/tracks']);
    const { clientWidth } = state['features/base/responsive-ui'];
    const { voiceCommandString } = state['features/base/mtApi'];
    const { confSettings } = state['features/base/jwt'];
    let desktopSharingDisabledTooltipKey;
    let liveStreamVisible;
    let recStreamVisible;
    const isPersonModerator = isLocalParticipantModerator(state);
    const { feature = {} } = getLocalParticipant(state);
    const sharingWhiteBoard = state['features/genericiframe'].visible;

    const recSettings = confSettings
        ? isPersonModerator && confSettings?.ENABLE_RECORDING === 1
        : false;
    const livSettings = confSettings
        ? isPersonModerator && confSettings?.ENABLE_LIVESTREAM === 1
        : false;
    const { fileRecordingsEnabled } = state['features/base/config'];

    liveStreamVisible = liveStreamingEnabled && livSettings && isPersonModerator;
    recStreamVisible = fileRecordingsEnabled && recSettings && isPersonModerator;

    if (typeof recStreamVisible === 'undefined') {
    // If the containing component provides the visible prop, that is one
    // above all, but if not, the button should be autonomus and decide on
    // its own to be visible or not.

        recStreamVisible
      = fileRecordingsEnabled && recSettings && isPersonModerator;

        if (enableFeaturesBasedOnToken) {
            recStreamVisible
        = recStreamVisible && String(feature.recording) === 'true';
            if (!recStreamVisible) {
                recStreamVisible = true;
            }
        }
    }

    if (enableFeaturesBasedOnToken) {
        liveStreamVisible
      = liveStreamVisible && String(feature.livestreaming) === 'true';
        if (!liveStreamVisible) {
            liveStreamVisible = true;
        }
    }

    const toolbarButtons = getToolbarButtons(state);
    const {
        editing: shareEtherpad,
        partId: participantID,
        onEdit
    } = state['features/etherpad'];

    const liveStreamVisible1
    = toolbarButtons.includes('livestreaming') || liveStreamVisible;
    const RecordingVisible
    = toolbarButtons.includes('recording') || recStreamVisible;

    if (enableFeaturesBasedOnToken) {
    // we enable desktop sharing if any participant already have this
    // feature enabled

        desktopSharingEnabled
      = getParticipants(state).find(
        ({ features = {} }) => String(features['screen-sharing']) === 'true'
            ) !== undefined;
        desktopSharingDisabledTooltipKey = 'dialog.shareYourScreenDisabled';
    }

    const embedMeeting = confSettings
        ? Boolean(confSettings?.ENABLE_EMBEED_MEETING === 1 && isPersonModerator)
        : false;
    const donorBoxVisible = confSettings
        ? confSettings?.DONOR_BOX === 1 && isPersonModerator
        : false;
    const clicknPledgeVisible = confSettings
        ? confSettings?.CP_CONNECT === 1 && isPersonModerator
        : false;
    const livePadVisible = confSettings
        ? confSettings?.LIVEPAD === 1 && isPersonModerator
        : false;
    const whiteBoardvisible = confSettings
        ? confSettings?.WHITE_BOARD === 1 && isPersonModerator
        : false;
    const screenShareVisible
    = !_.isNil(confSettings)
    && Boolean(
      isPersonModerator
        || !(confSettings?.DISABLE_SCREEN_SHARING_FOR_GUEST === 1)
    );
    const { partId } = state['features/genericiframe'];
    const participants = state['features/base/participants'];
    const participantLocal
    = participants.filter(i => i.local === true)[0]?.id === partId;
    const partTrue
    = participants.filter(i => i.local === true)[0]?.id === participantID;
    const localId = participants.filter(i => i.local === true)[0];

    // in future this will be based on jwt.
    const isVoiceCommandDisabled = Boolean(
    interfaceConfig?.DISABLE_VOICE_COMMAND || !isPersonModerator
    );

    // NB: We compute the buttons again here because if URL parameters were used to
    // override them we'd miss it.
    const buttons = new Set(interfaceConfig.TOOLBAR_BUTTONS);
    const shareVideoState = [ 'playing', 'pause', 'start' ].includes(
    sharedVideoStatus
    );
    const tracks = state['features/base/tracks'];

    return {
        _chatOpen: state['features/chat'].isOpen,
        _clientWidth: clientWidth,
        _clicknPledgeVisible: clicknPledgeVisible,
        _conference: conference,
        _desktopSharingEnabled: desktopSharingEnabled,
        _desktopSharingDisabledTooltipKey: desktopSharingDisabledTooltipKey,
        _dialog: Boolean(state['features/base/dialog'].component),
        _donorBoxVisible: donorBoxVisible,
        _feedbackConfigured: Boolean(callStatsID),
        _isGuest: state['features/base/jwt'].isGuest,
        _requestingCaptionsModal,
        _isVpaasMeeting: isVpaasMeeting(state),
        _embedMeeting: embedMeeting,
        _fullScreen: fullScreen,
        _tileViewEnabled: shouldDisplayTileView(state),
        _localParticipantID: localParticipant.id,
        _localRecState: localRecordingStates,
        _locked: locked,
        _isLiveStreamRunning: Boolean(
      getActiveSession(state, MHRecordingConstants.mode.STREAM)
        ),
        _isRecordingRunning: Boolean(
      getActiveSession(state, MHRecordingConstants.mode.FILE)
        ),
        _overflowMenuVisible: overflowMenuVisible,
        _raisedHand: localParticipant.raisedHand,
        _screensharing: localVideo && localVideo.videoType === 'desktop',
        _ShareMenuVisibility: visibility,
        _geturl: getUrl,
        _addurl: addUrl,
        _addCUrl: addCUrl,
        _getCUrl: getCUrl,
        _isMobile: isMobileBrowser(),
        _isOpenP: isOpen,
        _isModerator: isPersonModerator,
        liveStreamVisible: liveStreamVisible1,
        _partId: participantLocal,
        confSettings,
        _videoMuted: isLocalTrackMuted(tracks, MEDIA_TYPE.VIDEO),
        RecordingVisible,
        _roomName: roomName,
        _participantsPaneOpen: getParticipantsPaneOpen(state),
        _sharingVideo:
      sharedVideoStatus === 'playing'
      || sharedVideoStatus === 'start'
      || sharedVideoStatus === 'pause',
        _visible: isToolboxVisible(state),
        _visibleButtons: equals(visibleButtons, buttons) ? visibleButtons : buttons,
        _sharingWhiteBoard: sharingWhiteBoard,
        _shareEtherpad: shareEtherpad,
        _shareVideoState: shareVideoState,
        _etherpadPart: partTrue,
        _isWhiteBoardID: partId,
        _isetherPadId: participantID,
        _whiteBoardvisible: whiteBoardvisible,
        _livePadVisible: livePadVisible,
        _localId: localId,
        _vcString: voiceCommandString,
        vcVisible: !isLocalCameraTrackMuted(state['features/base/tracks']),
        iframeVisible,
        onEdit,
        _screenShareVisible: screenShareVisible,
        _isVoiceCommandDisabled: isVoiceCommandDisabled,
        _isGifEnabled: isGifEnabled(state),
        _shouldDisplayReactionsButtons: shouldDisplayReactionsButtons(state),
        _reactionsEnabled: isReactionsEnabled(state)
    };
}

export default translate(connect(_mapStateToProps)(Toolbox));
