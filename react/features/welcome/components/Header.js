/* eslint-disable */
import { jitsiLocalStorage } from "@jitsi/js-utils";
import $ from "jquery";
import { sha256 } from "js-sha256";
import _ from "lodash";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import MediaQuery from "react-responsive";

import { Avatar } from "../../base/avatar";
import { apiKEY } from "../../base/config/constants";
import { JWT_STORAGE, SET_ACCESS_TOKEN } from "../../base/jwt/actionTypes";
import { fetchApi, setJWTforGuest, setuserDetails } from "../../base/mtApi";
import { Watermark } from "../../base/react";
import { getDisplayName, updateSettings } from "../../base/settings";
import {
  getBackendSafeRoomName,
  parseURLParams,
  sliceLengthyCharacters,
} from "../../base/util";
import { isDomainWeborNative } from "../../base/util/checkOS";
import "metismenujs/style";

import { styleWeb } from "./stylesWeb";
import {
  MobileMenuCloseIcon,
  MyCalyIcon,
  VideoIcon,
} from "../../../../static/assets/vendor/svgIcons/svgIcons";
import MetisMenu from "metismenujs";
import { useTranslation } from "react-i18next";
import LanguagesDropdown from "../../languages-component/components/LanguagesDropdown";

declare var config: object;

export const Header = (props) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [userd, setUserd] = useState(undefined);
  const [rmoveImg, setrmoveImg] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);
  const [isToggled, setToggled] = useState(false);
  const toggleTrueFalse = () => setToggled(!isToggled);

  const [loginButton, setLoginButton] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const { interfaceConfig } = useSelector(
    (state) => state["features/base/config"]
  );
  const state1 = useSelector((state) => state["features/base/jwt"]);
  const name = useSelector((state) => getDisplayName(state));
  const { userDetails, pregData } = useSelector(
    (state) => state["features/base/mtApi"]
  );

  const userAccessToken = jitsiLocalStorage.getItem("accessToken");
  const roomName = getBackendSafeRoomName(
    useSelector((state) => state["features/base/conference"].room)
  );
  const { locationURL } = useSelector(
    (state) => state["features/base/connection"]
  );
  const urlPathName = locationURL?.pathname?.length;

  // menu code start

  //  let mobilebrower =  isMobileBrowser();
  //  console.log(mobilebrower,"which browser");

  useEffect(() => {
    // Function to close the menu when clicking outside
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    // Attach the event listener when the menu is open
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      // Remove the event listener when the menu is closed
      document.removeEventListener("mousedown", handleClickOutside);
    }

    if (document.querySelector("#metismenu")) {
      new MetisMenu("#metismenu");
    }

    // Cleanup when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const testMouseOut = () => {
    setrmoveImg(false);
  };

  const testMouseOver = () => {
    setrmoveImg(true);
  };

  //   menu code end

  useEffect(() => {
    // console.log('userAccessToken ==>', userAccessToken, 'state1 ==>', state1.accessToken, 'userDetails ==>', userDetails);
    let accesTokn = state1.accessToken ?? userAccessToken;
    const urlTest = locationURL?.pathname?.length;
    if (urlTest <= 1) {
      document.body.style.overflowY = "hidden";
    }
    // if(urlTest >= 1){
    //     const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');

    //     if (ele) {

    //         setTimeout(() => {
    //             ele.style.opacity = '0';
    //             document.body.style.overflowY = 'scroll';
    //             setTimeout(() => {
    //                 ele.style.display = 'none';
    //             }, 1200);
    //         }, 500);
    //     }
    // }
    const urrr = parseURLParams(locationURL, true, "search");
    if (
      urrr?.logout &&
      urrr?.client_id &&
      urrr?.client_id === config?.MH_CLIENT_ID
    ) {
      dispatch(setuserDetails(undefined));
      accesTokn = undefined;
      jitsiLocalStorage.clear();
      const ele =
        document.getElementById("preloader") ??
        document.getElementById("preloader1");

      if (ele) {
        setTimeout(() => {
          ele.style.opacity = "0";
          document.body.style.overflowY = "scroll";
          setTimeout(() => {
            ele.style.display = "none";
          }, 2000);
        }, 1500);
      }
    }

    if (
      (_.isNil(userDetails) && pregData && accesTokn) ||
      Boolean(_.isNil(userDetails) && urlTest <= 1 && accesTokn)
    ) {
      const moement = moment().seconds(0).milliseconds(0).format("X");

      const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

      const preRegObject = JSON.stringify({
        client_id: config.MH_CLIENT_ID,
        credentials: mesh256,
        ...isDomainWeborNative,
        meeting_id: roomName,
      });

      fetchApi(
        "customer/user_details",
        "post",
        {
          "Content-Type": "application/json",
          Authorization: `Bearer ${state1.accessToken ?? userAccessToken}`,
        },
        preRegObject
      )
        .then((response) => {
          const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);
          // eslint-disable-next-line camelcase
          // const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1);
          const { jwt = "" } = response?.data ?? {};

          // Also Skipping the pre-registration page redirect for Recorder

          if (
            isTrueReg &&
            !state1.jwt &&
            !state1.jwtData &&
            _.isEmpty(jwt) &&
            !config?.iAmRecorder === true
          ) {
            return window.location.replace(
              `${
                config?.brandedPreRegistrationURL
                  ? config?.brandedPreRegistrationURL
                  : pregData.registration_url
              }`
            );
          }
          setUserd(response.data);
          const namess = response?.data?.name;
          const emailss = response?.data?.email;
          const pict = response?.data?.picture;

          dispatch(
            updateSettings({
              avatarURL: pict,
              displayName: namess,
              email: emailss,
            })
          );

          if (response.success) {
            setLoginButton(true);
            dispatch({
              type: SET_ACCESS_TOKEN,
              accessToken: state1.accessToken ?? userAccessToken,
            });
            const getJwt = response.data?.jwt;

            if (!_.isEmpty(getJwt) && !state1?.jwt) {
              dispatch({
                type: JWT_STORAGE,
                jwtData: getJwt,
              });
            }
            if (
              _.isEmpty(getJwt) &&
              !state1?.jwt &&
              !state1.jwtData &&
              urlTest >= 1 &&
              interfaceConfig?.applyMeetingSettings === true
            ) {
              dispatch(setJWTforGuest());
            }
          } else {
            jitsiLocalStorage.removeItem("accessToken");
          }
          const ele =
            document.getElementById("preloader") ??
            document.getElementById("preloader1");

          if (ele) {
            setTimeout(() => {
              ele.style.opacity = "0";
              document.body.style.overflowY = "scroll";
              setTimeout(() => {
                ele.style.display = "none";
              }, 2000);
            }, 1500);
          }

          dispatch(setuserDetails(response.data));
        })
        .catch((e) => {
          console.log("errorResponse1234", e);
          const ele =
            document.getElementById("preloader") ??
            document.getElementById("preloader1");
          if (ele) {
            setTimeout(() => {
              ele.style.opacity = "0";
              document.body.style.overflowY = "scroll";
              setTimeout(() => {
                ele.style.display = "none";
              }, 2000);
            }, 1500);
          }
        });
    }

    const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);

    // // const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1);

    // Also Skipping the pre-registration page redirect for Recorder
    if (
      isTrueReg &&
      !state1.jwt &&
      !state1.jwtData &&
      !accesTokn &&
      !config?.iAmRecorder === true &&
      parseURLParams(locationURL)?.mt === undefined
    ) {
      return window.location.href(`${pregData.registration_url}`);
    }

    if (!accesTokn && loginButton === false) {
      const ele =
        document.getElementById("preloader") ??
        document.getElementById("preloader1");

      if (ele) {
        setTimeout(() => {
          ele.style.opacity = "0";
          document.body.style.overflowY = "scroll";
          setTimeout(() => {
            ele.style.display = "none";
          }, 2500);
        }, 2000);
      }
    }

    if (userDetails) {
      setLoginButton(true);
    }
  }, [pregData?.success]);

  const loginButtonJSx = (
    <>
      <li className="signin-li">
        <a
          href={`${config.URL_PREFIX}serviceLogin?client_id=${
            config.MH_CLIENT_ID
          }&redirect_uri=${window.location.href}${
            roomName ? "%23interfaceConfig.ENABLE_DESKTOP_DEEPLINK%3Dfalse" : ""
          }&device_type=web&response_type=get${
            roomName ? `&meeting_id=${roomName}` : ""
          }`}
        >
          {t("prejoin.signinsignup")}
        </a>
      </li>
    </>
  );

  // useEffect(() => {
  //     setUserDP(false);

  //     if (userd && userd.picture && userd.picture.match(/\.(jpg|jpeg|gif|png)$/)) {
  //         setUserDP(true);
  //     }
  // }, [ userd ]);

  useEffect(() => {
    $(document).on("click", ".mobile-nav .drop-down > a", function (e) {
      e.preventDefault();
      // eslint-disable-next-line no-invalid-this
      $(this).next().slideToggle(300);
      // eslint-disable-next-line no-invalid-this
      $(this).parent().toggleClass("active");
    });
  }, []);

  /**
   * Event used to logout the user from the app.
   *
   * @param {event} e - Event object..
   * @private
   * @returns {void}
   */
  function logOutClicked(e) {
    e.preventDefault();
    const ele =
      document.getElementById("preloader") ??
      document.getElementById("preloader1");

    if (ele) {
      ele.style.opacity = "100%";
      ele.style.display = "block";
    }
    jitsiLocalStorage.clear();
    window.location.replace(
      `${config.URL_PREFIX}serviceLogin?client_id=${
        config.MH_CLIENT_ID
      }&redirect_uri=${
        window.location.href
      }&device_type=web&response_type=get&access_token=${
        state1.accessToken ?? userAccessToken
          ? `&access_token=${state1.userAccessToken ?? userAccessToken}`
          : ""
      }&logout=true`
    );
    dispatch(setuserDetails(undefined));
    dispatch(
      updateSettings({
        displayName: undefined,
        email: undefined,
      })
    );
  }
  let logoutDocument;

  if (loginButton) {
    logoutDocument = (
      <li className="drop-down d-flex">
        {(userd && userd?.picture) || (userDetails && userDetails.picture) ? (
          <img
            className="avatar"
            src={userd?.picture ?? userDetails?.picture}
            style={styleWeb.widthandHeight}
          />
        ) : (
          <Avatar
            className="premeeting-screen-avatar"
            displayName={name}
            dynamicColor={true}
            participantId="local"
            size={45}
          />
        )}

        <a>
          {sliceLengthyCharacters({
            char: userd?.name ?? userDetails.name ?? "",
            charLength: 25,
            shouldBeDotted: true,
          })}
        </a>
        <ul className="drop-down1">
          <li>
            <a
              href={`${config.URL_PREFIX}serviceLogin?client_id=${
                config.MH_CLIENT_ID
              }&redirect_uri=${
                config.URL_PREFIX
              }customer/dashboard&device_type=web${
                state1.accessToken ?? userAccessToken
                  ? `&access_token=${state1.userAccessToken ?? userAccessToken}`
                  : ""
              }`}
            >
              <i className="bx bxs-dashboard" /> {t("prejoin.dashboard")}
            </a>
          </li>
          <li>
            <a
              href={`${config.URL_PREFIX}serviceLogin?client_id=${
                config.MH_CLIENT_ID
              }&redirect_uri=${
                config.URL_PREFIX
              }customer/profile&device_type=web${
                state1.accessToken ?? userAccessToken
                  ? `&access_token=${state1.accessToken ?? userAccessToken}`
                  : ""
              }`}
            >
              <i className="bx bx-user" /> {t("prejoin.profile")}
            </a>{" "}
          </li>
          <li>
            <a onClick={logOutClicked} style={styleWeb.cusrorPointer}>
              <i className="bx bx-log-out" /> {t("toolbar.logout")}
            </a>
          </li>
        </ul>
      </li>
    );
  } else if (props.desktopDeepLink) {
    logoutDocument = "";
  } else {
    logoutDocument = loginButtonJSx;
  }

  const handleSubmenuToggle = (e) => {
    e.preventDefault();

    const parentLi = e.currentTarget.closest("li");

    if (parentLi) {
      parentLi.classList.toggle("mm-active");
      const submenu = parentLi.querySelector(".submenu");
      if (submenu) {
        submenu.classList.toggle("mm-collapse");
        submenu.classList.toggle("mm-show");
      }
    }
  };

  return (
    <React.Fragment>
      {interfaceConfig &&
        interfaceConfig.SHOW_MEET_HOUR_WATERMARK &&
        !interfaceConfig.disablePrejoinHeader &&
        (config?.MEGAMENU_HEADER === true ? (
          <header className={"fixed-top align-items-center"} id="header-nextjs">
            <div
              className="container d-flex align-items-center"
              style={{ marginTop: "10px" }}
            >
              <div className="logo mr-auto width300">
                <Watermark />
                {config?.MEGAMENU_HEADER === true ? (
                  <div className="sidebar__menu d-flex justify-content-end d-lg-none">
                    <div
                      className="sidebar-toggle-btn sidebar-toggle-btn-5"
                      id="sidebar-toggle"
                      onClick={toggleTrueFalse}
                    >
                      <span className="line"></span>
                      <span className="line"></span>
                      <span className="line"></span>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {isToggled ? (
                  <div
                    className={`sidebar__area ${
                      !isToggled ? "" : "sidebar-opened"
                    }`}
                  >
                    <div className="sidebar__wrapper">
                      <div className="sidebar__close">
                        <button
                          className="sidebar__close-btn"
                          id="sidebar__close-btn"
                          onClick={toggleTrueFalse}
                        >
                          <span>
                            <MobileMenuCloseIcon />
                          </span>
                          <span>{t("chromeExtensionBanner:close")}</span>
                        </button>
                      </div>
                      <div className="sidebar__content">
                        <div className="logo mb-40">

                          <div className="d-flex">
                          <a href="/">
                            <img
                              alt="logo"
                              height={30}
                              src="/img/logo/logo.png"
                              width={160}
                            />
                          </a>

                          <LanguagesDropdown isMobileView={true} />
                          </div>
                        </div>
                        <div className={"mobile-menu mean-container"}>
                          <nav className="mean-nav">
                            <ul className="metismenu text-muted" id="metismenu">


                            <li>
                                {loginButton === true ? (
                                  <>
                                    <li className="drop-down d-flex">
                                      {userd?.picture || userDetails?.picture ? (
                                        <img
                                          className="avatar"
                                          src={userd?.picture ?? userDetails?.picture}
                                          style={{ width: "45px", height: "42px" }}
                                          alt="User Avatar"
                                        />
                                      ) : (
                                        <Avatar
                                          className="premeeting-screen-avatar"
                                          displayName={name}
                                          dynamicColor={true}
                                          participantId="local"
                                          size={45}
                                        />
                                      )}

                                      <a onClick={() => setShowDropdown(!showDropdown)}>
                                        {sliceLengthyCharacters({
                                          char: userd?.name ?? userDetails?.name ?? "",
                                          charLength: 25,
                                          shouldBeDotted: true,
                                        })}
                                        
                                      </a>

                                      <ul className={`drop-down1 ${showDropdown ? "show" : "hide"}`}>
                                        <li>

                                          <a
                                            href={`${config.URL_PREFIX}serviceLogin?client_id=${
                                              config.MH_CLIENT_ID
                                            }&redirect_uri=${config.URL_PREFIX}customer/dashboard&device_type=web${
                                              state1.accessToken ?? userAccessToken
                                                ? `&access_token=${state1.accessToken ?? userAccessToken}`
                                                : ""
                                            }`}
                                            
                                          >
                                            <i className="bx bxs-dashboard"></i> {t("prejoin.dashboard")}
                                          </a>
                                        </li>

                                        <li>
                                          <a
                                            href={`${config.URL_PREFIX}serviceLogin?client_id=${
                                              config.MH_CLIENT_ID
                                            }&redirect_uri=${config.URL_PREFIX}customer/profile&device_type=web${
                                              state1.accessToken ?? userAccessToken
                                                ? `&access_token=${state1.accessToken ?? userAccessToken}`
                                                : ""
                                            }`}
                                          >
                                            <i className="bx bx-user"></i> {t("prejoin.profile")}
                                          </a>
                                        </li>

                                        <li>
                                          <a style={{ cursor: "pointer" }} onClick={logOutClicked}>
                                            <i className="bx bx-log-out"></i> {t("toolbar.logout")}
                                          </a>
                                        </li>
                                      </ul>
                                    </li>
                                  </>
                                ) : (
                                  <a
                                    href={`${
                                      config.URL_PREFIX
                                    }serviceLogin?client_id=${
                                      config.MH_CLIENT_ID
                                    }&redirect_uri=${
                                      window.location.href
                                    }%23interfaceConfig.ENABLE_DESKTOP_DEEPLINK%3Dfalse&device_type=web&response_type=get${
                                      roomName ? `&meeting_id=${roomName}` : ""
                                    }`}
                                  >
                                    {t("prejoin.signinsignup")}
                                  </a>
                                )}
                              </li>

                              <li>
                                <a href="/">{t("welcomepage.footer.home")}</a>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="/products"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.products")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      href="products/video-conference"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.videoConference")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/products/video-conference/free">
                                          {t("welcomepage.header.free", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/pro">
                                          {t("welcomepage.header.pro", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/developer">
                                          {t("welcomepage.header.developer", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/enterprise">
                                          {t("welcomepage.header.enterprise", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/enterprise-selfhost">
                                          {t(
                                            "welcomepage.header.enterpriseSelfHost",
                                            { lng: "en" }
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/features">
                                          {t("welcomepage.header.features")}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>

                                  <li className="ml-20">
                                    <a
                                      href="https://mycaly.io/"
                                      target="_blank"
                                    >
                                      {t("welcomepage.header.myCaly", {
                                        lng: "en",
                                      })}
                                    </a>{" "}
                                  </li>
                                </ul>
                              </li>
                              <li>
                                <a
                                  className="has-arrow"
                                  href="/solutions"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.solutions")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      href="/solutions/usecases"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.useCases")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/solutions/usecases/videoconferencing">
                                          {t(
                                            "welcomepage.header.videoConferencing"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/livestreaming">
                                          {t(
                                            "welcomepage.header.liveStreaming"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/virtualclassrooms">
                                          {t(
                                            "welcomepage.header.liveStreaming"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/virtualevents">
                                          {t(
                                            "welcomepage.header.virtualEvents"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/ekyc">
                                          {t("welcomepage.header.videoKYC")}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/webinars">
                                          {t("welcomepage.header.webinars")}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/fundraising">
                                          {t(
                                            "welcomepage.header.fundraisingDonateOnline"
                                          )}
                                        </a>
                                      </li>{" "}
                                    </ul>{" "}
                                  </li>

                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      href="/solutions/industries"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.industries")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/solutions/industries/edtech">
                                          {t("welcomepage.header.edTech")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a href="/solutions/industries/fitness">
                                          {t("welcomepage.header.fitness")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a href="/solutions/industries/healthcare">
                                          {t("welcomepage.header.telehealth")}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>
                                </ul>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="/developers"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.developers")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.getStarted")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/developers/sdks">
                                          {t("welcomepage.header.preBuiltSDKs")}
                                        </a>
                                      </li>
                                      <li>
                                        <a
                                          href="https://docs.v-empower.com/docs/MeetHour-API"
                                          target="_blank"
                                        >
                                          {t(
                                            "welcomepage.header.documentation"
                                          )}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>

                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.resources")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/blog">
                                          {t("welcomepage.header.blog")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a href="/developers/resources/api-status">
                                          {t("welcomepage.header.apiStatus")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a
                                          href="http://kb.shell.groupvi.systems/category/how-to"
                                          target="_blank"
                                        >
                                          {t(
                                            "welcomepage.header.knowledgeBase"
                                          )}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>
                                </ul>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="/products/video-conference/pricing"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.pricing")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li>
                                    <a href="/products/video-conference/pricing">
                                      {t("welcomepage.header.videoConference")}
                                    </a>{" "}
                                  </li>
                                  <li>
                                    <a
                                      href="https://mycaly.io/pricing"
                                      target="_blank"
                                    >
                                      {t("welcomepage.header.myCaly", {
                                        lng: "en",
                                      })}
                                    </a>{" "}
                                  </li>
                                </ul>
                              </li>
                              <li className="ml-20">
                                <a>
                                  <LanguagesDropdown isMobileView={true} textColor="#333666" />
                                </a>
                              </li>

                              <li>
                                <a href="/joinmeeting">
                                  {t("welcomepage.header.joinAMeeting")}
                                </a>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="http://kb.shell.groupvi.systems/category/how-to"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.help")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li>
                                    <a href="mailto:<EMAIL>">
                                      {" "}
                                      <i className="icon_mail"></i>{" "}
                                      {t("welcomepage.header.eMail")}
                                    </a>{" "}
                                  </li>
                                  <li>
                                    <a
                                      href="https://meethour.tawk.help/"
                                      target="_blank"
                                    >
                                      {" "}
                                      <i className="icon_question_alt2"></i>{" "}
                                      {t("welcomepage.header.helpDesk")}{" "}
                                    </a>
                                  </li>

                                  <li>
                                    <a href="tel:+124-547-689">
                                      {" "}
                                      <i className="icon_phone"></i> +1 (312)
                                      800-9410
                                    </a>
                                  </li>
                                  <li>
                                    <a href="/schedule-a-demo">
                                      {" "}
                                      <i className="icon_info"></i>{" "}
                                      {t("welcomepage.header.scheduleADemo")}
                                    </a>
                                  </li>
                                </ul>
                              </li>

                            </ul>
                          </nav>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <></>
                )}
              </div>

              {interfaceConfig && !interfaceConfig.disablePrejoinHeader && (
                <>
                  {/* <MediaQuery maxWidth = { 1000 }>
                                    <button
                                        className = 'mobile-nav-toggle d-lg-none'
                                        onClick = { openNavbar }
                                        type = 'button'><i className = 'icofont-navigation-menu' /></button>
                                </MediaQuery> */}
                  <MediaQuery minWidth={1000}>
                    <nav
                      className="nav-menu d-none d-lg-flex desktop-width navbar navbar-expand-lg navbar-light nav-menu-nextjs"
                      //style={styleWeb.width100}
                    >
                      <div className="container-fluid">
                        <button
                          aria-controls="navbarExampleOnHover"
                          aria-expanded="false"
                          aria-label="Toggle navigation"
                          className="navbar-toggler px-0"
                          data-mdb-target="#navbarExampleOnHover"
                          data-mdb-toggle="collapse"
                          type="button"
                        >
                          <i className="fas fa-bars"></i>
                        </button>

                        <div
                          className="collapse navbar-collapse justify-content-between"
                          id="navbarExampleOnHover"
                        >
                          <ul className="navbar-nav ps-lg-0">
   
                          </ul>

                          <ul className="float-right-desktop d-flex">
                            <li
                              style={styleWeb.languageContainer}>
                              <LanguagesDropdown textColor="#333666" />
                            </li>
                            <li className='d-flex align-items-center joinM' style={styleWeb.headerliRight}>
                              <a href="/joinmeeting"> {t('welcomepage.header.joinAMeeting')}</a>
                            </li>
                            {logoutDocument}
                          </ul>
                        </div>
                      </div>

                      {/* <ul className="float-right-desktop">
                                                    <li
                                                        style={
                                                            styleWeb.headerliRight
                                                        }
                                                    >
                                                        <a href="/joinmeeting">
                                                            Join a Meeting
                                                        </a>
                                                    </li>
                                                    {logoutDocument}
                                                </ul> */}
                    </nav>
                  </MediaQuery>
                </>
              )}
            </div>
          </header>
        ) : (
          <header className={"fixed-top d-flex align-items-center"} id="header">
            <div className="container d-flex align-items-center">
              <div className="logo mr-auto" style={styleWeb.width300}>
                <Watermark />
              </div>

              {interfaceConfig && !interfaceConfig.disablePrejoinHeader && (
                <>
                  {/* <MediaQuery maxWidth = { 1000 }>
                                    <button
                                        className = 'mobile-nav-toggle d-lg-none'
                                        onClick = { openNavbar }
                                        type = 'button'><i className = 'icofont-navigation-menu' /></button>
                                </MediaQuery> */}
                  <MediaQuery minWidth={1000}>
                    <nav
                      className="nav-menu d-flex desktop-width"
                      //style={styleWeb.width100}
                    >
                      <ul className="float-right-desktop"></ul>
                      <li
                        style={styleWeb.languageContainer}>
                        <LanguagesDropdown textColor="#333666" />
                      </li>
                      <li className='d-flex align-items-center joinM' style={styleWeb.headerliRight}>
                        <a href="/joinmeeting">{t('welcomepage.header.joinAMeeting')}</a>
                      </li>
                      {logoutDocument}
                    </nav>
                  </MediaQuery>
                </>
              )}
            </div>
          </header>
        ))}
    </React.Fragment>
  );
};
