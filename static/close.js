/* global interfaceConfig */

// list of tips
const hints = [
    'You can pin participants by clicking on their thumbnails.',
    'You can tell others you have something to say by using the "Raise Hand" '
        + 'feature',
    'You can learn about key shortcuts by pressing Shift+?',
    'You can learn more about the state of everyone\'s connection by hovering '
        + 'on the bars in their thumbnail',
    'You can hide all thumbnails by using the button in the bottom right corner',
    'You can now Record and Live Stream your conference on YouTube with just a single click'
];

/**
 * Get a random hint message from hint array.
 *
 * @returns {string} The hint message.
 */
function getHint() {
    const l = hints.length - 1;
    const n = Math.round(Math.random() * l);

    return hints[n];
}

/**
 * Meet Hour.
 *
 * @param {any} str
 * @returns {any}
 */
function isJsonString(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }

    return true;
}


/** .........
 * Inserts text message
 * into DOM element
 *
 * @param id {string} element identificator
 * @param msg {string} text message
 */
function insertTextMsg(id, msg) {
    const el = document.getElementById(id);

    if (el) {
        el.innerHTML = msg;
    }
}

/**
 * Sets the hint and thanks messages. Will be executed on load event.
 */
function onLoad() {
    // Intentionally use string concatenation as this file does not go through
    // babel but IE11 is still supported.
    // eslint-disable-next-line prefer-template
    const thankYouMessage = 'Thank you for using ' + interfaceConfig.APP_NAME;

    // Works only for close2.html because close.html doesn't have this element.
    insertTextMsg('thanksMessage', thankYouMessage);

    const banners = [
        'https://repo.shell.groupvi.systems/banners/close_banner_pay_as_you_go.jpg',
        'https://repo.shell.groupvi.systems/banners/close_banner5.jpg',
        'https://repo.shell.groupvi.systems/banners/close_banner6.jpg',
        'https://repo.shell.groupvi.systems/banners/close_banner3.jpg',
        'https://repo.shell.groupvi.systems/banners/close_banner4.jpg'
    ];


    /**
     * Gets a random banner link.
     */
    function getBanner() {
        const l = banners.length - 1;
        const n = Math.round(Math.random() * l);

        return banners[n];
    }
    const getBannerLink = getBanner();

    if (getBannerLink) {
        const recordImg = document.querySelector('.recording-offer');

        if (recordImg) {
            recordImg.setAttribute('src', getBannerLink);
        }
    }

    // If there is a setting show a special message only for the guests
    if (interfaceConfig.CLOSE_PAGE_GUEST_HINT) {
        if (window.sessionStorage.getItem('guest') === 'true') {
            const element = document.getElementById('hintQuestion');

            element.classList.add('hide');
            insertTextMsg('hintMessage', interfaceConfig.CLOSE_PAGE_GUEST_HINT);

            return;
        }
    }

    insertTextMsg('hintMessage', getHint());


}

window.onload = onLoad;


let roomName;
let url = window.location.href;

url = new URL(url).hash;

if (url) {
    url = url.split('=')[1];
    if (url.includes('&')) {
        url = url.split('&')[0];
    }
    roomName = url;

}

const getListnener = document.getElementById('rejoinID');

if (getListnener) {
    getListnener.addEventListener('click', functUser);
}

/** .
     * RoomName to join back.
     *
     */
function functUser() {
    try {
        const pcode = localStorage.getItem('autopcode');
        const jsonString = isJsonString(pcode) ? JSON.parse(pcode) : null;
        const getDesktopHash = new URL(window.location.href).hash;
        const isIncluded = getDesktopHash.includes('desktopapp=1');

        if (roomName) {
            if (isIncluded) {
                window.open(`meet.groupvi.systems://shell.groupvi.systems/${roomName?.toUpperCase()}${jsonString ? `?pcode=${jsonString[`${roomName}`]}` : ''}`);

                return;
            }
            window.location.replace(`/${roomName?.toUpperCase()}${jsonString ? `?pcode=${jsonString[`${roomName}`]}` : ''}`);
        }
    } catch (error) {
        // const pcode = localStorage.getItem('autopcode');
        // const jsonString = isJsonString(pcode) ? JSON.parse(pcode) : null;
        const getDesktopHash = new URL(window.location.href).hash;
        const isIncluded = getDesktopHash.includes('desktopapp=1');

        if (roomName) {
            if (isIncluded) {
                window.open(`meet.groupvi.systems://shell.groupvi.systems/${roomName?.toUpperCase()}`);

                return;
            }
            window.location.replace(`/${roomName?.toUpperCase()}`);
        }

    }
}

// eslint-disable-next-line no-unused-vars, require-jsdoc
function defunc() {
    const getDesktopHash = new URL(window.location.href).hash;
    const isIncluded = getDesktopHash.includes('desktopapp=1');

    if (isIncluded) {
        window.open('meet.groupvi.systems://');

        return;
    }
}
