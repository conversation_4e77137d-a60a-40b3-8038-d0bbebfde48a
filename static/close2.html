
<html>
    <head>
        <meta charset="utf-8">	
        <meta http-equiv="content-type" content="text/html;charset=utf-8">	
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="/conf/css/all.css"/>
        <title>Meet Hour Video Conference App | Shifting Virtual Meet to Reality</title> 
        <meta property="og:title" content="Meet Hour Video Conference App | Shifting Virtual Meet to Reality" />
        <meta property="og:image" content="https://shell.groupvi.systems/images/logo.png?v=1"/>
        <meta property="og:image" content="https://repo.shell.groupvi.systems/banners/close_banner5.jpg"/>
        <meta property="og:image" content="https://repo.shell.groupvi.systems/banners/close_banner6.jpg"/>
        <script id="interfaceConfig" src="https://meet.groupvi.systems/libs/v2.4.6/interface_config.js?apiKey=b709af18554cee13bd74d8ab20aad74a4c6d46bd520ede88b54144b40e4ff3a8" ></script>    
        <script src="/static/close.js"></script>
    </head>
    <body class="welcome without-content">
        <div class="welcome-watermark"><div><a href="#" onclick="defunc(); return false;"><div class="watermark leftwatermark" style="background-image: url(&quot;/images/watermark.png&quot;);"></div></a></div></div>
        <div class="redirectPageMessage">
            <div class="thanks-msg">
                <p id="thanksMessage">Thank you for using Meet Hour</p>
            <a href="https://shell.groupvi.systems/products/video-conference/" target="_blank"><img class="recording-offer" alt="Offer" src="https://repo.shell.groupvi.systems/banners/close_banner3.jpg"></a>
                        <div>
                            <button class="btn btn-primary start-btn" style="width: 167px;margin: 20px 0px;cursor: pointer;" id = "rejoinID"  type="button" onclick="functUser()"><h4>JOIN BACK</h4></button>
                        </div>
            </div>
            <div class="hint-msg">
                <p>
                    <span id="hintQuestion">Did you know?</span>
                    <span class="hint-msg__holder" id="hintMessage"></span>
                </p>
                <div class="happy-software"></div>
            </div>
        </div>
        <style>
                    .welcome {
        background-image: url(../images/conference.jpeg)!important;}
            .redirectPageMessage {
                margin: 10% auto !important;
            }
            img.recording-offer {
                width: 70%;
            }
            .redirectPageMessage{
                position: relative;
            }
            @media (max-width: 600px) {
                img.recording-offer {
                    display: none;
                }
                .welcome-watermark {
                    display: none;
                }
            }
            .leftwatermark {
                left: 5% !important;
            }
        body.welcome.without-content {
              opacity: 1;
          }
        </style>
    <script>
    const getDesktopHash = new URL(window.location.href).hash
    const isIncluded = getDesktopHash.includes('desktopapp=1')
    function defunc() {
            
        if(isIncluded){
                window.open('meet.groupvi.systems://meet.groupvi.systems/customer/dashboard?desktopapp=1')
                return;
            }
    }

    if(isIncluded){
            const query = document.querySelector('.welcome-watermark');
              query.innerHTML = `${query.innerHTML} <a href="#" onclick="defunc(); return false;"><div class="watermark rightwatermark" style="background-image: url(&quot;/images/x-icon.png&quot;);"></div></a>`
    }

    </script>
    </body>
    </html>
    